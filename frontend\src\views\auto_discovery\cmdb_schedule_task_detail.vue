<template>
  <div class="app-container">
    <div class="page-header">
      <div class="header-title">
        <el-button @click="goBack" link size="small">
          <el-icon><Back /></el-icon> 返回列表
        </el-button>
        <h2>调度任务详情</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon> 编辑
        </el-button>
        <el-button type="success" @click="handleRun">
          <el-icon><VideoPlay /></el-icon> 执行
        </el-button>
        <el-button type="warning" @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <div>
            <el-tag v-if="taskDetail.status === 'active'" type="success">激活</el-tag>
            <el-tag v-else-if="taskDetail.status === 'inactive'" type="info">停用</el-tag>
            <el-tag v-else-if="taskDetail.status === 'paused'" type="warning">暂停</el-tag>
          </div>
        </div>
      </template>
      <div class="detail-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ taskDetail.id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.task_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="调度类型">
            <el-tag v-if="taskDetail.schedule_type === 'manual'" type="info">手动</el-tag>
            <el-tag v-else-if="taskDetail.schedule_type === 'once'" type="success">一次性</el-tag>
            <el-tag v-else-if="taskDetail.schedule_type === 'daily'" type="primary">每日</el-tag>
            <el-tag v-else-if="taskDetail.schedule_type === 'weekly'" type="warning">每周</el-tag>
            <el-tag v-else type="info">{{ taskDetail.schedule_type }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="调度值">{{ taskDetail.schedule_value || '-' }}</el-descriptions-item>
          <el-descriptions-item label="下次执行时间">
            <span v-if="taskDetail.next_run_time">{{ formatDateTime(taskDetail.next_run_time) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="上次执行时间">
            <span v-if="taskDetail.last_run_time">{{ formatDateTime(taskDetail.last_run_time) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            <span v-if="taskDetail.created_at">{{ formatDateTime(taskDetail.created_at) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ taskDetail.created_by || '-' }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">
            <span v-if="taskDetail.updated_at">{{ formatDateTime(taskDetail.updated_at) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="更新人">{{ taskDetail.updated_by || '-' }}</el-descriptions-item>
          <el-descriptions-item :span="2" label="任务描述">
            <div class="description-content">{{ taskDetail.description || '-' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>关联的发现任务</span>
          <span class="task-count">共 {{ taskDetail.items?.length || 0 }} 个任务</span>
        </div>
      </template>
      <el-table
        :data="taskDetail.items || []"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#f8f8f9', color: '#606266' }"
      >
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="discovery_task_id" label="任务ID" width="100" align="center" />
        <el-table-column prop="task_name" label="任务名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="task_type" label="任务类型" width="120" align="center" />
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.discovery_status === 'running'" type="warning">运行中</el-tag>
            <el-tag v-else-if="scope.row.discovery_status === 'completed'" type="success">完成</el-tag>
            <el-tag v-else-if="scope.row.discovery_status === 'failed'" type="danger">失败</el-tag>
            <el-tag v-else-if="scope.row.discovery_status === 'stopped'" type="info">已停止</el-tag>
            <el-tag v-else-if="scope.row.discovery_status === 'queued'" type="info">排队中</el-tag>
            <el-tag v-else type="info">{{ scope.row.discovery_status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDiscoveryTask(scope.row.discovery_task_id)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!taskDetail.items || taskDetail.items.length === 0" description="暂无关联的发现任务" />
    </el-card>

    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>执行历史</span>
          <span class="history-count">共 {{ taskDetail.history?.length || 0 }} 条记录</span>
        </div>
      </template>
      <el-table
        :data="taskDetail.history || []"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#f8f8f9', color: '#606266' }"
      >
        <el-table-column prop="id" label="历史ID" width="80" align="center" />
        <el-table-column label="开始时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.start_time">{{ formatDateTime(scope.row.start_time) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.end_time">{{ formatDateTime(scope.row.end_time) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.start_time && scope.row.end_time">
              {{ calculateDuration(scope.row.start_time, scope.row.end_time) }}
            </span>
            <span v-else-if="scope.row.start_time && !scope.row.end_time">运行中</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'started'" type="warning">运行中</el-tag>
            <el-tag v-else-if="scope.row.status === 'completed'" type="success">完成</el-tag>
            <el-tag v-else-if="scope.row.status === 'failed'" type="danger">失败</el-tag>
            <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="任务统计" width="200" align="center">
          <template #default="scope">
            <div class="task-stats">
              <span class="stat-item">
                <span class="stat-label">总数:</span>
                <span class="stat-value">{{ scope.row.total_tasks || 0 }}</span>
              </span>
              <span class="stat-item">
                <span class="stat-label">成功:</span>
                <span class="stat-value success">{{ scope.row.completed_tasks || 0 }}</span>
              </span>
              <span class="stat-item">
                <span class="stat-label">失败:</span>
                <span class="stat-value error">{{ scope.row.failed_tasks || 0 }}</span>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewHistoryDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!taskDetail.history || taskDetail.history.length === 0" description="暂无执行历史" />
    </el-card>

    <!-- 历史详情对话框 -->
    <el-dialog
      v-model="dialogVisible.historyDetail"
      title="执行历史详情"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-if="selectedHistory">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="历史ID">{{ selectedHistory.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="selectedHistory.status === 'started'" type="warning">运行中</el-tag>
            <el-tag v-else-if="selectedHistory.status === 'completed'" type="success">完成</el-tag>
            <el-tag v-else-if="selectedHistory.status === 'failed'" type="danger">失败</el-tag>
            <el-tag v-else type="info">{{ selectedHistory.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(selectedHistory.start_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ selectedHistory.end_time ? formatDateTime(selectedHistory.end_time) : '运行中' }}
          </el-descriptions-item>
          <el-descriptions-item label="总任务数">{{ selectedHistory.total_tasks || 0 }}</el-descriptions-item>
          <el-descriptions-item label="成功/失败">
            <span class="stat-value success">{{ selectedHistory.completed_tasks || 0 }}</span> /
            <span class="stat-value error">{{ selectedHistory.failed_tasks || 0 }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="history-result" v-if="selectedHistory.result">
          <div class="result-title">执行结果:</div>
          <el-scrollbar height="300px">
            <pre class="result-content">{{ selectedHistory.result }}</pre>
          </el-scrollbar>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible.edit"
      title="编辑调度任务"
      width="800px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <schedule-task-form
        ref="scheduleTaskFormRef"
        :form-data="formData"
        mode="edit"
        @submit="handleFormSubmit"
        @cancel="dialogVisible.edit = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back, Edit, VideoPlay, RefreshRight } from '@element-plus/icons-vue';
import { getScheduleTaskDetail, runScheduleTask } from '../../api/schedule-task';
import ScheduleTaskForm from '../../components/schedule-task-form.vue';

export default {
  name: 'CmdbScheduleTaskDetail',
  components: {
    Back,
    Edit,
    VideoPlay,
    RefreshRight,
    ScheduleTaskForm
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(false);
    const taskDetail = ref({});
    const selectedHistory = ref(null);
    const formData = ref({});
    const scheduleTaskFormRef = ref(null);

    // 对话框状态
    const dialogVisible = reactive({
      historyDetail: false,
      edit: false
    });

    // 获取调度任务详情
    const getTaskDetail = async () => {
      const id = route.query.id;
      if (!id) {
        ElMessage.error('缺少任务ID参数');
        return;
      }

      loading.value = true;
      try {
        const response = await getScheduleTaskDetail({ id });
        if (response.code === 0) {
          taskDetail.value = response.msg || {};
        } else {
          ElMessage.error(response.msg || '获取调度任务详情失败');
        }
      } catch (error) {
        console.error('获取调度任务详情失败:', error);
        ElMessage.error('获取调度任务详情失败');
      } finally {
        loading.value = false;
      }
    };

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '-';
      const date = new Date(dateTime);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');
    };

    // 计算持续时间
    const calculateDuration = (startTime, endTime) => {
      if (!startTime || !endTime) return '-';

      const start = new Date(startTime).getTime();
      const end = new Date(endTime).getTime();
      const diff = Math.abs(end - start) / 1000; // 秒

      if (diff < 60) {
        return `${Math.round(diff)}秒`;
      } else if (diff < 3600) {
        const minutes = Math.floor(diff / 60);
        const seconds = Math.round(diff % 60);
        return `${minutes}分${seconds}秒`;
      } else {
        const hours = Math.floor(diff / 3600);
        const minutes = Math.floor((diff % 3600) / 60);
        const seconds = Math.round(diff % 60);
        return `${hours}小时${minutes}分${seconds}秒`;
      }
    };

    // 返回列表
    const goBack = () => {
      router.push('/cmdb_schedule_tasks');
    };

    // 刷新
    const handleRefresh = () => {
      getTaskDetail();
    };

    // 编辑
    const handleEdit = () => {
      formData.value = { ...taskDetail.value };
      dialogVisible.edit = true;
    };

    // 表单提交
    const handleFormSubmit = () => {
      dialogVisible.edit = false;
      getTaskDetail();
    };

    // 执行
    const handleRun = async () => {
      try {
        const response = await runScheduleTask({ id: taskDetail.value.id });
        if (response.code === 0) {
          ElMessage.success('任务已开始执行');
          setTimeout(() => {
            getTaskDetail();
          }, 1000);
        } else {
          ElMessage.error(response.msg || '执行失败');
        }
      } catch (error) {
        console.error('执行调度任务失败:', error);
        ElMessage.error('执行调度任务失败');
      }
    };

    // 查看发现任务
    const viewDiscoveryTask = (taskId) => {
      window.open(`#/cmdb_discovery_results?task_id=${taskId}`, '_blank');
    };

    // 查看历史详情
    const viewHistoryDetail = (history) => {
      selectedHistory.value = history;
      dialogVisible.historyDetail = true;
    };

    onMounted(() => {
      getTaskDetail();
    });

    return {
      loading,
      taskDetail,
      selectedHistory,
      dialogVisible,
      formData,
      scheduleTaskFormRef,
      formatDateTime,
      calculateDuration,
      goBack,
      handleRefresh,
      handleEdit,
      handleFormSubmit,
      handleRun,
      viewDiscoveryTask,
      viewHistoryDetail
    };
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  display: flex;
  align-items: center;
}

.header-title h2 {
  margin: 0 0 0 10px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-count, .history-count {
  font-size: 14px;
  color: #909399;
}

.description-content {
  white-space: pre-line;
  line-height: 1.5;
}

.task-stats {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-label {
  margin-right: 5px;
  color: #606266;
}

.stat-value {
  font-weight: bold;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}

.history-result {
  margin-top: 20px;
}

.result-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.result-content {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  line-height: 1.5;
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 10px;
  height: 10px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #dddee0;
  border-radius: 5px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background-color: #f6f6f6;
  border-radius: 5px;
}
</style>