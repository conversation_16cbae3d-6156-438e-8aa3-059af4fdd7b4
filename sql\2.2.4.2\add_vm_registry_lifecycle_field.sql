-- =====================================================
-- 版本: *******
-- 功能: 为 cmdb_vm_registry 表添加生命周期字段
-- 日期: 2025-08-01
-- 描述: 参考 cmdb_server_management 表的实现，为虚拟机登记表添加 operation_status 生命周期字段
-- =====================================================

-- 添加生命周期字段到虚拟机登记表
ALTER TABLE public.cmdb_vm_registry 
ADD COLUMN operation_status character varying(50);

-- 添加字段注释
COMMENT ON COLUMN public.cmdb_vm_registry.operation_status IS '生命周期
（系统管理员填写）';

-- 为现有记录设置默认值为"D00035"
UPDATE public.cmdb_vm_registry 
SET operation_status = 'D00035' 
WHERE operation_status IS NULL;

-- 设置字段为非空
ALTER TABLE public.cmdb_vm_registry 
ALTER COLUMN operation_status SET NOT NULL;

-- 更新虚拟机视图以包含生命周期字段
DROP VIEW IF EXISTS public.v_cmdb_vm_registry;

CREATE VIEW public.v_cmdb_vm_registry AS
 SELECT t.id,
    t.management_ip,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    t.host_ip,
    COALESCE(t2.dict_name, t.operating_system) AS operating_system,
    COALESCE(t3.dict_name, t.data_center1) AS data_center1,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    t.admin,
    t.app_system_id,
    t.virtual_host_ip,
    t.data_center2,
    t.is_monitored,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            WHEN (t.monitoring_requirement = false) THEN '否'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.weak_password_exists,
    to_char((t.weak_password_correction_date)::timestamp with time zone, 'yyyy-mm-dd'::text) AS weak_password_correction_date,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((public.cmdb_vm_registry t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.operating_system)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.data_center1)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.operation_status)::text))))
     LEFT JOIN public.cmdb_application_system_info t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.management_ip)::text = (t.management_ip)::text))))
     LEFT JOIN ( SELECT t9_1.ip_address
           FROM public.cmdb_monitored_ip_list t9_1
          WHERE ((t9_1.del_flag)::text = '0'::text)
          GROUP BY t9_1.ip_address) t9 ON (((t9.ip_address)::text = (t.management_ip)::text)))
  WHERE ((t.del_flag)::text = '0'::text);

ALTER VIEW public.v_cmdb_vm_registry OWNER TO postgres;

COMMENT ON VIEW public.v_cmdb_vm_registry IS '虚拟机视图：包含监控需求字段和生命周期字段，布尔值转换为中文显示';

-- 授予权限
GRANT SELECT ON TABLE public.v_cmdb_vm_registry TO cjmonitor;

-- 输出成功信息
DO $$
BEGIN
    RAISE NOTICE '虚拟机登记表生命周期字段添加完成！';
    RAISE NOTICE '版本: *******';
    RAISE NOTICE '添加字段: operation_status (生命周期)';
    RAISE NOTICE '添加数据字典项: 生命周期状态选项';
    RAISE NOTICE '更新视图: v_cmdb_vm_registry 包含生命周期字段';
END $$;


-- 重建视图
DROP VIEW IF EXISTS public.v_cmdb_application_system_info CASCADE ;
CREATE VIEW public.v_cmdb_application_system_info AS
 SELECT t.id,
    t.management_ip,
    COALESCE(t6.hostname, t5.hostname, ''::text) AS hostname,
    COALESCE(t6.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    COALESCE(t6.admin1, t5.admin1, ''::text) AS server_admin1,
    COALESCE(t6.admin2, t5.admin2, ''::text) AS server_admin2,
    COALESCE(t6.data_center, t5.data_center, ''::text) AS data_center,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS machine_usage_status,
    t.remarks,
    t.business_system_name,
    COALESCE(( SELECT u1.real_name
           FROM public.cmdb_users u1
          WHERE (((u1.username)::text = t8.main_admin) AND ((u1.del_flag)::text = '0'::text) AND (u1.real_name IS NOT NULL) AND ((u1.real_name)::text <> ''::text))), ( SELECT u2.real_name
           FROM public.cmdb_users u2
          WHERE (((u2.real_name)::text = t8.main_admin) AND ((u2.del_flag)::text = '0'::text) AND (u2.real_name IS NOT NULL) AND ((u2.real_name)::text <> ''::text))), (t8.main_admin)::character varying) AS system_administrator,
    t8.system_level AS system_classification,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            WHEN (t.monitoring_requirement = false) THEN '否'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (t9.ip_address IS NOT NULL) THEN '是'::text
            ELSE '否'::text
        END AS is_monitored,
    t.deployed_applications,
    COALESCE(t2.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t3.dict_name, t.master_slave_role) AS master_slave_role,
    COALESCE(t4.dict_name, t.backup_mode) AS backup_mode,
    t.internet_ip,
    t.internet_port,
    t.related_master_slave_ips,
    COALESCE(t6.operating_system, t5.operating_system, ''::text) AS operating_system,
    COALESCE(t.has_antivirus_software, '是'::character varying) AS has_antivirus_software,
    COALESCE(t.patch_update_configured, '是'::character varying) AS patch_update_configured,
        CASE
            WHEN (t8.system_level = '一级'::text) THEN '有一级系统管理员'::text
            WHEN (t8.system_level = '二级'::text) THEN '有二级系统管理员'::text
            ELSE ''::text
        END AS has_system_administrator,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((((public.cmdb_application_system_info t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.production_attributes)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.master_slave_role)::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.dict_code)::text = (t.backup_mode)::text))))
     LEFT JOIN ( SELECT t5_1.management_ip,
            max((t5_1.hostname)::text) AS hostname,
            max((t5_1.function_purpose)::text) AS function_purpose,
            max((t5_1.admin1)::text) AS admin1,
            max((t5_1.admin2)::text) AS admin2,
            max((t5_1.data_center)::text) AS data_center,
            max((t5_1.operating_system)::text) AS operating_system
           FROM public.v_cmdb_server_management t5_1
          GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t6_1.management_ip,
            max((t6_1.hostname)::text) AS hostname,
            max((t6_1.function_purpose)::text) AS function_purpose,
            max((t6_1.admin1)::text) AS admin1,
            max((t6_1.admin2)::text) AS admin2,
            max((t6_1.data_center1)::text) AS data_center,
            max((t6_1.operating_system)::text) AS operating_system
           FROM public.v_cmdb_vm_registry t6_1
          GROUP BY t6_1.management_ip) t6 ON (((t6.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t8_1.system_abbreviation,
            max((t8_1.main_admin)::text) AS main_admin,
            max((COALESCE(t82.dict_name, t8_1.system_level))::text) AS system_level
           FROM (public.cmdb_system_admin_responsibility_company t8_1
             LEFT JOIN public.cmdb_data_dictionary t82 ON ((((t82.del_flag)::text = '0'::text) AND ((t82.dict_code)::text = (t8_1.system_level)::text))))
          WHERE ((t8_1.del_flag)::text = '0'::text)
          GROUP BY t8_1.system_abbreviation) t8 ON (((t8.system_abbreviation)::text = (t.business_system_name)::text)))
     LEFT JOIN ( SELECT t9_1.ip_address
           FROM public.cmdb_monitored_ip_list t9_1
          WHERE ((t9_1.del_flag)::text = '0'::text)
          GROUP BY t9_1.ip_address) t9 ON (((t9.ip_address)::text = (t.management_ip)::text)))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_application_system_info OWNER TO postgres;



CREATE VIEW public.v_cmdb_system_admin_responsibility_company AS
 SELECT t.id,
    t.self_build_system_id,
    t.system_abbreviation,
    t.main_admin,
    t.backup_admin,
    t.business_department,
    COALESCE(t7.dict_name, t.system_attribute) AS system_attribute,
    t.go_live_date,
    t.decommission_date,
    t.major_milestones,
    t.industry_name,
    t.monitoring_system_name,
    t.system_function_summary,
    t.system_form,
    t.cs_client_name,
    t.bs_url,
    t.ip_port,
    t.business_line,
    t.system_category,
    COALESCE(t2.dict_name, t.system_level) AS system_level,
    t.has_backup_strategy,
    t4.server_count,
    t.remarks,
        CASE
            WHEN (t4.server_count = 0) THEN '0.00%'::text
            ELSE (round((((t4.is_monitored_qty)::numeric / (t4.server_count)::numeric) * (100)::numeric), 2) || '%'::text)
        END AS monitoring_coverage,
    t.digital_classification,
    t.jrt_0059_backup_standard,
    COALESCE(t5.dict_name, t.xinchuang_category_major) AS xinchuang_category_major,
    COALESCE(t3.dict_name, t.xinchuang_category_minor) AS xinchuang_category_minor,
    t.software_copyright_name,
    t.is_reported_to_external,
    COALESCE(t6.dict_name, t.construction_method) AS construction_method,
    COALESCE(t8.dict_name, t.technical_route) AS technical_route,
    t4.centos7_count,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by,
    t.operation_status,
    t.xinchuang_status,
    t.security_level,
    t.general_function_domains,
    t.futures_function_domains
   FROM (((((((public.cmdb_system_admin_responsibility_company t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.system_level)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.xinchuang_category_minor)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.xinchuang_category_major)::text))))
     LEFT JOIN public.cmdb_data_dictionary t6 ON ((((t6.del_flag)::text = '0'::text) AND ((t6.dict_code)::text = (t.construction_method)::text))))
     LEFT JOIN public.cmdb_data_dictionary t7 ON ((((t7.del_flag)::text = '0'::text) AND ((t7.dict_code)::text = (t.system_attribute)::text))))
     LEFT JOIN public.cmdb_data_dictionary t8 ON ((((t8.del_flag)::text = '0'::text) AND ((t8.dict_code)::text = (t.technical_route)::text))))
     LEFT JOIN ( SELECT t4_1.business_system_name,
            count(1) AS server_count,
            sum(
                CASE
                    WHEN (t4_1.is_monitored = '是'::text) THEN 1
                    ELSE 0
                END) AS is_monitored_qty,
            sum(
                CASE
                    WHEN (t4_1.operating_system = 'CentOS7'::text) THEN 1
                    ELSE 0
                END) AS centos7_count
           FROM public.v_cmdb_application_system_info t4_1
          GROUP BY t4_1.business_system_name) t4 ON (((t4.business_system_name)::text = (t.system_abbreviation)::text)))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_system_admin_responsibility_company OWNER TO postgres;



CREATE VIEW public.v_cmdb_host_scan_results AS
 SELECT t.management_ip,
        CASE
            WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
            ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
        END AS admin1,
    COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) AS admin2,
    COALESCE(t.designated_admin, ''::character varying) AS designated_admin,
    COALESCE(t2.data_center, t3.data_center, t4.data_center) AS datacenter,
        CASE
            WHEN ((
            CASE
                WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
                ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
            END <> ''::text) OR (COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) <> ''::text) OR ((t.designated_admin)::text <> ''::text)) THEN '有人管'::text
            ELSE '无人管'::text
        END AS management_status,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_vm_registry vm
              WHERE (((vm.management_ip)::text = (t.management_ip)::text) AND ((vm.del_flag)::text = '0'::text)))) THEN '是'::character varying
            ELSE '否'::character varying
        END AS is_virtual_machine,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.cmdb_registration_status,
    t.remarks,
    t.scan_date,
    t.id,
    COALESCE(t2.function_purpose, t3.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM ((((public.cmdb_host_scan_results t
     LEFT JOIN ( SELECT t2_1.management_ip,
            max((t2_1.admin1)::text) AS admin1,
            max((t2_1.admin2)::text) AS admin2,
            max((t2_1.data_center)::text) AS data_center,
            max((t2_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_device_management t2_1
          GROUP BY t2_1.management_ip) t2 ON (((t2.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t3_1.management_ip,
            max((t3_1.admin1)::text) AS admin1,
            max((t3_1.admin2)::text) AS admin2,
            max((t3_1.data_center)::text) AS data_center,
            max((t3_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_server_management t3_1
          GROUP BY t3_1.management_ip) t3 ON (((t3.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t4_1.management_ip,
            max(t4_1.server_admin1) AS admin1,
            max(t4_1.server_admin2) AS admin2,
            max(t4_1.data_center) AS data_center
           FROM public.v_cmdb_application_system_info t4_1
          GROUP BY t4_1.management_ip) t4 ON (((t4.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t5_1.management_ip,
            max((t5_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_vm_registry t5_1
          GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_host_scan_results OWNER TO postgres;