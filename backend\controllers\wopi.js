const fs = require('fs');
const path = require('path');
const { getBaseUrl } = require('../utils/config');

/**
 * WOPI协议控制器
 * 用于LibreOffice Online文档预览集成
 */

// WOPI CheckFileInfo接口 - 返回文件信息
exports.checkFileInfo = async (req, res) => {
  try {
    const { fileId } = req.params;


    // 从fileId中解析出实际的文件ID
    const actualFileId = fileId.replace('file_', '');
    
    // 检查是否是变更管理文件（格式：changeId_fileType）
    if (actualFileId.includes('_')) {
      const [changeId, fileType] = actualFileId.split('_');
      
      // 查询变更管理文件信息
      let query;
      if (fileType === 'oa_process') {
        query = 'SELECT oa_process_file AS file_path, change_number, change_title FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'signed_archive') {
        query = 'SELECT signed_archive_file AS file_path, change_number, change_title FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'operation_sheet') {
        query = 'SELECT operation_sheet AS file_path, change_number, change_title FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'supplementary_material') {
        query = 'SELECT supplementary_material AS file_path, change_number, change_title FROM ops_change_management WHERE change_id = $1';
      } else {
        return res.status(404).json({ error: 'Invalid file type' });
      }
      
      const result = await req.db.query(query, [changeId]);
      
      if (result.rows.length === 0 || !result.rows[0].file_path) {
        return res.status(404).json({ error: 'File not found' });
      }

      const fileInfo = result.rows[0];
      const filePath = path.join(process.env.FILE_UPLOAD_BASE_PATH, fileInfo.file_path);
      
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'Physical file not found' });
      }

      // 获取文件统计信息
      const stats = fs.statSync(filePath);
      const fileName = path.basename(fileInfo.file_path);
      
      // 返回WOPI标准的文件信息
      const wopiResponse = {
        // 基本文件信息
        BaseFileName: fileName,
        Size: stats.size,
        Version: stats.mtime.getTime().toString(),
        
        // 用户信息
        UserId: 'user_' + (req.user?.username || 'anonymous'),
        UserFriendlyName: req.user?.display_name || req.user?.username || '匿名用户',
        
        // 权限设置
        UserCanWrite: false,  // 只读模式
        UserCanNotWriteRelative: true,
        UserCanRename: false,
        UserCanPrint: true,
        UserCanExport: true,
        
        // 文件属性
        ReadOnly: true,
        RestrictedWebViewOnly: false,
        
        // 时间戳
        LastModifiedTime: stats.mtime.toISOString(),
        
        // 下载URL
        DownloadUrl: `${getBaseUrl()}/api/wopi/files/${fileId}/contents`,
        
        // 其他属性
        SupportsLocks: false,
        SupportsGetLock: false,
        SupportsExtendedLockLength: false,
        SupportsCobalt: false,
        SupportsFolders: false,
        SupportsUpdate: false,
        SupportsRename: false,
        SupportsDeleteFile: false,
        
        // 品牌信息
        BreadcrumbBrandName: 'CMDB系统',
        BreadcrumbDocName: fileName,
        BreadcrumbFolderName: fileInfo.change_number ? `变更${fileInfo.change_number}` : '文档预览'
      };


      return res.json(wopiResponse);
    }
    
    // 原有的attachment_files表查询逻辑（保持兼容性）
    const query = `
      SELECT af.*, c.change_number, c.change_title 
      FROM attachment_files af
      LEFT JOIN ops_changes c ON af.change_id = c.id
      WHERE af.id = $1
    `;
    
    const result = await req.db.query(query, [actualFileId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'File not found' });
    }

    const fileInfo = result.rows[0];
    const filePath = fileInfo.file_path;
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Physical file not found' });
    }

    // 获取文件统计信息
    const stats = fs.statSync(filePath);
    
    // 返回WOPI标准的文件信息
    const wopiResponse = {
      // 基本文件信息
      BaseFileName: fileInfo.original_name || path.basename(filePath),
      Size: stats.size,
      Version: stats.mtime.getTime().toString(),
      
      // 用户信息
      UserId: 'user_' + (req.user?.username || 'anonymous'),
      UserFriendlyName: req.user?.display_name || req.user?.username || '匿名用户',
      
      // 权限设置
      UserCanWrite: false,  // 只读模式
      UserCanNotWriteRelative: true,
      UserCanRename: false,
      UserCanPrint: true,
      UserCanExport: true,
      
      // 文件属性
      ReadOnly: true,
      RestrictedWebViewOnly: false,
      
      // 时间戳
      LastModifiedTime: stats.mtime.toISOString(),
      
      // 下载URL
      DownloadUrl: `${getBaseUrl()}/api/wopi/files/${fileId}/contents`,
      
      // 其他属性
      SupportsLocks: false,
      SupportsGetLock: false,
      SupportsExtendedLockLength: false,
      SupportsCobalt: false,
      SupportsFolders: false,
      SupportsUpdate: false,
      SupportsRename: false,
      SupportsDeleteFile: false,
      
      // 品牌信息
      BreadcrumbBrandName: 'CMDB系统',
      BreadcrumbDocName: fileInfo.original_name || path.basename(filePath),
      BreadcrumbFolderName: fileInfo.change_number ? `变更${fileInfo.change_number}` : '文档预览'
    };

    res.json(wopiResponse);

  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// WOPI GetFile接口 - 返回文件内容
exports.getFileContents = async (req, res) => {
  try {
    const { fileId } = req.params;


    // 从fileId中解析出实际的文件ID
    const actualFileId = fileId.replace('file_', '');
    
    // 检查是否是变更管理文件（格式：changeId_fileType）
    if (actualFileId.includes('_')) {
      const [changeId, fileType] = actualFileId.split('_');
      
      // 查询变更管理文件信息
      let query;
      if (fileType === 'oa_process') {
        query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'signed_archive') {
        query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'operation_sheet') {
        query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
      } else if (fileType === 'supplementary_material') {
        query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
      } else {
        return res.status(404).json({ error: 'Invalid file type' });
      }
      
      const result = await req.db.query(query, [changeId]);
      
      if (result.rows.length === 0 || !result.rows[0].file_path) {
        return res.status(404).json({ error: 'File not found' });
      }

      const fileInfo = result.rows[0];
      const filePath = path.join(process.env.FILE_UPLOAD_BASE_PATH, fileInfo.file_path);
      
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'Physical file not found' });
      }

      // 设置响应头
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(path.basename(fileInfo.file_path))}"`);
      
      // 流式传输文件
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
      return;
    }
    
    // 原有的attachment_files表查询逻辑（保持兼容性）
    const query = 'SELECT * FROM attachment_files WHERE id = $1';
    const result = await req.db.query(query, [actualFileId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'File not found' });
    }

    const fileInfo = result.rows[0];
    const filePath = fileInfo.file_path;
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Physical file not found' });
    }

    // 设置响应头
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileInfo.original_name)}"`);
    
    // 流式传输文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// 获取WOPI发现信息（可选）
exports.getWopiDiscovery = async (req, res) => {
  try {
    const baseUrl = getBaseUrl();
    
    // 简化的WOPI发现信息
    const discovery = {
      'net-zone': {
        name: 'external-http',
        apps: [
          {
            name: 'Excel',
            favIconUrl: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/images/x-office-spreadsheet.svg`,
            actions: [
              {
                name: 'view',
                ext: 'xlsx',
                urlsrc: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/cool.html?`
              },
              {
                name: 'view', 
                ext: 'xls',
                urlsrc: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/cool.html?`
              }
            ]
          },
          {
            name: 'Word',
            favIconUrl: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/images/x-office-document.svg`,
            actions: [
              {
                name: 'view',
                ext: 'docx',
                urlsrc: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/cool.html?`
              },
              {
                name: 'view',
                ext: 'doc', 
                urlsrc: `${process.env.LIBREOFFICE_SERVER}/browser/e724e42045/cool.html?`
              }
            ]
          }
        ]
      }
    };

    res.json(discovery);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
}; 