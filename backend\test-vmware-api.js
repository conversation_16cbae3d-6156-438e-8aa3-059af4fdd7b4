/**
 * 测试VMware接口数据获取和解析
 */

const axios = require('axios');

async function testVmwareApi() {
    const dataSourceUrl = 'http://**************:8081/config_files?file=vmware_hosts.json';
    
    try {
        console.log('开始测试VMware接口...');
        console.log('数据源URL:', dataSourceUrl);
        
        // 获取远程数据
        const response = await axios.get(dataSourceUrl, {
            timeout: 30000,
            headers: {
                'User-Agent': 'CMDB-VMware-Sync-Service/1.0'
            }
        });

        if (!response.data) {
            throw new Error('接口返回数据为空');
        }

        let responseData = response.data;
        
        // 如果返回的是字符串，尝试解析为JSON
        if (typeof responseData === 'string') {
            responseData = JSON.parse(responseData);
        }

        // 打印原始数据结构用于调试
        console.log('接口返回的原始数据结构:', JSON.stringify(responseData, null, 2).substring(0, 1000) + '...');
        console.log('数据类型:', typeof responseData);
        console.log('是否为数组:', Array.isArray(responseData));
        console.log('包含的键:', Object.keys(responseData || {}));

        // 提取虚拟机数据和更新时间
        let vmwareData;
        let dataSourceTime;

        // 处理不同的数据格式
        if (Array.isArray(responseData)) {
            // 如果直接是数组格式
            vmwareData = responseData;
            dataSourceTime = new Date();
            console.log('检测到直接数组格式');
        } else if (responseData && typeof responseData === 'object') {
            // 如果是对象格式，尝试提取data字段
            if (Array.isArray(responseData.data)) {
                vmwareData = responseData.data;
                dataSourceTime = responseData.updatetime ? new Date(responseData.updatetime) : new Date();
                console.log('检测到对象包装格式，提取data字段');
            } else if (responseData.data && Array.isArray(responseData.data.data)) {
                // 处理嵌套的data结构
                vmwareData = responseData.data.data;
                dataSourceTime = responseData.data.updatetime ? new Date(responseData.data.updatetime) : new Date();
                console.log('检测到嵌套对象格式');
            } else {
                // 如果没有data字段，尝试将整个对象作为数组处理
                const keys = Object.keys(responseData);
                console.log('未找到标准data字段，可用键:', keys);
                
                // 尝试找到数组字段
                const arrayKey = keys.find(key => Array.isArray(responseData[key]));
                if (arrayKey) {
                    vmwareData = responseData[arrayKey];
                    const updateTimeStr = responseData.updatetime || responseData.timestamp;
                    dataSourceTime = updateTimeStr ? new Date(updateTimeStr) : new Date();
                    console.log(`使用字段 "${arrayKey}" 作为数据源`);
                } else {
                    throw new Error(`接口返回的数据格式不正确，未找到数组数据。返回的键: ${keys.join(', ')}`);
                }
            }
        } else {
            throw new Error(`接口返回的数据格式不正确，期望对象或数组格式，实际类型: ${typeof responseData}`);
        }

        // 验证最终的数据格式
        if (!Array.isArray(vmwareData)) {
            throw new Error(`提取的虚拟机数据不是数组格式，实际类型: ${typeof vmwareData}`);
        }

        console.log(`成功解析数据！`);
        console.log(`虚拟机数据数量: ${vmwareData.length}`);
        console.log(`数据源时间: ${dataSourceTime.toISOString()}`);
        
        // 显示前几条数据的示例
        if (vmwareData.length > 0) {
            console.log('前3条数据示例:');
            vmwareData.slice(0, 3).forEach((vm, index) => {
                console.log(`  ${index + 1}. 名称: ${vm.name}, vCenter: ${vm.vcenter_ip}, ESXi: ${vm.esxi_ip}, VM IP: ${vm.vm_ip}`);
            });
        }

        console.log('测试成功！');
        
    } catch (error) {
        console.error('测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

testVmwareApi();