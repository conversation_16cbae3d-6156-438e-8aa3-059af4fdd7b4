<style lang="scss" scoped>
.layout-container {
  height: 100vh;

  .aside {
    background: linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
    box-shadow: inset -1px 0 0 rgba(88, 166, 255, 0.08), 0 0 15px rgba(10, 14, 39, 0.3);
    transition: width 0.3s;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */

    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      color: #fff;

      img {
        width: 28px;
        height: 28px;
        margin-right: 10px;
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 5px;
        padding: 3px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }
      
      img:hover {
        background-color: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      span {
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        opacity: 0.95;
        transition: opacity 0.3s ease;
      }
      
      span:hover {
        opacity: 1;
      }
    }

    .el-menu {
      border-right: none;
      flex: 1;
      overflow-y: auto;
      /* 滚动条优化 */
      padding-right: 4px; /* 留出更多空间给滚动条 */
      margin-right: -4px; /* 补偿留出的空间 */
      scrollbar-gutter: stable; /* 稳定的滚动条空间 */
      /* 添加底部内边距，防止最后一个菜单项被版本号遮挡 */
      padding-bottom: 50px; /* 版本信息高度 + 额外空间 */

      /* 确保滚动条不影响菜单项的布局 */
      &::-webkit-scrollbar {
        width: 8px;
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      &:hover::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .header {
    background-color: #fff;
    border-bottom: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    .header-left {
      display: flex;
      align-items: center;

      .collapse-btn {
        font-size: 20px;
        cursor: pointer;
        margin-right: 20px;
      }
    }

    .header-right {
      display: flex;
      font-size: 15px;
      align-items: center;
      // cursor: pointer;
      margin-right: 20px;

      .avatar-container {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>


<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :style="{ width: isCollapse ? '60px' : '200px' }" class="aside">
      <div class="logo">
        <img src="@/assets/logo.png" alt="CMDB" />
        <span v-if="!isCollapse">IT资源管理系统</span>
      </div>
      <el-menu
        :default-active="this.$route.path"
        class="el-menu-vertical custom-scrollbar"
        :collapse="isCollapse"
        background-color="#0a0e27"
        text-color="#e1e4e8"
        active-text-color="#58a6ff"
        router
      >
        <el-menu-item index="/dashboard" v-if="hasPagePermission('/dashboard')">
          <el-icon><Monitor /></el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>

        <el-menu-item index="/global_search" v-if="hasPagePermission('/global_search')">
          <el-icon><Search /></el-icon>
          <template #title>全局搜索</template>
        </el-menu-item>

        <el-menu-item index="/ai_platform" v-if="hasPagePermission('/ai_platform')">
          <el-icon><Monitor /></el-icon>
          <template #title>AI平台</template>
        </el-menu-item>

        <el-sub-menu index="/asset" v-if="hasAssetManagementPermission()">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>IT资源管理</span>
          </template>

          <el-menu-item index="/cmdb_application_system_info" v-if="hasPagePermission('/cmdb_application_system_info')"
            >应用系统信息管理</el-menu-item
          >

          <el-menu-item index="/cmdb_device_management" v-if="hasPagePermission('/cmdb_device_management')"
            >网络设备资源管理</el-menu-item
          >
          <el-menu-item index="/cmdb_server_management" v-if="hasPagePermission('/cmdb_server_management')"
            >实体服务器资源管理</el-menu-item
          >
          <el-menu-item index="/cmdb_vm_registry" v-if="hasPagePermission('/cmdb_vm_registry')"
            >虚拟化资源管理</el-menu-item
          >

          <!-- <el-menu-item index="/cmdb_internet_line_management" v-if="hasPagePermission('/cmdb_internet_line_management')"
            >互联网线路管理</el-menu-item
          >
          <el-menu-item index="/cmdb_ip_mapping_management" v-if="hasPagePermission('/cmdb_ip_mapping_management')"
            >互联网IP映射管理</el-menu-item
          > -->

          <el-menu-item index="/cmdb_system_admin_responsibility_company" v-if="hasPagePermission('/cmdb_system_admin_responsibility_company')"
            >系统管理员责任表（公司）</el-menu-item
          >
          <el-menu-item index="/cmdb_system_admin_responsibility" v-if="hasPagePermission('/cmdb_system_admin_responsibility')"
            >系统管理员责任表（外部）</el-menu-item
          >

          <el-menu-item index="/cmdb_host_scan_results" v-if="hasPagePermission('/cmdb_host_scan_results')"
            >无人管IP资源</el-menu-item
          >

          <el-menu-item index="/cmdb_monitored_ip_list" v-if="hasPagePermission('/cmdb_monitored_ip_list')"
            >监控IP列表</el-menu-item
          >

        </el-sub-menu>

        <el-sub-menu index="/discovery" v-if="hasDiscoveryPermission()">
          <template #title>
            <el-icon><Connection /></el-icon>
            <span>自动发现</span>
          </template>
          <el-menu-item index="/cmdb_discovery_tasks" v-if="hasPagePermission('/cmdb_discovery_tasks')">发现任务管理</el-menu-item>
          <el-menu-item index="/cmdb_discovery_results" v-if="hasPagePermission('/cmdb_discovery_results')">发现结果查看</el-menu-item>
          <el-menu-item index="/cmdb_discovery_cleanup" v-if="hasPagePermission('/cmdb_discovery_cleanup')">发现结果清理</el-menu-item>
          <el-menu-item index="/cmdb_schedule_tasks" v-if="hasPagePermission('/cmdb_schedule_tasks')">调度任务管理</el-menu-item>
          <el-menu-item index="/cmdb_vmware_hosts_management" v-if="hasPagePermission('/cmdb_vmware_hosts_management')">虚拟化信息更新管理</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/operations" v-if="hasOperationsPermission()">
          <template #title>
            <el-icon><Operation /></el-icon>
            <span>运维管理</span>
          </template>
          <el-menu-item index="/ops_calendar" v-if="hasPagePermission('/ops_calendar')">交易日历</el-menu-item>
          <el-menu-item index="/ops_change_management" v-if="hasPagePermission('/ops_change_management')">变更管理</el-menu-item>
          <el-menu-item index="/ops_change_templates" v-if="hasPagePermission('/ops_change_templates')">变更模板管理</el-menu-item>
          <el-menu-item index="/ops_event_management" v-if="hasPagePermission('/ops_event_management')">事件管理</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/reports" v-if="hasReportPermission()">
          <template #title>
            <el-icon><PieChart /></el-icon>
            <span>报表中心</span>
          </template>
          <el-menu-item index="/report_network_device_age" v-if="hasPagePermission('/report_network_device_age')">网络设备年限情况统计</el-menu-item>
          <el-menu-item index="/report_server_age" v-if="hasPagePermission('/report_server_age')">实体服务器年限情况统计</el-menu-item>
          <!-- <el-menu-item index="/cmdb_internet_line_cost_statistics" v-if="hasPagePermission('/cmdb_internet_line_cost_statistics')">互联网线路费用统计</el-menu-item> -->
        </el-sub-menu>

        <el-sub-menu index="/config" v-if="hasConfigPermission()">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>配置管理</span>
          </template>
          <el-menu-item index="/cmdb_data_dictionary" v-if="hasPagePermission('/cmdb_data_dictionary')">数据字典</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/issues" v-if="hasIssuesPermission()">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>需求与问题</span>
          </template>
          <el-menu-item index="/cmdb_issue_collection" v-if="hasPagePermission('/cmdb_issue_collection')">需求与问题收集</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/system" v-if="hasSystemPermission()">
          <template #title>
            <el-icon><Tools /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/cmdb_users" v-if="hasPagePermission('/cmdb_users')">用户管理</el-menu-item>
          <el-menu-item index="/cmdb_page_permissions" v-if="hasPagePermission('/cmdb_page_permissions')">页面权限管理</el-menu-item>
          <el-menu-item index="/msg_push_management" v-if="hasPagePermission('/msg_push_management')">消息推送管理</el-menu-item>
        </el-sub-menu>

      </el-menu>
      <!-- 侧边栏底部版本信息 -->
      <SidebarVersionInfo :is-collapsed="isCollapse" />
    </el-aside>

    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-icon class="collapse-btn" @click="toggleSidebar">
            <Fold v-if="!isCollapse" />
            <Expand v-else />
          </el-icon>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }"
              >首页</el-breadcrumb-item
            >
            <el-breadcrumb-item>{{
              this.$route.meta.title
            }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <div style="margin: 20px">
            <span style="font-weight: bold">用户：</span>{{ username }}
            <span style="font-weight: bold">权限：</span>{{ formatIdus(role) }}
          </div>
          <div>
            <el-dropdown trigger="click" @command="handleCommand">
              <div class="avatar-container">
                <el-avatar :size="32" :src="defaultAvatar" />
                <!-- <el-avatar :size="32" :src="userStore.userInfo?.avatar" /> -->
                <!-- <span class="username">{{ userStore.userInfo?.realName }}</span> -->
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile"
                    >个人信息</el-dropdown-item
                  >
                  <el-dropdown-item command="password"
                    >修改密码</el-dropdown-item
                  >
                  <el-dropdown-item divided command="logout"
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <!-- 主要内容区 -->
      <el-main class="main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>

      <!-- 修改密码对话框 -->
      <el-dialog
        title="修改密码"
        v-model="dialogVisible"
        width="350px"
        align-center
        @close="resetForm('passwordForm')"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          :model="form"
          :rules="rules"
          ref="passwordForm"
          label-width="120px"
          label-position="right"
        >
          <el-form-item label="当前密码:" prop="currentPassword">
            <el-input
              type="password"
              style="width: 240px"
              v-model="form.currentPassword"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="新密码:" prop="newPassword">
            <el-input
              type="password"
              style="width: 240px"
              v-model="form.newPassword"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认新密码:" prop="confirmPassword">
            <el-input
              type="password"
              style="width: 240px"
              v-model="form.confirmPassword"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm('passwordForm')"
            >确定</el-button
          >
        </span>
      </el-dialog>

    </el-container>
  </el-container>
</template>


<script>
import { ElMessageBox } from "element-plus";
import {
  Monitor,
  Box,
  Setting,
  Tools,
  Fold,
  Expand,
  CaretBottom,
  User,
  Search,
  Document,
  PieChart,
  Connection,
  Management as Operation,
} from "@element-plus/icons-vue";
import SidebarVersionInfo from '@/components/SidebarVersionInfo.vue';

export default {
  components: {
    Monitor,
    Box,
    Setting,
    Tools,
    Fold,
    Expand,
    CaretBottom,
    User,
    Search,
    Document,
    PieChart,
    Connection,
    Operation,
    SidebarVersionInfo,
  },

  mounted() {
    // 初始化CSS变量，用于水平滚动条位置计算
    document.documentElement.style.setProperty('--left-sidebar-width', this.isCollapse ? '60px' : '200px');
  },

  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.form.newPassword) {
        callback(new Error("两次输入的新密码不一致"));
      } else {
        callback();
      }
    };

    return {
      username: localStorage.getItem("loginUsername"),
      role: localStorage.getItem("role_code"),
      defaultAvatar:
        "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png", // 默认头像 URL
      isCollapse: false,
      // 自定义验证规则：确认新密码与新密码一致

      // 对话框状态
      dialogVisible: false,
      form: {
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      },

      rules: {
        currentPassword: [
          { required: true, message: "请输入当前密码", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
        ],
        confirmPassword: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          { validator: validateConfirmPassword, trigger: "blur" },
        ],
      },

      // 表单数据
      formData: {
        id: null,
        username: "",
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
    };
  },

  methods: {
    // logOff() {
    //   // 退出登录
    //   this.$router.push("/");
    // },

    formatIdus(str) {
      if (!str || typeof str !== "string") {
        return ""; // 或者你可以选择返回其他默认值
      }

      const mapping = {
        I: "增",
        D: "删",
        U: "改",
        S: "查",
      };
      return str
        .split("")
        .map((char) => mapping[char])
        .join("");
    },

    async handleCommand(command) {
      switch (command) {
        case "profile":
          this.$router.push("/cmdb_users");
          break;
        case "password":
          this.dialogVisible = !this.dialogVisible;
          break;
        case "logout":
          await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });
          localStorage.removeItem("authToken");
          this.$router.push("/login");
          break;
      }
    },

    toggleSidebar() {
      this.isCollapse = !this.isCollapse; // 直接操作 isCollapse

      // 更新CSS变量，用于水平滚动条位置计算
      document.documentElement.style.setProperty('--left-sidebar-width', this.isCollapse ? '60px' : '200px');
    },

    async submitForm(formName) {
      // 验证表单是否有效
      const valid = await this.$refs[formName].validate();

      if (valid) {
        try {
          // 准备要发送的数据
          const requestData = {
            username: this.username,
            password: this.form.newPassword,
            currentPassword: this.form.currentPassword,
          };

          // 发送请求到服务器
          const response = await this.$axios.post(
            "/api/update_cmdb_users_password",
            requestData
          );
          // 请求成功后的处理
          if (response.data.code === 1) {
            this.$message.error(response.data.msg);
          } else {
            // 显示成功消息并关闭对话框
            this.$message.success("密码修改成功");
            this.dialogVisible = false;
          }
        } catch (error) {
          // 请求失败后的处理
          console.error("更新失败:", error);
          this.$message.error("更新失败");
        }
      } else {
        // 表单验证失败的处理
        this.$message.error("表单验证失败，请检查输入");
      }
    },

    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields();
      }
    },

    // 检查是否有页面权限
    hasPagePermission(pagePath) {
      // admin 用户有所有页面的权限
      if (this.username === 'admin') {
        return true;
      }

      // 从 localStorage 中获取页面权限
      const pagePermissions = JSON.parse(localStorage.getItem('pagePermissions') || '[]');

      // 特殊处理AI平台相关页面，如果用户已经登录，默认允许访问
      if (pagePath === '/ai_platform' || pagePath === '/ai_knowledge_base' || pagePath === '/document_editor') {
        // 如果权限列表中没有这些页面，尝试刷新权限
        const hasAIPermission = pagePermissions.some(permission =>
          permission.page_path === '/ai_platform' ||
          permission.page_path === '/ai_knowledge_base' ||
          permission.page_path === '/ai_document_editor'
        );

        if (!hasAIPermission) {
          // 异步刷新权限，不阻塞UI
          this.refreshPermissions();
        }

        // 允许访问AI平台
        return true;
      }

      // 检查是否有权限访问指定页面
      return pagePermissions.some(permission => {
        return pagePath === permission.page_path || pagePath.startsWith(permission.page_path + '/');
      });
    },

    // 刷新用户权限
    async refreshPermissions() {
      try {
        const response = await this.$axios.post('/api/get_current_user', {}, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`
          }
        });

        if (response.data.code === 0 && response.data.msg.pagePermissions) {
          localStorage.setItem('pagePermissions', JSON.stringify(response.data.msg.pagePermissions));
          console.log('权限刷新成功');
        }
      } catch (error) {
        console.error('刷新权限失败:', error);
      }
    },

    // 检查是否有资产管理模块的权限
    hasAssetManagementPermission() {
      return this.hasPagePermission('/cmdb_device_management') ||
             this.hasPagePermission('/cmdb_server_management') ||
             this.hasPagePermission('/cmdb_vm_registry') ||
             this.hasPagePermission('/cmdb_application_system_info') ||
             this.hasPagePermission('/cmdb_system_admin_responsibility_company') ||
             this.hasPagePermission('/cmdb_system_admin_responsibility') ||
             this.hasPagePermission('/cmdb_host_scan_results') ||
             this.hasPagePermission('/cmdb_monitored_ip_list');
    },

    // 检查是否有自动发现模块的权限
    hasDiscoveryPermission() {
      return this.hasPagePermission('/cmdb_discovery_tasks') ||
             this.hasPagePermission('/cmdb_discovery_results') ||
             this.hasPagePermission('/cmdb_discovery_cleanup') ||
             this.hasPagePermission('/cmdb_schedule_tasks') ||
             this.hasPagePermission('/cmdb_vmware_hosts_management');
    },

    // 检查是否有报表模块的权限
    hasReportPermission() {
      return this.hasPagePermission('/report_network_device_age') ||
             this.hasPagePermission('/report_server_age');
    },

    // 检查是否有配置管理模块的权限
    hasConfigPermission() {
      return this.hasPagePermission('/cmdb_data_dictionary');
    },

    // 检查是否有需求与问题模块的权限
    hasIssuesPermission() {
      return this.hasPagePermission('/cmdb_issue_collection');
    },

    // 检查是否有系统管理模块的权限
    hasSystemPermission() {
      return this.hasPagePermission('/cmdb_users') ||
             this.hasPagePermission('/cmdb_page_permissions');
    },

    // 检查是否有运维管理模块的权限
    hasOperationsPermission() {
      return this.hasPagePermission('/ops_change_management') ||
             this.hasPagePermission('/ops_change_templates') ||
             this.hasPagePermission('/ops_event_management');
    },
  },
};
</script>