import { apiClient } from '@/main'

/**
 * 获取虚拟机宿主机IP信息
 * @param {string} managementIp 虚拟机管理IP
 * @returns {Promise<Object>} API响应
 */
export async function getZabbixHostInfo(managementIp) {
  try {
    const response = await apiClient.post('/api/get_zabbix_host_info', {
      management_ip: managementIp
    }, {
      timeout: 15000 // 15秒超时
    });

    // 检查响应格式
    if (response && response.data) {
      return response.data;
    } else {
      return { 
        success: false, 
        error: '响应格式不正确',
        code: 'INVALID_RESPONSE_FORMAT'
      };
    }
  } catch (error) {

    // 如果有响应数据，返回响应数据
    if (error.response && error.response.data) {
      return error.response.data;
    }

    // 返回错误响应
    return {
      success: false,
      error: error.response?.data?.error || error.message || '查询宿主机IP失败',
      code: error.response?.data?.code || 'NETWORK_ERROR'
    };
  }
}

/**
 * 批量更新虚拟机宿主机IP
 * @param {Object} options 更新选项
 * @param {Array<number>} options.vm_ids 虚拟机ID列表（可选）
 * @param {boolean} options.update_all 是否更新所有虚拟机
 * @param {Object} options.options 其他选项
 * @returns {Promise<Object>} API响应
 */
export async function updateVmHostIpsBatch(options = {}) {
  try {
    const response = await apiClient.post('/api/update_vm_host_ips_batch', options, {
      timeout: 120000 // 2分钟超时，批量操作需要更长时间
    });

    // 检查响应格式
    if (response && response.data) {
      return response.data;
    } else {
      return { 
        success: false, 
        error: '响应格式不正确',
        code: 'INVALID_RESPONSE_FORMAT'
      };
    }
  } catch (error) {

    // 如果有响应数据，返回响应数据
    if (error.response && error.response.data) {
      return error.response.data;
    }

    // 返回错误响应
    return {
      success: false,
      error: error.response?.data?.error || error.message || '批量更新宿主机IP失败',
      code: error.response?.data?.code || 'NETWORK_ERROR'
    };
  }
}

/**
 * 获取Zabbix配置状态
 * @returns {Promise<Object>} API响应
 */
export async function getZabbixConfigStatus() {
  try {
    const response = await apiClient.get('/api/zabbix_config_status', {
      timeout: 10000 // 10秒超时
    });

    // 检查响应格式
    if (response && response.data) {
      return response.data;
    } else {
      return { 
        success: false, 
        error: '响应格式不正确',
        code: 'INVALID_RESPONSE_FORMAT'
      };
    }
  } catch (error) {

    // 如果有响应数据，返回响应数据
    if (error.response && error.response.data) {
      return error.response.data;
    }

    // 返回错误响应
    return {
      success: false,
      error: error.response?.data?.error || error.message || '获取配置状态失败',
      code: error.response?.data?.code || 'NETWORK_ERROR'
    };
  }
}

/**
 * 带缓存的宿主机IP查询
 * 使用简单的内存缓存避免频繁API调用
 */
class ZabbixHostInfoCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 获取缓存的宿主机IP信息
   * @param {string} managementIp 管理IP
   * @returns {Object|null} 缓存的数据或null
   */
  get(managementIp) {
    const cached = this.cache.get(managementIp);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * 设置缓存的宿主机IP信息
   * @param {string} managementIp 管理IP
   * @param {Object} data 数据
   */
  set(managementIp, data) {
    this.cache.set(managementIp, {
      data: data,
      timestamp: Date.now()
    });
  }

  /**
   * 清除指定IP的缓存
   * @param {string} managementIp 管理IP
   */
  clear(managementIp) {
    this.cache.delete(managementIp);
  }

  /**
   * 清除所有缓存
   */
  clearAll() {
    this.cache.clear();
  }
}

// 创建缓存实例
const hostInfoCache = new ZabbixHostInfoCache();

/**
 * 带缓存的获取虚拟机宿主机IP信息
 * @param {string} managementIp 虚拟机管理IP
 * @param {boolean} forceRefresh 是否强制刷新缓存
 * @returns {Promise<Object>} API响应
 */
export async function getZabbixHostInfoWithCache(managementIp, forceRefresh = false) {
  // 检查缓存
  if (!forceRefresh) {
    const cached = hostInfoCache.get(managementIp);
    if (cached) {
      return cached;
    }
  }

  // 调用API
  const result = await getZabbixHostInfo(managementIp);
  
  // 只缓存成功的结果
  if (result.success) {
    hostInfoCache.set(managementIp, result);
  }

  return result;
}

/**
 * 清除宿主机IP缓存
 * @param {string} managementIp 管理IP（可选，不提供则清除所有）
 */
export function clearZabbixHostInfoCache(managementIp = null) {
  if (managementIp) {
    hostInfoCache.clear(managementIp);
  } else {
    hostInfoCache.clearAll();
  }
}

/**
 * 防抖函数工具
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 创建防抖的宿主机IP查询函数
 * @param {number} delay 防抖延迟时间，默认1000ms
 * @returns {Function} 防抖后的查询函数
 */
export function createDebouncedHostInfoQuery(delay = 1000) {
  return debounce(async (managementIp, callback) => {
    try {
      const result = await getZabbixHostInfoWithCache(managementIp);
      if (callback && typeof callback === 'function') {
        callback(result);
      }
      return result;
    } catch (error) {
      console.error('防抖查询宿主机IP失败:', error);
      if (callback && typeof callback === 'function') {
        callback({
          success: false,
          error: error.message || '查询失败',
          code: 'DEBOUNCED_QUERY_ERROR'
        });
      }
    }
  }, delay);
}