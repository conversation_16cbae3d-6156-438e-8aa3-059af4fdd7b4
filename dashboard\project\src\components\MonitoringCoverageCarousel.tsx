import React, { useState, useEffect } from 'react';
import { Activity } from 'lucide-react';
import TechProgressBar from './TechProgressBar';
import EnhancedCard from './EnhancedCard';

interface CoverageMetric {
  name: string;
  value: number;
  total: number;
  covered: number;
  icon: React.ReactNode;
}

interface MonitoringCoverageCarouselProps {
  metrics: CoverageMetric[];
  itemsPerPage?: number;
  autoPlayInterval?: number;
  className?: string;
}

const MonitoringCoverageCarousel: React.FC<MonitoringCoverageCarouselProps> = ({
  metrics,
  itemsPerPage = 3,
  autoPlayInterval = 5000,
  className = ""
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayedMetrics, setDisplayedMetrics] = useState<CoverageMetric[]>([]);

  const totalPages = Math.ceil(metrics.length / itemsPerPage);

  // 初始化显示的指标
  useEffect(() => {
    const startIndex = currentIndex * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, metrics.length);
    setDisplayedMetrics(metrics.slice(startIndex, endIndex));
  }, [currentIndex, metrics, itemsPerPage]);

  // 自动轮播
  useEffect(() => {
    if (totalPages <= 1) return;

    const interval = setInterval(() => {
      handleNext();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentIndex, totalPages, autoPlayInterval]);

  const handleNext = () => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % totalPages);
      setIsTransitioning(false);
    }, 300);
  };

  const handlePrevious = () => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev - 1 + totalPages) % totalPages);
      setIsTransitioning(false);
    }, 300);
  };

  const goToPage = (pageIndex: number) => {
    if (isTransitioning || pageIndex === currentIndex) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex(pageIndex);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <EnhancedCard 
      title="监控覆盖率" 
      icon={<Activity className="w-5 h-5" />}
      className={`h-80 ${className}`}
      animationDelay={0}
    >
      {/* 页面指示器和控制按钮 */}
      <div className="flex justify-between items-center mb-4">
        <button
          onClick={handlePrevious}
          disabled={isTransitioning}
          className="w-8 h-8 rounded-full glass-card flex items-center justify-center hover:bg-neon-blue/20 transition-all disabled:opacity-50"
        >
          <svg className="w-4 h-4 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div className="flex space-x-2">
          {Array.from({ length: totalPages }).map((_, i) => (
            <button
              key={i}
              onClick={() => goToPage(i)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                i === currentIndex 
                  ? 'bg-neon-blue shadow-lg shadow-neon-blue/50' 
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        <button
          onClick={handleNext}
          disabled={isTransitioning}
          className="w-8 h-8 rounded-full glass-card flex items-center justify-center hover:bg-neon-blue/20 transition-all disabled:opacity-50"
        >
          <svg className="w-4 h-4 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* 指标卡片容器 */}
      <div className="relative overflow-hidden">
        <div 
          className={`transition-all duration-500 ease-in-out ${
            isTransitioning ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
          }`}
        >
          <div className="space-y-3">
            {displayedMetrics.map((metric, index) => (
              <MetricCard 
                key={`${metric.name}-${currentIndex}-${index}`}
                metric={metric}
                animationDelay={index * 100}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 总体统计 - 美化版 */}
      <div className="mt-4 pt-4 border-t border-gray-700/50">
        <div className="glass-card rounded-lg p-4 bg-gradient-to-r from-neon-blue/5 to-neon-cyan/5 border border-neon-blue/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-neon-blue/20 to-neon-cyan/20 flex items-center justify-center">
                <Activity className="w-5 h-5 text-neon-blue" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-300">总覆盖率</div>
                <div className="text-xs text-gray-500">
                  {metrics.reduce((sum, m) => sum + m.covered, 0)}/{metrics.reduce((sum, m) => sum + m.total, 0)} 设备
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 环形进度条 */}
              <div className="relative w-16 h-16">
                <TechProgressBar 
                  value={((metrics.reduce((sum, m) => sum + m.covered, 0) / 
                           metrics.reduce((sum, m) => sum + m.total, 0)) * 100)}
                  max={100}
                  label=""
                  variant="circular"
                  size={64}
                  glowColor="#00d4ff"
                  animationDuration={1200}
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-bold text-neon-blue">
                    {((metrics.reduce((sum, m) => sum + m.covered, 0) / 
                       metrics.reduce((sum, m) => sum + m.total, 0)) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
              
              {/* 数值显示 */}
              <div className="text-right">
                <div className="text-2xl font-bold tech-gradient-text count-animation">
                  {((metrics.reduce((sum, m) => sum + m.covered, 0) / 
                     metrics.reduce((sum, m) => sum + m.total, 0)) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Overall Coverage
                </div>
              </div>
              
              {/* 状态指示器 */}
              <div className={`w-3 h-3 rounded-full ${
                ((metrics.reduce((sum, m) => sum + m.covered, 0) / 
                  metrics.reduce((sum, m) => sum + m.total, 0)) * 100) >= 95 
                  ? 'bg-neon-green shadow-lg shadow-neon-green/50' :
                ((metrics.reduce((sum, m) => sum + m.covered, 0) / 
                  metrics.reduce((sum, m) => sum + m.total, 0)) * 100) >= 90 
                  ? 'bg-neon-orange shadow-lg shadow-neon-orange/50' :
                  'bg-neon-red shadow-lg shadow-neon-red/50'
              } pulse-glow`} />
            </div>
          </div>
          
          {/* 趋势指示器 */}
          <div className="mt-3 flex items-center justify-center">
            <div className="flex items-center space-x-2 text-xs">
              <div className="w-2 h-2 bg-neon-green rounded-full animate-pulse" />
              <span className="text-gray-400">实时监控</span>
              <div className="w-1 h-1 bg-gray-600 rounded-full" />
              <span className="text-neon-blue">
                {metrics.length} 项指标
              </span>
            </div>
          </div>
        </div>
      </div>
    </EnhancedCard>
  );
};

interface MetricCardProps {
  metric: CoverageMetric;
  animationDelay: number;
}

const MetricCard: React.FC<MetricCardProps> = ({ metric, animationDelay }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, animationDelay);
    return () => clearTimeout(timer);
  }, [animationDelay]);

  return (
    <div 
      className={`flex items-center justify-between p-4 glass-card rounded-lg transition-all duration-500 hover:bg-white/5 hover:shadow-lg hover:shadow-neon-blue/20 ${
        isVisible ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'
      }`}
    >
      <div className="flex items-center space-x-3 flex-1">
        <div className="text-neon-blue p-2 rounded-lg bg-neon-blue/10">
          {metric.icon}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-200 mb-1">{metric.name}</div>
          <div className="text-xs text-gray-400">
            {metric.covered}/{metric.total} 设备
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        <div className="w-16">
          <TechProgressBar 
            value={metric.value}
            max={100}
            label=""
            variant="linear"
            glowColor={metric.value >= 95 ? "#10b981" : metric.value >= 90 ? "#f59e0b" : "#ef4444"}
            animationDuration={800}
          />
        </div>
        
        <div className="text-right min-w-[3rem]">
          <div className="text-sm font-bold text-white">
            {metric.value.toFixed(1)}%
          </div>
        </div>
        
        {/* 状态指示器 */}
        <div className={`w-2 h-2 rounded-full ${
          metric.value >= 95 ? 'bg-neon-green shadow-lg shadow-neon-green/50' :
          metric.value >= 90 ? 'bg-neon-orange shadow-lg shadow-neon-orange/50' :
          'bg-neon-red shadow-lg shadow-neon-red/50'
        } pulse-glow`} />
      </div>
    </div>
  );
};

export default MonitoringCoverageCarousel;