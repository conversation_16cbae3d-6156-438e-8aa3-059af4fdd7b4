<style lang="scss" scoped>
.user-manage {
  .search-card {
    margin-bottom: 10px;
    height: auto;

    // label-width:130px;
  }

  :deep(.el-table) {

    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap;
      /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all;
      /* 尽量保持单词完整，不强制断开 */
      max-width: 400px;
      /* 保持最大宽度不变 */
      width: 100%;
      /* 确保宽度一致 */
      word-wrap: break-word;
      /* 当单词超过容器宽度时允许换行 */
      display: inline-block;
      /* 确保元素可以正确处理宽度 */
    }

    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }

  .pagination {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
  }
}

.form-control {
  width: auto;
  /* 占满父容器宽度 */
  min-width: 190px;
}
</style>

<template>
  <div class="user-manage">
    <el-dialog v-model="dialogVisible.add" title="新增应用系统信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="addFormRef" label-position="right" status-icon>
          <!-- 管理IP -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <!-- <p>
          <span class="label">主机名:</span>
          <el-input v-model="formData.hostname" style="width: 240px" disabled />
        </p> -->

          <!-- 功能用途 -->
          <!-- <p>
          <span class="label">功能用途:</span>
          <el-input
            v-model="formData.function_purpose"
            style="width: 240px"
            clearable
          />
        </p> -->

          <!-- 管理员1 -->
          <!-- <p>
          <span class="label">管理员1:</span>
          <el-input
            v-model="formData.server_admin1"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 管理员2 -->
          <!-- <p>
          <span class="label">管理员2:</span>
          <el-input
            v-model="formData.server_admin2"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 所属机房 -->
          <!-- <p>
          <span class="label">所属机房:</span>
          <el-input
            v-model="formData.data_center"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- PING状态 -->
          <!-- <p>
          <span class="label">PING状态:</span>
          <el-input
            v-model="formData.machine_usage_status"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 归属业务系统 -->
          <el-form-item prop="business_system_name" label="归属业务系统:" required>
            <el-select v-model="formData.business_system_name" style="width: 240px" clearable placeholder="请选择归属业务系统"
              filterable>
              <el-option v-for="item in businessSystems" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>


          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:" required>
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable placeholder="请选择需要监控"
              @change="handleMonitoringRequirementChange">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === '否'">
            <el-input v-model="formData.monitoring_requirement_description" type="textarea" :rows="3"
              style="width: 240px" clearable placeholder="请输入不监控原因"
              :disabled="formData.monitoring_requirement === '是'" />
          </el-form-item>

          <!-- 生产属性 -->
          <el-form-item prop="production_attributes" label="生产属性:" required>
            <el-select v-model="formData.production_attributes" style="width: 240px" clearable placeholder="请选择生产属性">
              <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 主从角色 -->
          <el-form-item prop="master_slave_role" label="主从角色:" required>
            <el-select v-model="formData.master_slave_role" style="width: 240px" clearable placeholder="请选择主从角色"
              @change="validateRelatedMasterSlaveIps">
              <el-option v-for="item in masterslaveroles" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 关联主从机IP -->
          <el-form-item prop="related_master_slave_ips" label="关联主从机IP:" :required="getSingleMachineRequired()">
            <el-input v-model="formData.related_master_slave_ips" style="width: 240px" clearable
              placeholder="请输入关联主从机IP，多个IP用英文逗号分隔" />
          </el-form-item>

          <!-- 备份模式 -->
          <!-- <el-form-item prop="backup_mode" label="备份模式:">
            <el-select
              v-model="formData.backup_mode"
              style="width: 240px"
              placeholder="请选择备份模式"
            >
              <el-option
                v-for="item in backupmodes"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item> -->

          <!-- 互联网IP -->
          <el-form-item prop="internet_ip" label="互联网IP:">
            <el-input v-model="formData.internet_ip" style="width: 240px" clearable placeholder="请输入互联网IP" />
          </el-form-item>

          <!-- 互联网端口 -->
          <el-form-item prop="internet_port" label="互联网端口:">
            <el-input v-model="formData.internet_port" style="width: 240px" clearable placeholder="请输入互联网端口" />
          </el-form-item>

          <!-- 操作系统 -->
          <!-- <p>
          <span class="label">操作系统:</span>
          <el-input
            v-model="formData.operating_system"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 是否安装杀毒软件 -->
          <!-- <el-form-item prop="has_antivirus_software" label="是否安装杀毒软件:">
            <el-select
              v-model="formData.has_antivirus_software"
              style="width: 240px"
              clearable
              placeholder="请选择状态"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->

          <!-- 离线备注 -->
          <el-form-item prop="remarks" label="离线备注:">
            <el-input v-model="formData.remarks" style="width: 240px" type="textarea" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-form>

        <!-- 系统级别是否有管理员 -->
        <!-- <p>
          <span class="label">系统级别是否有管理员:</span>
          <el-input
            v-model="formData.has_system_administrator"
            style="width: 240px"
            disabled
          />
        </p> -->
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">返回</el-button>
          <el-button type="primary" @click="validateAndSubmitAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.edit" title="编辑应用系统信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="editFormRef" label-position="right" status-icon>
          <!-- 管理IP -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <!-- <p>
          <span class="label">主机名:</span>
          <el-input v-model="formData.hostname" style="width: 240px" disabled />
        </p> -->

          <!-- 功能用途 -->
          <!-- <p>
          <span class="label">功能用途:</span>
          <el-input
            v-model="formData.function_purpose"
            style="width: 240px"
            clearable
          />
        </p> -->

          <!-- 管理员1 -->
          <!-- <p>
          <span class="label">管理员1:</span>
          <el-input
            v-model="formData.server_admin1"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 管理员2 -->
          <!-- <p>
          <span class="label">管理员2:</span>
          <el-input
            v-model="formData.server_admin2"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 所属机房 -->
          <!-- <p>
          <span class="label">所属机房:</span>
          <el-input
            v-model="formData.data_center"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- PING状态 -->
          <!-- <p>
          <span class="label">PING状态:</span>
          <el-input
            v-model="formData.machine_usage_status"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 归属业务系统 -->
          <el-form-item prop="business_system_name" label="归属业务系统:" required>
            <el-select v-model="formData.business_system_name" style="width: 240px" clearable placeholder="请选择归属业务系统"
              filterable>
              <el-option v-for="item in businessSystems" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 系统管理员 -->
          <!-- <p>
          <span class="label">系统管理员:</span>
          <el-input
            v-model="formData.system_administrator"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 系统分级 -->
          <!-- <p>
          <span class="label">系统分级:</span>
          <el-input
            v-model="formData.system_classification"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 监控状态 -->
          <!-- <p>
          <span class="label">监控状态:</span>
          <el-select
            v-model="formData.is_monitored"
            style="width: 240px"
            placeholder="请选择状态"
          >
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </p> -->

          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:" required>
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable placeholder="请选择需要监控"
              @change="handleMonitoringRequirementChange">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === '否'">
            <el-input v-model="formData.monitoring_requirement_description" type="textarea" :rows="3"
              style="width: 240px" clearable placeholder="请输入不监控原因"
              :disabled="formData.monitoring_requirement === '是'" />
          </el-form-item>

          <!-- 生产属性 -->
          <el-form-item prop="production_attributes" label="生产属性:" required>
            <el-select v-model="formData.production_attributes" style="width: 240px" clearable placeholder="请选择生产属性">
              <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 主从角色 -->
          <el-form-item prop="master_slave_role" label="主从角色:" required>
            <el-select v-model="formData.master_slave_role" style="width: 240px" clearable placeholder="请选择主从角色"
              @change="validateRelatedMasterSlaveIps">
              <el-option v-for="item in masterslaveroles" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 关联主从机IP -->
          <el-form-item prop="related_master_slave_ips" label="关联主从机IP:" :required="getSingleMachineRequired()">
            <el-input v-model="formData.related_master_slave_ips" style="width: 240px" clearable
              placeholder="请输入关联主从机IP，多个IP用英文逗号分隔" />
          </el-form-item>

          <!-- 备份模式 -->
          <!-- <el-form-item prop="backup_mode" label="备份模式:">
            <el-select
              v-model="formData.backup_mode"
              style="width: 240px"
              placeholder="请选择备份模式" -->
          <!-- >
              <el-option
                v-for="item in backupmodes"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item> -->

          <!-- 互联网IP -->
          <el-form-item prop="internet_ip" label="互联网IP:">
            <el-input v-model="formData.internet_ip" style="width: 240px" clearable placeholder="请输入互联网IP" />
          </el-form-item>

          <!-- 互联网端口 -->
          <el-form-item prop="internet_port" label="互联网端口:">
            <el-input v-model="formData.internet_port" style="width: 240px" clearable placeholder="请输入互联网端口" />
          </el-form-item>

          <!-- 操作系统 -->
          <!-- <p>
          <span class="label">操作系统:</span>
          <el-input
            v-model="formData.operating_system"
            style="width: 240px"
            disabled
          />
        </p> -->

          <!-- 是否安装杀毒软件 -->
          <!-- <el-form-item prop="has_antivirus_software" label="是否安装杀毒软件:">
            <el-select
              v-model="formData.has_antivirus_software"
              style="width: 240px"
              clearable
              placeholder="请选择状态"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->

          <!-- 离线备注 -->
          <el-form-item prop="remarks" label="离线备注:">
            <el-input v-model="formData.remarks" style="width: 240px" type="textarea" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-form>

        <!-- 系统级别是否有管理员 -->
        <!-- <p>
          <span class="label">系统级别是否有管理员:</span>
          <el-input
            v-model="formData.has_system_administrator"
            style="width: 240px"
            disabled
          />
        </p> -->
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitEdit">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.delete" title="删除管理IP" width="500" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-alert type="warning" :title="`确定要删除 IP 为 ${formData.management_ip} 的记录吗？`" :closable="false" />
      <template #footer>
        <div>
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true">
        <el-row :gutter="10">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item label="管理IP">
              <el-autocomplete
                v-model="search.management_ip"
                :fetch-suggestions="(queryString, cb) => querySearchAsync(queryString, cb, 'management_ip')"
                placeholder="请输入管理IP"
                clearable
                class="form-control"
                @select="handleSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主机名">
              <el-autocomplete
                v-model="search.hostname"
                :fetch-suggestions="(queryString, cb) => querySearchAsync(queryString, cb, 'hostname')"
                placeholder="请输入主机名"
                clearable
                class="form-control"
                @select="handleSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="功能用途">
              <el-autocomplete
                v-model="search.function_purpose"
                :fetch-suggestions="(queryString, cb) => querySearchAsync(queryString, cb, 'function_purpose')"
                placeholder="请输入功能用途"
                clearable
                class="form-control"
                @select="handleSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="设备管理员">
              <el-select v-model="search.server_admin1" placeholder="请选择设备管理员" clearable filterable
                class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item label="系统管理员">
              <el-select v-model="search.system_administrator" placeholder="请选择系统管理员" clearable filterable
                class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="设备生命周期">
              <el-select v-model="search.operation_status" placeholder="请选择设备生命周期" clearable filterable class="form-control">
                <el-option v-for="item in operationstatuses" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="系统运行状态">
              <el-select v-model="search.system_operation_status" placeholder="请选择系统运行状态" clearable filterable
                class="form-control">
                <el-option v-for="item in systemoperationstatuses" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>          

          <el-col :span="6">
            <el-form-item label="PING状态">
              <el-select v-model="search.machine_usage_status" placeholder="请选择PING状态" clearable filterable class="form-control">
                <el-option label="在线" value="在线" />
                <el-option label="离线" value="离线" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所属机房">
              <el-select v-model="search.data_center" clearable filterable placeholder="请选择所属机房" class="form-control">
                <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="归属业务系统">
              <el-select v-model="search.business_system_name" placeholder="请选择归属业务系统" filterable clearable
                class="form-control">
                <el-option v-for="item in businessSystems" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="系统分级">
              <el-select v-model="search.system_classification" placeholder="请选择系统分级" clearable filterable class="form-control">
                <el-option v-for="item in systemclassifications" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="需要监控">
              <el-select v-model="search.monitoring_requirement" placeholder="请选择需要监控" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="监控状态">
              <el-select v-model="search.is_monitored" placeholder="请选择监控状态" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生产属性">
              <el-select v-model="search.production_attributes" placeholder="请选择生产属性" clearable filterable class="form-control">
                <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="操作系统">
              <el-select v-model="search.operating_system" placeholder="请选择操作系统" clearable filterable class="form-control">
                <el-option v-for="item in operatingsystems" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="是否虚拟机">
              <el-select v-model="search.is_virtual_machine" placeholder="请选择是否虚拟机" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="search-buttons-col">
            <el-form-item label=" " class="form-item-with-label search-buttons">
              <div class="button-container">
                <el-button type="primary" @click="loadData">
                  <el-icon>
                    <Search />
                  </el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" :disabled="!hasInsertPermission" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>新增资产
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData">
          <el-icon>
            <Download />
          </el-icon> 导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="userArr" ref="table" border stripe v-loading="loading" table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="id" label="主键" v-if="false"></el-table-column>
        <!-- 管理IP -->
        <el-table-column prop="management_ip" label="管理IP" sortable></el-table-column>

        <!-- 主机名 -->
        <el-table-column prop="hostname" label="主机名" sortable></el-table-column>

        <!-- 功能用途 -->
        <el-table-column prop="function_purpose" label="功能用途" sortable></el-table-column>

        <!-- 管理员1 -->
        <el-table-column prop="server_admin1" label="设备管理员" sortable></el-table-column>

        <!-- 系统管理员 -->
        <el-table-column prop="system_administrator" label="系统管理员" sortable></el-table-column>

        <!-- 生命周期 -->
        <el-table-column prop="operation_status" label="设备生命周期" sortable>
          <template #default="scope">
            <el-tag :type="getLifecycleTagType(scope.row.operation_status)">
              {{ scope.row.operation_status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 系统运行状态 -->
        <el-table-column prop="system_operation_status" label="系统运行状态" sortable>
          <template #default="scope">
            <el-tag :type="getSystemOperationStatusTagType(scope.row.system_operation_status)">
              {{ scope.row.system_operation_status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- PING状态 -->
        <el-table-column prop="machine_usage_status" label="PING状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.machine_usage_status === '在线' ? 'success' : 'danger'">
              {{ scope.row.machine_usage_status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 离线备注 -->
        <el-table-column prop="remarks" label="离线备注" sortable></el-table-column>        

        <!-- 所属机房 -->
        <el-table-column prop="data_center" label="所属机房" sortable></el-table-column>

        <!-- 归属业务系统 -->
        <el-table-column prop="business_system_name" label="归属业务系统" sortable></el-table-column>

        <!-- 系统分级 -->
        <el-table-column prop="system_classification" label="系统分级" sortable></el-table-column>

        <!-- 需要监控 -->
        <el-table-column prop="monitoring_requirement" label="需要监控" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.monitoring_requirement === '是' ? 'success' : 'warning'">
              {{ scope.row.monitoring_requirement }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 不监控原因 -->
        <el-table-column prop="monitoring_requirement_description" label="不监控原因" sortable></el-table-column>

        <!-- 监控状态 -->
        <el-table-column prop="is_monitored" label="监控状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_monitored === '是' ? 'success' : 'danger'">
              {{ scope.row.is_monitored }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 生产属性 -->
        <el-table-column prop="production_attributes" label="生产属性" sortable></el-table-column>

        <!-- 主从角色 -->
        <el-table-column prop="master_slave_role" label="主从角色" sortable></el-table-column>

        <!-- 关联主从机IP -->
        <el-table-column prop="related_master_slave_ips" label="关联主从机IP" sortable></el-table-column>

        <!-- 备份模式 -->
        <!-- <el-table-column prop="backup_mode" label="备份模式" sortable></el-table-column> -->

        <!-- 互联网IP -->
        <el-table-column prop="internet_ip" label="互联网IP" sortable></el-table-column>

        <!-- 互联网端口 -->
        <el-table-column prop="internet_port" label="互联网端口" sortable></el-table-column>

        <!-- 操作系统 -->
        <el-table-column prop="operating_system" label="操作系统" sortable></el-table-column>

        <!-- 是否虚拟机 -->
        <el-table-column prop="is_virtual_machine" label="是否虚拟机" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_virtual_machine === '是' ? 'success' : 'info'">
              {{ scope.row.is_virtual_machine }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 是否安装杀毒软件 -->
        <!-- <el-table-column prop="has_antivirus_software" label="是否安装杀毒软件" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.has_antivirus_software === '是' ? 'success' : 'danger'
              ">
              {{ scope.row.has_antivirus_software }}
            </el-tag>
          </template>
        </el-table-column> -->

        <el-table-column prop="created_at" label="创建时间" sortable></el-table-column>
        <el-table-column prop="created_by" label="创建人" sortable></el-table-column>
        <el-table-column prop="updated_at" label="更新时间" sortable></el-table-column>
        <el-table-column prop="updated_by" label="更新人" sortable></el-table-column>
        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap">
              <el-button size="small" type="warning" :disabled="!hasUpdatePermission"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button size="small" type="danger" :disabled="!hasDeletePermission"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination background :current-page="search.currentPage" :page-size="search.pageSize" :total="search.total"
          :page-sizes="[10, 20, 50, 100, 1000, 10000]" :pager-count="5" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { Plus, Search, Download } from "@element-plus/icons-vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export default {
  components: {
    Plus,
    Search,
    Download,
  },

  data() {
    return {
      userArr: [], // 监控IP列表
      loading: false, // 加载状态
      productionattributes: [], //数据字典值
      masterslaveroles: [], //数据字典值
      backupmodes: [], //数据字典值
      systemclassifications: [],//数据字典值
      datacenters: [],//数据字典值
      operationstatuses: [], //数据字典值 - 生命周期状态
      systemoperationstatuses: [], //数据字典值 - 系统运行状态
      operatingsystems: [], //数据字典值 - 操作系统
      businessSystems: [], // 业务系统列表
      usersList: [], // 用户列表

      hasDeletePermission: localStorage.getItem("role_code")?.includes("D"), // 是否有删除权限
      hasUpdatePermission: localStorage.getItem("role_code")?.includes("U"), // 是否有删除权限
      hasInsertPermission: localStorage.getItem("role_code")?.includes("I"), // 是否有删除权限

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false,
        delete: false,
      },
      // 查询数据
      search: {
        management_ip: "",
        hostname: "", // 公式自动获取，禁用输入
        function_purpose: "", // 功能用途
        server_admin1: "", // 公式自动获取，禁用输入
        data_center: "", // 公式自动获取，禁用输入
        machine_usage_status: "", // 公式自动获取，禁用输入
        business_system_name: "",
        system_administrator: "", // 系统管理员搜索字段
        system_classification: "", // 公式自动获取，禁用输入
        system_operation_status: "", // 新增：系统运行状态搜索字段
        monitoring_requirement: "", // 新增：需要监控搜索字段
        is_monitored: "", // 默认值
        production_attributes: "",
        operation_status: "", // 生命周期搜索字段
        operating_system: "", // 操作系统搜索字段

        total: 0, // 总记录数
        pageSize: 10, // 每页显示条目数
        currentPage: 1, // 当前页码
        sortProp: "updated_at", // 排序字段
        sortOrder: "desc", // 排序顺序
      },

      // 自动完成选项
      autoCompleteOptions: {
        management_ip: [],
        hostname: [],
        function_purpose: [],
        business_system_name: [],
        internet_ip: [],
        internet_port: [],
        remarks: []
      },

      // 表单数据
      formData: {
        id: null,
        management_ip: "",
        hostname: "", // 公式自动获取，禁用输入
        function_purpose: "", // 公式自动获取，禁用输入
        server_admin1: "", // 公式自动获取，禁用输入
        server_admin2: "", // 公式自动获取，禁用输入
        data_center: "", // 公式自动获取，禁用输入
        machine_usage_status: "", // 公式自动获取，禁用输入
        remarks: "",
        business_system_name: "",
        system_administrator: "", // 公式自动获取，禁用输入
        system_classification: "", // 公式自动获取，禁用输入
        monitoring_requirement: "是", // 新增：需要监控字段，默认值为"是"
        monitoring_requirement_description: "", // 新增：不监控原因字段
        is_monitored: "", // 默认值
        production_attributes: "",
        master_slave_role: "",
        related_master_slave_ips: "", // 关联主从机IP
        backup_mode: "",
        internet_ip: "",
        internet_port: "",
        operating_system: "", // 公式自动获取，禁用输入
        has_antivirus_software: "否", // 默认值
      },
      // 表单验证规则
      rules: {
        management_ip: [
          { required: true, message: '请输入管理IP', trigger: 'blur' },
          { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
        ],
        business_system_name: [
          { required: true, message: '请选择归属业务系统', trigger: 'change' }
        ],
        master_slave_role: [
          { required: true, message: '请选择主从角色', trigger: 'change' }
        ],
        production_attributes: [
          { required: true, message: '请选择生产属性', trigger: 'change' }
        ],
        monitoring_requirement: [
          { required: true, message: '请选择需要监控', trigger: 'change' }
        ],
        monitoring_requirement_description: [
          {
            required: false,
            validator: (_, value, callback) => {
              if (this.formData.monitoring_requirement === '否') {
                if (!value || value.trim() === '') {
                  callback(new Error('当需要监控为"否"时，不监控原因为必填项'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        related_master_slave_ips: [
          {
            required: false,
            validator: (_, value, callback) => {
              if (this.formData.master_slave_role && !this.isSingleMachine(this.formData.master_slave_role)) {
                if (!value) {
                  callback(new Error('当主从角色不为单机时，关联主从机IP为必填项'));
                } else {
                  // 验证IP格式，支持多个IP用英文逗号分隔
                  const ips = value.split(',');
                  const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
                  const invalidIps = ips.filter(ip => !ipPattern.test(ip.trim()));

                  if (invalidIps.length > 0) {
                    callback(new Error('请输入正确的IP地址格式，多个IP请用英文逗号分隔'));
                  } else {
                    callback();
                  }
                }
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
    };
  },
  mounted() {
    // 检查URL参数中是否有search_ip
    if (this.$route.query.search_ip) {
      this.search.management_ip = this.$route.query.search_ip;
    }

    this.loadData();

    this.getDatadict("C", "productionattributes");
    this.getDatadict("F", "masterslaveroles");
    this.getDatadict("G", "backupmodes");
    this.getDatadict("A", "datacenters");
    this.getDatadict("I", "systemclassifications");
    this.getDatadict("D", "operationstatuses"); // 新增：加载生命周期数据字典
    this.getDatadict("H", "systemoperationstatuses"); // 新增：加载系统运行状态数据字典
    this.getDatadict("K", "operatingsystems"); // 新增：加载操作系统数据字典

    // 获取业务系统列表
    this.getBusinessSystems();
    // 加载用户列表
    this.loadUsersList();
    
    // 初始化自动完成选项
    this.initAutoCompleteOptions();

    // 检查是否来自发现结果页面
    if (this.$route.query.from_discovery === 'true') {
      this.$nextTick(() => {
        this.handleAddFromDiscovery();
      });
    }
  },
  methods: {
    // 获取生命周期标签类型（5个状态：正常、故障、闲置、报废、预报废）
    getLifecycleTagType(status) {
      switch (status) {
        case '正常':
          return 'success';    // 绿色 - 设备正常运行
        case '故障':
          return 'danger';     // 红色 - 设备出现故障
        case '闲置':
          return 'info';       // 蓝色 - 设备暂时不使用但可用
        case '报废':
          return 'info';       // 灰色 - 设备已报废，不再使用
        case '预报废':
          return 'warning';    // 橙色 - 设备即将报废
        default:
          // 兼容其他可能的状态表述
          if (status && status.includes('正常')) {
            return 'success';
          }
          if (status && status.includes('故障')) {
            return 'danger';
          }
          if (status && status.includes('闲置')) {
            return 'info';
          }
          if (status && (status.includes('报废') && !status.includes('预'))) {
            return 'info';
          }
          if (status && status.includes('预报废')) {
            return 'warning';
          }
          // 默认为信息色
          return 'info';
      }
    },

    // 获取系统运行状态标签类型（3个状态：建设中、运行中、已下线）
    getSystemOperationStatusTagType(status) {
      switch (status) {
        case '在用':
          return 'success';    // 绿色 - 系统在用
        case '下线':
          return 'danger';     // 红色 - 系统已下线
        case '归档':
          return 'info';       // 蓝色 - 系统已归档
        default:
          // 兼容其他可能的状态表述
          if (status && (status.includes('在用') || status.includes('运行'))) {
            return 'success';
          }
          if (status && status.includes('下线')) {
            return 'danger';
          }
          if (status && status.includes('归档')) {
            return 'info';
          }
          // 默认为信息色
          return 'info';
      }
    },

    // 页码选择
    handlePageChange(newPage) {
      this.search.currentPage = newPage;
      this.loadData();
    },

    // 每页显示条目数变化
    handlePageSizeChange(newSize) {
      this.search.pageSize = parseInt(newSize);
      this.search.currentPage = 1; // 重置当前页码为第一页
      this.loadData();
    },
    //增加排序
    handleSortChange({ prop, order }) {
      this.search.sortProp = prop;
      this.search.sortOrder = order === "ascending" ? "asc" : "desc";
      this.loadData();
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        const response = await this.$axios.post(
          `/api/get_cmdb_application_system_info`,
          this.search
        );
        this.userArr = response.data.msg;
        this.search.total = response.data.total;
        
        // 更新自动完成选项
        this.updateAutoCompleteOptions();
      } catch (error) {
        this.$message.error("数据加载失败");
      } finally {
        this.loading = false;
      }
    },

    // 更新自动完成选项
    updateAutoCompleteOptions() {
      // 从当前数据中提取唯一值作为自动完成选项
      const fields = ['management_ip', 'hostname', 'function_purpose'];
      
      fields.forEach(field => {
        const uniqueValues = [...new Set(
          this.userArr
            .map(item => item[field])
            .filter(value => value && value.trim() !== '')
        )];
        
        this.autoCompleteOptions[field] = uniqueValues.map(value => ({
          value: value
        }));
      });
    },

    // 自动完成查询方法
    querySearchAsync(queryString, callback, field) {
      const options = this.autoCompleteOptions[field] || [];
      
      if (!queryString) {
        callback(options.slice(0, 10)); // 限制显示前10个选项
        return;
      }
      
      // 过滤匹配的选项
      const filteredOptions = options.filter(option => 
        option.value.toLowerCase().includes(queryString.toLowerCase())
      );
      
      callback(filteredOptions.slice(0, 10)); // 限制显示前10个匹配项
    },

    // 处理选择事件
    handleSelect(item) {
      // 可以在这里添加选择后的处理逻辑
      console.log('Selected:', item);
    },

    // 验证并添加管理IP
    async validateAndSubmitAdd() {
      try {
        // 表单验证
        await this.$refs.addFormRef.validate();

        // 验证通过，提交表单
        await this.submitAdd();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
      }
    },

    // 添加管理IP
    async submitAdd() {
      try {
        // 添加参数，用于记录创建者
        const requestData = {
          ...this.formData,
          username: localStorage.getItem("loginUsername") || "unknown"
        };

        await this.$axios.post(
          `/api/add_cmdb_application_system_info`,
          requestData
        );
        this.$message.success("添加成功");
        this.dialogVisible.add = false;
        this.loadData();
      } catch (error) {
        this.$message.error(error.response?.data?.msg || "添加失败");
      }
    },

    // 验证并编辑管理IP
    async validateAndSubmitEdit() {
      try {
        // 表单验证
        await this.$refs.editFormRef.validate();

        // 验证通过，提交表单
        await this.submitEdit();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
      }
    },

    // 编辑管理IP
    async submitEdit() {
      try {
        // 添加参数，用于记录更新者
        const requestData = {
          ...this.formData,
          username: localStorage.getItem("loginUsername") || "unknown"
        };

        await this.$axios.post(
          `/api/update_cmdb_application_system_info`,
          requestData
        );
        this.$message.success("更新成功");
        this.dialogVisible.edit = false;
        this.loadData();
      } catch (error) {

        this.$message.error(error.response?.data?.msg || "更新失败");
      }
    },

    // 删除监控IP
    async submitDelete() {
      try {
        await this.$axios.post(
          `/api/del_cmdb_application_system_info`,
          this.formData
        );
        this.$message.success("删除成功");
        this.loadData();
        this.dialogVisible.delete = false;
      } catch (error) {

        this.$message.error("删除失败");
      }
    },

    // 重置搜索条件
    resetSearch() {
      this.search = {
        management_ip: "",
        hostname: "",
        function_purpose: "",
        server_admin1: "",
        data_center: "",
        machine_usage_status: "",
        business_system_name: "",
        system_administrator: "", // 重置系统管理员搜索字段
        system_classification: "",
        system_operation_status: "", // 新增：重置系统运行状态搜索字段
        monitoring_requirement: "", // 新增：重置需要监控搜索字段
        is_monitored: "",
        production_attributes: "",
        operation_status: "", // 生命周期搜索字段
        operating_system: "", // 操作系统搜索字段
        is_virtual_machine: "", // 是否虚拟机搜索字段
        total: 0,
        pageSize: 10,
        currentPage: 1,
        sortProp: "updated_at",
        sortOrder: "desc",
      };
      this.loadData();
    },

    // 得到数据字典
    async getDatadict(dictCode, targetArray) {
      try {
        const response = await this.$axios.post(
          `/api/get_cmdb_data_dictionary`,
          {
            dict_code: dictCode,
          }
        );
        this[targetArray] = response.data.msg;


      } catch (error) {

        this.$message.error("数据加载失败");
      }
    },

    // 新增效果实现
    handleAdd() {
      this.dialogVisible.add = !this.dialogVisible.add;
      this.formData = {
        monitoring_requirement: "是", // 新增：需要监控默认值
        monitoring_requirement_description: "", // 新增：不监控原因
        has_antivirus_software: "否", // 默认值
        master_slave_role: "" // 必须手动选择
      };
      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
          // 确保关联主从机IP字段不会被标记为必填
          this.$refs.addFormRef.clearValidate('related_master_slave_ips');
        });
      }
    },

    // 编辑按钮实现
    handleEdit(_, row) {
      this.dialogVisible.edit = true;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
      this.formData.hostname = row.hostname;
      this.formData.function_purpose = row.function_purpose;
      this.formData.server_admin1 = row.server_admin1;
      this.formData.server_admin2 = row.server_admin2;
      // 所属机房：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.data_center = row.data_center_code || row.data_center;
      this.formData.machine_usage_status = row.machine_usage_status;
      this.formData.remarks = row.remarks;
      this.formData.business_system_name = row.business_system_name;
      this.formData.system_administrator = row.system_administrator;
      this.formData.system_classification = row.system_classification;
      this.formData.monitoring_requirement = row.monitoring_requirement; // 新增：需要监控
      this.formData.monitoring_requirement_description = row.monitoring_requirement_description; // 新增：不监控原因
      this.formData.is_monitored = row.is_monitored;
      // 生产属性：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.production_attributes = row.production_attributes_code || row.production_attributes;
      this.formData.master_slave_role = row.master_slave_role;
      this.formData.related_master_slave_ips = row.related_master_slave_ips;
      this.formData.backup_mode = row.backup_mode;
      this.formData.internet_ip = row.internet_ip;
      this.formData.internet_port = row.internet_port;
      this.formData.operating_system = row.operating_system;
      this.formData.has_antivirus_software = row.has_antivirus_software;

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.editFormRef) {
        this.$nextTick(() => {
          this.$refs.editFormRef.clearValidate();
          // 如果主从角色为单机，确保关联主从机IP字段不会被标记为必填
          if (this.isSingleMachine(this.formData.master_slave_role)) {
            this.$refs.editFormRef.clearValidate('related_master_slave_ips');
          }
          // 手动触发一次验证，确保表单状态正确
          this.validateRelatedMasterSlaveIps();
        });
      }
    },

    // 删除效果实现
    handleDelete(_, row) {
      this.dialogVisible.delete = !this.dialogVisible.delete;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
    },
    //导出数据
    exportData() {
      const table = this.$refs.table; // 获取 el-table 实例
      const columns = table.columns; // 获取表头
      const headers = columns.map((col) => col.label); // 获取表头
      const data = this.userArr.map((row) =>
        columns.map((col) => row[col.property])
      ); // 获取表格数据

      const wsData = [headers, ...data]; // 将表头和数据合并
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const blob = new Blob([wbout], { type: "application/octet-stream" });
      saveAs(blob, "应用系统信息.xlsx");
    },

    // 获取单机对应的字典值
    getSingleMachineCode() {
      const singleMachine = this.masterslaveroles.find(item => item.dict_name === "单机");
      return singleMachine ? singleMachine.dict_code : 'F00047'; // F00047 是单机 ，F00045 是主机 ，F00046 是从机
    },

    // 检查主从角色是否为单机（支持代码值或名称）
    isSingleMachine(value) {
      if (!value) return false;

      // 如果值是字典代码
      const singleMachineCode = this.getSingleMachineCode();
      if (value === singleMachineCode) return true;

      // 如果值是字典名称
      const isSingleMachineName = value === "单机";
      return isSingleMachineName;
    },

    // 判断关联主从机IP是否必填
    getSingleMachineRequired() {
      return this.formData.master_slave_role && !this.isSingleMachine(this.formData.master_slave_role);
    },

    // 获取业务系统列表
    async getBusinessSystems() {
      try {
        const response = await this.$axios.post(
          `/api/get_cmdb_system_admin_responsibility_company`,
          {
            currentPage: 1,
            pageSize: 1000, // 获取足够多的记录
            sortProp: "system_abbreviation",
            sortOrder: "asc"
          }
        );

        if (response.data && response.data.msg) {
          // 提取系统简称作为业务系统名称选项
          this.businessSystems = response.data.msg.filter(item => item.system_abbreviation).map(item => ({
            value: item.system_abbreviation,
            label: item.system_abbreviation
          }));

          // 去重
          this.businessSystems = this.businessSystems.filter((item, index, self) =>
            index === self.findIndex(t => t.value === item.value)
          );


        }
      } catch (error) {

        this.$message.error("获取业务系统列表失败");
      }
    },

    // 加载用户列表
    async loadUsersList() {
      try {
        const response = await this.$axios.post('/api/get_all_users_real_name');
        this.usersList = response.data.msg;

      } catch (error) {

        this.$message.error('用户列表加载失败');
      }
    },

    // 处理需要监控变化的方法
    handleMonitoringRequirementChange(value) {
      // 当需要监控为"是"时，清空不监控原因并禁用输入
      if (value === '是') {
        this.formData.monitoring_requirement_description = '';
        // 清除不监控原因的验证错误
        this.$nextTick(() => {
          if (this.$refs.addFormRef) {
            this.$refs.addFormRef.clearValidate('monitoring_requirement_description');
          }
          if (this.$refs.editFormRef) {
            this.$refs.editFormRef.clearValidate('monitoring_requirement_description');
          }
        });
      }
      // 当需要监控为"否"时，需要验证不监控原因
      else if (value === '否') {
        this.$nextTick(() => {
          if (this.$refs.addFormRef) {
            this.$refs.addFormRef.validateField('monitoring_requirement_description');
          }
          if (this.$refs.editFormRef) {
            this.$refs.editFormRef.validateField('monitoring_requirement_description');
          }
        });
      }
    },

    // 验证关联主从机IP
    validateRelatedMasterSlaveIps() {
      // 当主从角色变化时，重新验证关联主从机IP字段

      // 更新表单中关联主从机IP字段的required属性
      this.$nextTick(() => {
        if (this.$refs.addFormRef) {
          // 如果选择了单机，清除验证错误
          if (this.isSingleMachine(this.formData.master_slave_role)) {
            this.$refs.addFormRef.clearValidate('related_master_slave_ips');
          } else {
            this.$refs.addFormRef.validateField('related_master_slave_ips');
          }
        }
        if (this.$refs.editFormRef) {
          // 如果选择了单机，清除验证错误
          if (this.isSingleMachine(this.formData.master_slave_role)) {
            this.$refs.editFormRef.clearValidate('related_master_slave_ips');
          } else {
            this.$refs.editFormRef.validateField('related_master_slave_ips');
          }
        }
      });
    },

    // 处理从发现结果页面传递的参数
    handleAddFromDiscovery() {
      // 打开添加对话框
      this.dialogVisible.add = true;

      // 从 URL 参数中获取数据
      const { ip_address, hostname, open_ports } = this.$route.query;

      // 只填充管理IP
      this.formData = {
        management_ip: ip_address || '',
        hostname: hostname || '',
        business_system_name: '',
        remarks: '',
        monitoring_requirement: "是", // 默认需要监控为"是"
        monitoring_requirement_description: "",
        has_antivirus_software: '否',
        master_slave_role: '' // 必须手动选择
      };

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
          // 确保关联主从机IP字段不会被标记为必填
          this.$refs.addFormRef.clearValidate('related_master_slave_ips');
          // 显示提示消息
          this.$message.info('请完善应用系统信息并提交');
        });
      }

      // 清除URL参数，避免刷新页面时重复打开对话框
      this.$router.replace({ path: this.$route.path });
    },

    // 初始化自动完成选项
    async initAutoCompleteOptions() {
      try {
        // 获取所有数据用于自动完成
        const response = await this.$axios.post(
          `/api/get_cmdb_application_system_info`,
          {
            currentPage: 1,
            pageSize: 10000, // 获取大量数据用于自动完成
            sortProp: "updated_at",
            sortOrder: "desc"
          }
        );
        
        if (response.data && response.data.msg) {
          const allData = response.data.msg;
          
          // 提取各字段的唯一值
          const fields = ['management_ip', 'hostname', 'function_purpose'];
          
          fields.forEach(field => {
            const uniqueValues = [...new Set(
              allData
                .map(item => item[field])
                .filter(value => value && value.trim() !== '')
            )];
            
            this.autoCompleteOptions[field] = uniqueValues.map(value => ({
              value: value
            }));
          });
        }
      } catch (error) {
        console.error('初始化自动完成选项失败:', error);
      }
    },
  },
};
</script>

<style scoped>
/* 统一操作按钮区样式 */
.unified-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: nowrap;
}

.action-bar-left,
.action-bar-right {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 按钮容器 */
.button-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

/* 搜索按钮对齐 */
.search-buttons-col {
  display: flex;
  align-items: center;
}

.search-buttons {
  margin-bottom: 0;
  text-align: right;
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-bar-left,
  .action-bar-right {
    margin-bottom: 8px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-bar-right {
    justify-content: flex-end;
  }
}
</style>