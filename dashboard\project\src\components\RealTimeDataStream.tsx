import React, { useState, useEffect, useRef } from 'react';
import { Activity, Zap, Database, Network } from 'lucide-react';

interface StreamData {
  id: string;
  type: 'cpu' | 'memory' | 'network' | 'disk';
  value: number;
  timestamp: number;
  status: 'normal' | 'warning' | 'critical';
}

interface RealTimeDataStreamProps {
  title: string;
  className?: string;
  animationDelay?: number;
  maxDataPoints?: number;
}

const RealTimeDataStream: React.FC<RealTimeDataStreamProps> = ({
  title,
  className = '',
  animationDelay = 0,
  maxDataPoints = 50
}) => {
  const [streamData, setStreamData] = useState<StreamData[]>([]);
  const [isActive, setIsActive] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  // 生成模拟数据
  const generateData = (): StreamData => {
    const types: StreamData['type'][] = ['cpu', 'memory', 'network', 'disk'];
    const type = types[Math.floor(Math.random() * types.length)];
    const value = Math.random() * 100;
    const status: StreamData['status'] = 
      value > 80 ? 'critical' : 
      value > 60 ? 'warning' : 'normal';

    return {
      id: Math.random().toString(36).substr(2, 9),
      type,
      value,
      timestamp: Date.now(),
      status
    };
  };

  // 获取类型图标
  const getTypeIcon = (type: StreamData['type']) => {
    switch (type) {
      case 'cpu': return <Activity className="w-4 h-4" />;
      case 'memory': return <Database className="w-4 h-4" />;
      case 'network': return <Network className="w-4 h-4" />;
      case 'disk': return <Zap className="w-4 h-4" />;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: StreamData['status']) => {
    switch (status) {
      case 'normal': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
    }
  };

  // 绘制实时波形图
  const drawWaveform = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    ctx.clearRect(0, 0, width, height);

    // 绘制网格
    ctx.strokeStyle = 'rgba(0, 212, 255, 0.1)';
    ctx.lineWidth = 1;
    
    // 垂直网格线
    for (let x = 0; x < width; x += 40) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // 水平网格线
    for (let y = 0; y < height; y += 30) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // 绘制数据线
    if (streamData.length > 1) {
      const stepX = width / (maxDataPoints - 1);
      
      // 按类型分组绘制
      const typeColors = {
        cpu: '#00d4ff',
        memory: '#10b981',
        network: '#f59e0b',
        disk: '#8b5cf6'
      };

      Object.entries(typeColors).forEach(([type, color]) => {
        const typeData = streamData.filter(d => d.type === type);
        if (typeData.length < 2) return;

        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        
        ctx.beginPath();
        typeData.forEach((data, index) => {
          const x = index * stepX;
          const y = height - (data.value / 100) * height;
          
          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        ctx.stroke();
        
        ctx.shadowBlur = 0;
      });
    }

    // 绘制扫描线
    const scanX = (Date.now() / 20) % width;
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.6)';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(scanX, 0);
    ctx.lineTo(scanX, height);
    ctx.stroke();
    ctx.setLineDash([]);
  };

  // 动画循环
  const animate = () => {
    drawWaveform();
    animationRef.current = requestAnimationFrame(animate);
  };

  useEffect(() => {
    // 启动数据生成
    const dataInterval = setInterval(() => {
      if (isActive) {
        setStreamData(prev => {
          const newData = [...prev, generateData()];
          return newData.slice(-maxDataPoints);
        });
      }
    }, 500);

    // 启动动画
    animate();

    return () => {
      clearInterval(dataInterval);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, maxDataPoints]);

  return (
    <div
      className={`glass-card rounded-xl p-6 hover-lift entrance-animation ${className}`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      {/* 标题栏 */}
      <div className="flex flex-col items-center mb-4">
        <h3 className="text-lg font-bold tech-gradient-text text-center mb-2">{title}</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsActive(!isActive)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              isActive ? 'bg-neon-green pulse-glow' : 'bg-gray-500'
            }`}
          />
          <span className="text-xs text-gray-400">
            {isActive ? '实时' : '暂停'}
          </span>
        </div>
      </div>

      {/* 波形图 */}
      <div className="relative mb-4">
        <canvas
          ref={canvasRef}
          width={400}
          height={120}
          className="w-full h-30 rounded-lg bg-tech-darker/50"
        />
        
        {/* 数据流指示器 */}
        <div className="absolute top-2 right-2 flex space-x-1">
          {['cpu', 'memory', 'network', 'disk'].map((type, index) => (
            <div
              key={type}
              className="w-2 h-2 rounded-full opacity-60"
              style={{
                backgroundColor: {
                  cpu: '#00d4ff',
                  memory: '#10b981',
                  network: '#f59e0b',
                  disk: '#8b5cf6'
                }[type as keyof typeof getTypeIcon],
                animation: `particleTwinkle 2s ease-in-out infinite`,
                animationDelay: `${index * 0.3}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* 实时数据列表 */}
      <div className="space-y-2 max-h-32 overflow-y-auto scrollbar-tech">
        {streamData.slice(-6).reverse().map((data, index) => (
          <div
            key={data.id}
            className="flex items-center justify-between p-2 rounded-lg bg-tech-surface/30 hover:bg-tech-surface/50 transition-all duration-200"
            style={{
              animation: `slideUp 0.3s ease-out`,
              animationDelay: `${index * 50}ms`
            }}
          >
            <div className="flex items-center space-x-3">
              <div
                className="p-1 rounded"
                style={{
                  color: getStatusColor(data.status),
                  backgroundColor: `${getStatusColor(data.status)}20`
                }}
              >
                {getTypeIcon(data.type)}
              </div>
              <div>
                <div className="text-sm font-medium text-white">
                  {data.type.toUpperCase()}
                </div>
                <div className="text-xs text-gray-400">
                  {new Date(data.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div
                className="text-sm font-bold"
                style={{ color: getStatusColor(data.status) }}
              >
                {data.value.toFixed(1)}%
              </div>
              <div
                className="text-xs px-2 py-1 rounded"
                style={{
                  backgroundColor: `${getStatusColor(data.status)}20`,
                  color: getStatusColor(data.status)
                }}
              >
                {data.status}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 统计信息 */}
      <div className="mt-4 pt-4 border-t border-gray-700/50">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-xs text-gray-400">数据点</div>
            <div className="text-sm font-bold text-neon-blue">
              {streamData.length}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400">平均值</div>
            <div className="text-sm font-bold text-neon-green">
              {streamData.length > 0 
                ? (streamData.reduce((sum, d) => sum + d.value, 0) / streamData.length).toFixed(1)
                : '0.0'
              }%
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400">告警数</div>
            <div className="text-sm font-bold text-neon-orange">
              {streamData.filter(d => d.status !== 'normal').length}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeDataStream;