import React from 'react';

interface ChinaMapSVGProps {
  className?: string;
  fillColor?: string;
  strokeColor?: string;
  strokeWidth?: number;
}

// 主要城市坐标数据
const majorCities = [
  { name: '北京', x: 65, y: 20, isCapital: true },
  { name: '上海', x: 75, y: 35, isCapital: false },
  { name: '广州', x: 68, y: 65, isCapital: false },
  { name: '深圳', x: 70, y: 68, isCapital: false },
  { name: '武汉', x: 67, y: 42, isCapital: false },
  { name: '成都', x: 58, y: 45, isCapital: false },
  { name: '郑州', x: 64, y: 38, isCapital: false },
  { name: '大连', x: 71, y: 18, isCapital: false },
  { name: '杭州', x: 72, y: 40, isCapital: false },
  { name: '南京', x: 73, y: 32, isCapital: false },
  { name: '西安', x: 59, y: 38, isCapital: false },
  { name: '重庆', x: 59, y: 48, isCapital: false },
  { name: '天津', x: 66, y: 22, isCapital: false },
  { name: '青岛', x: 70, y: 28, isCapital: false },
  { name: '沈阳', x: 69, y: 15, isCapital: false }
];

const ChinaMapSVG: React.FC<ChinaMapSVGProps> = ({
  className = '',
  strokeColor = 'rgba(0, 212, 255, 0.25)' }) => {
  return (
    <svg
      viewBox="0 0 1000 600"
      className={`w-full h-full ${className}`}
    >
      <defs>
        <filter id="textGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>

        <radialGradient id="cityDot" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="rgba(0, 212, 255, 0.8)" />
          <stop offset="100%" stopColor="rgba(0, 212, 255, 0.3)" />
        </radialGradient>

        <radialGradient id="capitalDot" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="rgba(255, 215, 0, 0.9)" />
          <stop offset="100%" stopColor="rgba(255, 215, 0, 0.4)" />
        </radialGradient>
      </defs>

      {/* 背景网格 - 科技感 */}
      <defs>
        <pattern id="techGrid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
          <path
            d="M 40 0 L 0 0 0 40"
            fill="none"
            stroke="rgba(0, 212, 255, 0.1)"
            strokeWidth="0.5"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#techGrid)" opacity="0.3" />

      {/* 城市标记和地名 */}
      {majorCities.map((city, index) => (
        <g key={city.name} className="city-marker">
          {/* 城市光点 */}
          <circle
            cx={city.x * 10}
            cy={city.y * 10}
            r={city.isCapital ? 8 : 5}
            fill={city.isCapital ? "url(#capitalDot)" : "url(#cityDot)"}
            className="transition-all duration-300 hover:scale-125"
            style={{
              filter: `drop-shadow(0 0 ${city.isCapital ? 12 : 8}px ${city.isCapital ? 'rgba(255, 215, 0, 0.6)' : 'rgba(0, 212, 255, 0.6)'})`,
              animation: `cityPulse 2s ease-in-out infinite ${index * 0.2}s`
            }}
          />

          {/* 脉冲圆环 */}
          <circle
            cx={city.x * 10}
            cy={city.y * 10}
            r={city.isCapital ? 12 : 8}
            fill="none"
            stroke={city.isCapital ? "rgba(255, 215, 0, 0.4)" : "rgba(0, 212, 255, 0.4)"}
            strokeWidth="1"
            className="animate-ping"
            style={{
              animationDelay: `${index * 0.3}s`,
              animationDuration: '3s'
            }}
          />

          {/* 城市名称 */}
          <text
            x={city.x * 10 + (city.isCapital ? 12 : 8)}
            y={city.y * 10 - (city.isCapital ? 8 : 5)}
            fontSize={city.isCapital ? "14" : "12"}
            fill={city.isCapital ? "#FFD700" : strokeColor}
            fontWeight={city.isCapital ? "bold" : "normal"}
            filter="url(#textGlow)"
            className="transition-all duration-300 hover:scale-110"
            style={{
              fontFamily: 'system-ui, -apple-system, sans-serif',
              textShadow: `0 0 ${city.isCapital ? 8 : 6}px ${city.isCapital ? 'rgba(255, 215, 0, 0.5)' : 'rgba(0, 212, 255, 0.5)'}`,
              userSelect: 'none'
            }}
          >
            {city.name}
          </text>

          {/* 连接线 - 从光点到文字 */}
          <line
            x1={city.x * 10}
            y1={city.y * 10}
            x2={city.x * 10 + (city.isCapital ? 12 : 8)}
            y2={city.y * 10 - (city.isCapital ? 8 : 5)}
            stroke={city.isCapital ? "rgba(255, 215, 0, 0.3)" : "rgba(0, 212, 255, 0.3)"}
            strokeWidth="1"
            strokeDasharray="2,2"
            className="transition-opacity duration-300 opacity-60 hover:opacity-100"
          />
        </g>
      ))}

      {/* 装饰性扫描线 */}
      <line
        x1="0"
        y1="300"
        x2="1000"
        y2="300"
        stroke="rgba(0, 212, 255, 0.2)"
        strokeWidth="1"
        strokeDasharray="5,5"
        className="animate-pulse"
      />

      <line
        x1="500"
        y1="0"
        x2="500"
        y2="600"
        stroke="rgba(0, 212, 255, 0.2)"
        strokeWidth="1"
        strokeDasharray="5,5"
        className="animate-pulse"
        style={{ animationDelay: '1s' }}
      />

      {/* 角落装饰 */}
      <g stroke="rgba(0, 212, 255, 0.3)" fill="none" strokeWidth="2">
        {/* 左上角 */}
        <path d="M 20 20 L 20 60 M 20 20 L 60 20" />
        {/* 右上角 */}
        <path d="M 980 20 L 980 60 M 980 20 L 940 20" />
        {/* 左下角 */}
        <path d="M 20 580 L 20 540 M 20 580 L 60 580" />
        {/* 右下角 */}
        <path d="M 980 580 L 980 540 M 980 580 L 940 580" />
      </g>

      <style>
        {`
        @keyframes cityPulse {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.1);
          }
        }
        
        .city-marker:hover text {
          fill: #00ffff !important;
        }
        
        .city-marker:hover circle {
          filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.8)) !important;
        }
        `}
      </style>
    </svg>
  );
};

export default ChinaMapSVG;