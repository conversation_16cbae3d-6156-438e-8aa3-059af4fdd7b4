<template>
  <div class="report-center">
    <div class="report-container">
      <el-card class="report-card">
          <template #header>
            <div class="card-header">
              <h3 class="report-title">网络设备年限情况统计</h3>
              <div class="unified-action-bar">
                <div class="action-bar-left">
                  <el-button type="primary" @click="showFilterDialog">
                    <el-icon><Filter /></el-icon>
                    筛选条件
                  </el-button>
                  <el-button type="warning" @click="showFavoritesDialog">
                    <el-icon><Star /></el-icon>
                    收藏列表
                  </el-button>
                </div>
                <div class="action-bar-right">
                  <el-button type="primary" @click="exportData">
                    <el-icon><Download /></el-icon>
                    导出Excel
                  </el-button>
                </div>
              </div>
            </div>
          </template>

          <!-- 当前筛选条件显示 -->
          <div class="filter-tags" v-if="hasActiveFilters">
            <span class="filter-label">当前筛选条件：</span>

            <!-- 机房标签 -->
            <el-tag
              v-for="center in selectedDataCenters"
              :key="center"
              closable
              @close="removeDataCenter(center)"
              class="filter-tag"
              type="success"
            >
              机房: {{ center }}
            </el-tag>

            <!-- 运行状态标签 -->
            <el-tag
              v-for="status in selectedOperationStatuses"
              :key="status"
              closable
              @close="removeOperationStatus(status)"
              class="filter-tag"
              type="warning"
            >
              运行状态: {{ status }}
            </el-tag>

            <!-- 是否信创标签 -->
            <el-tag
              v-if="selectedInnovativeTechFlag"
              closable
              @close="removeInnovativeTechFlag"
              class="filter-tag"
              type="danger"
            >
              是否信创: {{ selectedInnovativeTechFlag }}
            </el-tag>

            <!-- 时间段标签 -->
            <el-tag
              v-for="(period, index) in timePeriods"
              :key="index"
              closable
              @close="removeTimePeriod(index)"
              class="filter-tag"
              type="info"
            >
              时间段: {{ period.label || (formatDate(period.startDate) + ' 至 ' + formatDate(period.endDate)) }}
            </el-tag>

            <el-button link @click="clearAllFilters" class="clear-filters">
              清除所有筛选
            </el-button>
          </div>

          <div class="table-container" v-loading="loading">
            <el-table
              :data="deviceAgeData"
              border
              stripe
              highlight-current-row
              :header-cell-style="{backgroundColor: '#f5f7fa', color: '#606266', fontWeight: 'bold'}"
              ref="deviceAgeTable">
              <el-table-column prop="data_center" label="机房" width="180" align="left" />
              <el-table-column label="全部设备" width="100" align="right">
                <template #default="scope">
                  {{ scope.row.total_count }}
                </template>
              </el-table-column>

              <!-- 动态生成时间段列 -->
              <template v-for="(period, index) in timePeriods" :key="index">
                <el-table-column :label="period.label || (formatDate(period.startDate) + ' 至 ' + formatDate(period.endDate))">
                  <el-table-column :prop="'period_' + index + '_count'" label="数量" width="80" align="right" />
                  <el-table-column label="占比" width="100" align="right">
                    <template #default="scope">
                      <span :class="getPercentageClass(scope.row['period_' + index + '_percentage'], index === timePeriods.length - 1)">
                        {{ scope.row['period_' + index + '_percentage'] }}%
                      </span>
                    </template>
                  </el-table-column>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </el-card>
    </div>

    <!-- 筛选条件对话框 -->
    <el-dialog
      v-model="filterDialogVisible"
      title="设置筛选条件"
      width="650px"
      destroy-on-close
      :close-on-click-modal="false"
      custom-class="custom-dialog"
    >
      <el-form :model="filterForm" label-width="100px">
        <!-- 机房选择 -->
        <el-form-item label="机房">
          <el-select
            v-model="filterForm.dataCenters"
            multiple
            filterable
            placeholder="请选择机房"
            style="width: 100%"
          >
            <el-option
              v-for="item in dataCenterOptions"
              :key="item.data_center"
              :label="item.data_center"
              :value="item.data_center"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 运行状态选择 -->
        <el-form-item label="运行状态">
          <el-select
            v-model="filterForm.operationStatuses"
            multiple
            filterable
            placeholder="请选择运行状态"
            style="width: 100%"
          >
            <el-option
              v-for="item in operationStatusOptions"
              :key="item.operation_status"
              :label="item.operation_status"
              :value="item.operation_status"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 是否信创选择 -->
        <el-form-item label="是否信创">
          <el-select
            v-model="filterForm.innovativeTechFlag"
            filterable
            placeholder="请选择是否信创"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in innovativeTechOptions"
              :key="item.is_innovative_tech"
              :label="item.is_innovative_tech"
              :value="item.is_innovative_tech"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 时间段设置 -->
        <el-form-item label="时间段">
          <div v-for="(period, index) in filterForm.timePeriods" :key="index" class="time-period-row">
            <el-date-picker
              v-model="period.startDate"
              type="date"
              placeholder="开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 140px"
            />
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="period.endDate"
              type="date"
              placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 140px"
            />
            <span class="date-separator">标签:</span>
            <el-input
              v-model="period.label"
              placeholder="自定义标签"
              style="width: 120px"
            />
            <el-button
              type="danger"
              circle
              @click="removeFilterTimePeriod(index)"
              :disabled="filterForm.timePeriods.length <= 1"
              style="margin-left: 10px"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>

          <div class="add-period-row">
            <el-button
              type="primary"
              @click="addTimePeriod"
              :disabled="filterForm.timePeriods.length >= 5"
            >
              <el-icon><Plus /></el-icon>
              添加时间段
            </el-button>
            <span v-if="filterForm.timePeriods.length >= 5" class="period-limit-hint">
              (最多支持5个时间段)
            </span>
          </div>
        </el-form-item>

        <!-- 收藏设置 -->
        <el-form-item label="收藏设置">
          <div class="favorite-row">
            <el-input
              v-model="favoriteForm.name"
              placeholder="输入收藏名称"
              style="width: 200px"
            />
            <el-button
              type="success"
              @click="saveFilterFavorite"
              :disabled="!favoriteForm.name"
              style="margin-left: 10px"
            >
              <el-icon><Star /></el-icon>
              保存收藏
            </el-button>
          </div>
        </el-form-item>

        <!-- 收藏列表 -->
        <el-form-item label="收藏列表" v-if="filterFavorites.length > 0">
          <div class="favorites-list">
            <el-tag
              v-for="favorite in filterFavorites"
              :key="favorite.id"
              class="favorite-tag"
              closable
              @close="deleteFilterFavorite(favorite.id)"
              @click="applyFilterFavorite(favorite)"
            >
              {{ favorite.name }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="filterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 收藏列表对话框 -->
    <el-dialog
      v-model="favoritesDialogVisible"
      title="收藏的筛选条件"
      width="500px"
      destroy-on-close
      :close-on-click-modal="false"
      custom-class="custom-dialog"
    >
      <div v-if="filterFavorites.length === 0" class="no-favorites">
        暂无收藏的筛选条件
      </div>
      <el-table v-else :data="filterFavorites" style="width: 100%">
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ new Date(scope.row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="applyFilterFavorite(scope.row)">应用</el-button>
            <el-button type="danger" size="small" @click="deleteFilterFavorite(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { Download, Filter, Delete, Plus, Star } from '@element-plus/icons-vue';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { ElMessage } from 'element-plus';

export default {
  name: 'report_network_device_age',
  components: {
    Download,
    Filter,
    Delete,
    Plus,
    Star
  },
  data() {
    // 从localStorage中恢复状态
    const savedState = JSON.parse(localStorage.getItem("networkDeviceAgeState") || "null");

    return {
      deviceAgeData: savedState?.deviceAgeData || [],
      loading: false,

      // 筛选对话框
      filterDialogVisible: false,
      favoritesDialogVisible: false,

      // 机房选项
      dataCenterOptions: [],

      // 运行状态选项
      operationStatusOptions: [],

      // 是否信创选项
      innovativeTechOptions: [],

      // 当前选中的筛选条件
      selectedDataCenters: savedState?.selectedDataCenters || [],
      selectedOperationStatuses: savedState?.selectedOperationStatuses || [],
      selectedInnovativeTechFlag: savedState?.selectedInnovativeTechFlag || '',
      timePeriods: savedState?.timePeriods || [],

      // 筛选表单
      filterForm: {
        dataCenters: [],
        operationStatuses: [],
        innovativeTechFlag: '',
        timePeriods: [{ startDate: null, endDate: null, label: '' }]
      },

      // 收藏表单
      favoriteForm: {
        name: ''
      },

      // 收藏列表
      filterFavorites: [],

      // 标记是否已经从localStorage恢复了状态
      restoredFromStorage: !!savedState
    };
  },
  computed: {
    // 判断是否有活跃的筛选条件
    hasActiveFilters() {
      return this.selectedDataCenters.length > 0 ||
             this.selectedOperationStatuses.length > 0 ||
             this.selectedInnovativeTechFlag !== '' ||
             this.timePeriods.length > 0;
    }
  },
  mounted() {
    this.loadDataCenters();
    this.loadOperationStatuses();
    this.loadInnovativeTechFlags();
    this.loadFilterFavorites();

    // 如果没有从localStorage恢复状态，则加载数据
    if (!this.restoredFromStorage) {
      this.loadDeviceAgeData();
    } else if (this.deviceAgeData.length === 0 &&
              (this.selectedDataCenters.length > 0 ||
               this.selectedOperationStatuses.length > 0 ||
               this.selectedInnovativeTechFlag !== '' ||
               this.timePeriods.length > 0)) {
      // 如果有筛选条件但没有数据，重新加载数据
      this.loadDeviceAgeData();
    }
  },
  methods: {
    // 加载机房列表
    async loadDataCenters() {
      try {
        const response = await this.$axios.post('/api/get_all_data_centers');
        if (response.data.code === 0) {
          this.dataCenterOptions = response.data.msg;
        } else {
          ElMessage.error('机房数据加载失败');
        }
      } catch (error) {

        ElMessage.error('机房数据加载失败');
      }
    },

    // 加载运行状态列表
    async loadOperationStatuses() {
      try {
        const response = await this.$axios.post('/api/get_all_operation_statuses');
        if (response.data.code === 0) {
          this.operationStatusOptions = response.data.msg;
        } else {
          ElMessage.error('运行状态数据加载失败');
        }
      } catch (error) {
        console.error('运行状态数据加载失败:', error);
        ElMessage.error('运行状态数据加载失败');
      }
    },

    // 加载是否信创选项列表
    async loadInnovativeTechFlags() {
      try {
        const response = await this.$axios.post('/api/get_all_device_innovative_tech_flags');
        if (response.data.code === 0) {
          this.innovativeTechOptions = response.data.msg;
        } else {
          ElMessage.error('是否信创选项加载失败');
        }
      } catch (error) {
        console.error('是否信创选项加载失败:', error);
        ElMessage.error('是否信创选项加载失败');
      }
    },

    // 加载网络设备年限数据
    async loadDeviceAgeData() {
      try {
        this.loading = true;

        // 准备请求参数
        const params = {
          dataCenters: this.selectedDataCenters.length > 0 ? this.selectedDataCenters : null,
          operationStatuses: this.selectedOperationStatuses.length > 0 ? this.selectedOperationStatuses : null,
          innovativeTechFlag: this.selectedInnovativeTechFlag || null,
          timePeriods: this.timePeriods.length > 0 ? this.timePeriods : null
        };

        const response = await this.$axios.post('/api/get_network_device_age_statistics', params);
        if (response.data.code === 0) {
          this.deviceAgeData = response.data.msg;

          // 保存状态到localStorage
          this.saveStateToLocalStorage();
        } else {
          ElMessage.error('数据加载失败');
        }
      } catch (error) {
        console.error('数据加载失败:', error);
        ElMessage.error('数据加载失败');
      } finally {
        this.loading = false;
      }
    },

    // 保存状态到localStorage
    saveStateToLocalStorage() {
      const stateToSave = {
        deviceAgeData: this.deviceAgeData,
        selectedDataCenters: this.selectedDataCenters,
        selectedOperationStatuses: this.selectedOperationStatuses,
        selectedInnovativeTechFlag: this.selectedInnovativeTechFlag,
        timePeriods: this.timePeriods
      };
      localStorage.setItem("networkDeviceAgeState", JSON.stringify(stateToSave));
    },

    // 显示筛选对话框
    showFilterDialog() {
      // 将当前筛选条件设置到表单中
      this.filterForm.dataCenters = [...this.selectedDataCenters];
      this.filterForm.operationStatuses = [...this.selectedOperationStatuses];
      this.filterForm.innovativeTechFlag = this.selectedInnovativeTechFlag;

      // 如果有时间段筛选条件，则使用它们，否则初始化一个空的时间段
      if (this.timePeriods.length > 0) {
        this.filterForm.timePeriods = JSON.parse(JSON.stringify(this.timePeriods));
      } else {
        this.filterForm.timePeriods = [{ startDate: null, endDate: null, label: '' }];
      }

      this.filterDialogVisible = true;
    },

    // 添加时间段
    addTimePeriod() {
      if (this.filterForm.timePeriods.length < 5) {
        this.filterForm.timePeriods.push({ startDate: null, endDate: null, label: '' });
      }
    },

    // 移除筛选表单中的时间段
    removeFilterTimePeriod(index) {
      if (this.filterForm.timePeriods.length > 1) {
        this.filterForm.timePeriods.splice(index, 1);
      }
    },

    // 移除已选机房
    removeDataCenter(center) {
      const index = this.selectedDataCenters.indexOf(center);
      if (index !== -1) {
        this.selectedDataCenters.splice(index, 1);
        this.loadDeviceAgeData();
      }
    },

    // 移除已选运行状态
    removeOperationStatus(status) {
      const index = this.selectedOperationStatuses.indexOf(status);
      if (index !== -1) {
        this.selectedOperationStatuses.splice(index, 1);
        this.loadDeviceAgeData();
      }
    },

    // 移除已选是否信创
    removeInnovativeTechFlag() {
      this.selectedInnovativeTechFlag = '';
      this.loadDeviceAgeData();
    },

    // 移除采购日期范围
    removePurchaseDateRange(range) {
      const index = this.selectedPurchaseDateRanges.indexOf(range);
      if (index !== -1) {
        this.selectedPurchaseDateRanges.splice(index, 1);

        // 如果移除的是“自定义”选项，同时清除所有自定义时间段
        if (range === 'custom') {
          this.timePeriods = [];
        }

        this.loadDeviceAgeData();
      }
    },

    // 移除已选时间段
    removeTimePeriod(index) {
      this.timePeriods.splice(index, 1);
      this.loadDeviceAgeData();
    },

    // 清除所有筛选条件
    clearAllFilters() {
      this.selectedDataCenters = [];
      this.selectedOperationStatuses = [];
      this.selectedInnovativeTechFlag = '';
      this.selectedPurchaseDateRanges = [];
      this.timePeriods = [];
      this.loadDeviceAgeData();

      // 清除localStorage中保存的状态
      localStorage.removeItem("networkDeviceAgeState");
    },

    // 应用筛选条件
    applyFilters() {
      // 验证时间段
      const validTimePeriods = this.filterForm.timePeriods.filter(period => period.startDate && period.endDate);

      // 处理时间段标签
      validTimePeriods.forEach(period => {
        if (!period.label) {
          // 如果没有自定义标签，生成一个默认标签
          const startYear = period.startDate ? new Date(period.startDate).getFullYear() : '';
          const endYear = period.endDate ? new Date(period.endDate).getFullYear() : '';

          if (startYear === endYear) {
            period.label = `${startYear}年`;
          } else {
            period.label = `${startYear}-${endYear}年`;
          }
        }
      });

      // 更新筛选条件
      this.selectedDataCenters = [...this.filterForm.dataCenters];
      this.selectedOperationStatuses = [...this.filterForm.operationStatuses];
      this.selectedInnovativeTechFlag = this.filterForm.innovativeTechFlag;
      this.timePeriods = validTimePeriods;

      // 关闭对话框
      this.filterDialogVisible = false;

      // 重新加载数据
      this.loadDeviceAgeData();
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      // 如果已经是格式化的字符串，直接返回
      if (typeof dateStr === 'string' && dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateStr;
      }
      // 否则尝试格式化
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return dateStr; // 如果无效日期，返回原值

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('Date formatting error:', error);
        return dateStr; // 出错时返回原值
      }
    },

    // 获取采购日期范围的显示文本
    getPurchaseDateRangeLabel(range) {
      switch (range) {
        case 'before_2016':
          return '2016年以前';
        case 'between_2017_2019':
          return '2017-2019年';
        case 'after_2020':
          return '2020年至今';
        case 'custom':
          return '自定义时间段';
        default:
          return range;
      }
    },

    // 根据百分比返回不同的CSS类
    getPercentageClass(percentage, isNew = false) {
      percentage = parseFloat(percentage);
      if (isNew) {
        // 对于新设备（2020-至今），高占比是好的
        return percentage >= 70 ? 'percentage-good' :
               percentage >= 40 ? 'percentage-medium' :
               'percentage-bad';
      } else {
        // 对于旧设备，低占比是好的
        return percentage <= 10 ? 'percentage-good' :
               percentage <= 30 ? 'percentage-medium' :
               'percentage-bad';
      }
    },

    // 加载收藏的筛选条件列表
    async loadFilterFavorites() {
      try {
        const username = localStorage.getItem('loginUsername') || '';
        if (!username) {
          console.log('未找到登录用户名');
          return;
        }

        const response = await this.$axios.post('/api/get_filter_favorites', { username });
        if (response.data.code === 0) {
          this.filterFavorites = response.data.msg;
        } else {
          ElMessage.error('加载收藏列表失败');
        }
      } catch (error) {
        console.error('加载收藏列表失败:', error);
        ElMessage.error('加载收藏列表失败');
      }
    },

    // 保存筛选条件收藏
    async saveFilterFavorite() {
      try {
        const username = localStorage.getItem('loginUsername') || '';
        if (!username) {
          ElMessage.warning('请先登录再保存收藏');
          return;
        }

        if (!this.favoriteForm.name) {
          ElMessage.warning('请输入收藏名称');
          return;
        }

        // 准备要保存的筛选条件
        const params = {
          name: this.favoriteForm.name,
          dataCenters: this.filterForm.dataCenters,
          operationStatuses: this.filterForm.operationStatuses,
          innovativeTechFlag: this.filterForm.innovativeTechFlag,
          timePeriods: this.filterForm.timePeriods.filter(period => period.startDate && period.endDate),
          username
        };

        const response = await this.$axios.post('/api/save_filter_favorite', params);
        if (response.data.code === 0) {
          ElMessage.success('收藏成功');
          this.favoriteForm.name = '';
          this.loadFilterFavorites();
        } else {
          ElMessage.error('收藏失败');
        }
      } catch (error) {
        console.error('收藏失败:', error);
        ElMessage.error('收藏失败');
      }
    },

    // 删除筛选条件收藏
    async deleteFilterFavorite(id) {
      try {
        const username = localStorage.getItem('loginUsername') || '';
        if (!username) {
          ElMessage.warning('请先登录再进行操作');
          return;
        }

        const response = await this.$axios.post('/api/delete_filter_favorite', { id, username });
        if (response.data.code === 0) {
          ElMessage.success('删除成功');
          this.loadFilterFavorites();
        } else {
          ElMessage.error('删除失败');
        }
      } catch (error) {
        console.error('删除收藏失败:', error);
        ElMessage.error('删除失败');
      }
    },

    // 应用收藏的筛选条件
    applyFilterFavorite(favorite) {
      this.filterForm.dataCenters = [...favorite.data_centers];
      this.filterForm.operationStatuses = [...favorite.operation_statuses];
      this.filterForm.innovativeTechFlag = favorite.innovative_tech_flag || '';

      // 处理时间段
      if (favorite.time_periods && favorite.time_periods.length > 0) {
        this.filterForm.timePeriods = JSON.parse(JSON.stringify(favorite.time_periods));
      } else {
        this.filterForm.timePeriods = [{ startDate: null, endDate: null, label: '' }];
      }

      // 如果是从收藏列表对话框中应用，则关闭对话框
      if (this.favoritesDialogVisible) {
        this.favoritesDialogVisible = false;
        this.filterDialogVisible = true;
      }
    },

    // 显示收藏列表对话框
    showFavoritesDialog() {
      this.loadFilterFavorites();
      this.favoritesDialogVisible = true;
    },

    // 导出Excel
    exportData() {
      const table = this.$refs.deviceAgeTable;
      if (!table) return;

      // 准备表头第一行
      const headerRow1 = ['机房', '全部设备数量'];
      // 准备表头第二行
      const headerRow2 = ['', ''];

      // 添加时间段列
      this.timePeriods.forEach(period => {
        const label = period.label || (this.formatDate(period.startDate) + ' 至 ' + this.formatDate(period.endDate));
        headerRow1.push(label, '');
        headerRow2.push('数量', '占比');
      });

      // 组合表头
      const headers = [headerRow1, headerRow2];

      // 准备数据行
      const data = this.deviceAgeData.map(row => {
        const rowData = [row.data_center, row.total_count];

        // 添加每个时间段的数据
        this.timePeriods.forEach((_, index) => {
          rowData.push(
            row['period_' + index + '_count'],
            row['period_' + index + '_percentage'] + '%'
          );
        });

        return rowData;
      });

      // 合并表头和数据
      const wsData = [...headers, ...data];

      // 创建工作表
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // 设置合并单元格
      const merges = [
        { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // 机房
        { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } }  // 全部设备数量
      ];

      // 添加时间段列的合并单元格
      this.timePeriods.forEach((_, index) => {
        const colIndex = 2 + (index * 2); // 从第3列开始，每个时间段占两列
        merges.push({ s: { r: 0, c: colIndex }, e: { r: 0, c: colIndex + 1 } });
      });

      ws['!merges'] = merges;

      // 设置列宽
      const cols = [
        { wch: 20 }, // 机房
        { wch: 12 }  // 全部设备数量
      ];

      // 添加时间段列的列宽
      this.timePeriods.forEach(() => {
        cols.push(
          { wch: 10 }, // 数量
          { wch: 10 }  // 占比
        );
      });

      ws['!cols'] = cols;

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '网络设备年限情况统计');

      // 导出Excel
      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });
      saveAs(blob, '网络设备年限情况统计.xlsx');
    }
  }
};
</script>

<style scoped>
.report-center {
  padding: 20px;
}

.report-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.report-card {
  margin-bottom: 20px;
  border-radius: 4px;
  border: none;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.report-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

/* 统一操作按钮区样式 */
.unified-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.action-bar-left, .action-bar-right {
  display: flex;
  gap: 12px;
  flex-wrap: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .action-bar-left, .action-bar-right {
    width: 100%;
  }
}

:deep(.el-button) {
  transition: all 0.3s;
}

:deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-button .el-icon) {
  margin-right: 5px;
}

.table-container {
  margin-top: 20px;
  overflow-x: auto;
  border-radius: 4px;
  padding: 0 10px 20px;
}

:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 600;
  height: 50px;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__body tr.hover-row > td) {
  background-color: #f0f9ff !important;
}

/* 筛选条件标签样式 */
.filter-tags {
  margin: 15px 0 20px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.filter-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.filter-tag {
  margin-right: 5px;
  transition: all 0.3s;
}

.filter-tag:hover {
  transform: translateY(-2px);
}

.clear-filters {
  margin-left: auto;
  color: #F56C6C;
  font-weight: bold;
  transition: all 0.3s;
}

.clear-filters:hover {
  color: #f78989;
  transform: scale(1.05);
}

/* 时间段行样式 */
.time-period-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  transition: all 0.3s;
}

.time-period-row:hover {
  background-color: #eef1f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-separator {
  margin: 0 10px;
  color: #606266;
}

.add-period-row {
  margin-top: 15px;
  display: flex;
  align-items: center;
}

.period-limit-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

/* 收藏相关样式 */
.favorite-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.favorites-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 5px;
}

.favorite-tag {
  cursor: pointer;
  transition: all 0.3s;
  padding: 8px 12px;
}

.favorite-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.no-favorites {
  text-align: center;
  color: #909399;
  padding: 30px 0;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* 百分比颜色样式 */
.percentage-good {
  color: #67C23A;
  font-weight: bold;
  background-color: rgba(103, 194, 58, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  transition: all 0.3s;
}

.percentage-medium {
  color: #E6A23C;
  font-weight: bold;
  background-color: rgba(230, 162, 60, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  transition: all 0.3s;
}

.percentage-bad {
  color: #F56C6C;
  font-weight: bold;
  background-color: rgba(245, 108, 108, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  transition: all 0.3s;
}

.percentage-good:hover, .percentage-medium:hover, .percentage-bad:hover {
  transform: scale(1.05);
}

/* 小计行样式 */
:deep(.el-table__row:last-child) {
  background-color: #ecf5ff !important;
  font-weight: bold;
  border-top: 2px solid #409EFF;
}

:deep(.el-table__row:last-child td) {
  background-color: #ecf5ff !important;
  color: #409EFF;
  padding: 12px 0;
}

/* 对话框样式 */
:deep(.custom-dialog) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.custom-dialog .el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.custom-dialog .el-dialog__footer) {
  border-top: 1px solid #ebeef5;
}
</style>
