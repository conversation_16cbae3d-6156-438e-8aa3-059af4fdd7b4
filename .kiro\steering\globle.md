---
description: 
globs: 
alwaysApply: true
---

# 整体要求

- 每个功能采用模块化单独设计
- 最小化编码，在修改或更新一个功能时，应该采用最小化编码，尽可能不影响其他功能代码
- 统一配置文件
- 前后端分离
- 前后端统一日期格式采用`yyyy-MM-dd`格式
- 前后端统一时间格式采用`yyyy-MM-dd HH:mm:ss`格式
- 格式化函数添加到全局混入（mixin）或工具函数中，以便在所有组件中重用
- 系统默认用户`admin`为超级用户，拥有所有页面权限，且默认取不到当前登录用户时，都默认使用`admin`用户

# 目录结构设计
- ​清晰分层​：分离不同职责的代码（如业务逻辑、配置、测试）
- ​可扩展性​：预留合理的子目录结构以适应未来功能增长
- ​工具链友好​：兼容构建工具（如 Webpack、Maven）和框架约定
- ​语言/框架适配​：遵循特定技术的推荐结构（如 React、Django）
- 根目录下只能放`CHANGELOG.md`和`README.md`文档，其他`.md`说明文档文件统一放在docs目录中

# 记录变更日志
- 每完成一个需求后需要进行一次总结
- 将总结结果更新到`README.md`文件和`CHANGELOG.md`文件
- 完成一次任务后需要更新`CHANGELOG.md`中当前的版本内容，只做总结性记录，同类事情只记录一条
- 不要更新`CHANGELOG.md`中的日期和版本号
- `README.md`中注意更新当前最新版本号

# 调试代码删除
- 临时创建的调试代码应该在调式完成后删除，避免留下不必要的冗余代码

# 更新数据
- 新建一条数据时，默认记录创建人 (created_by) 和更新人 (updated_by)，取当前登录用户，如 username
- 更新一条数据时，默认记录更新人 (updated_by)，取当前登录用户，如 username
