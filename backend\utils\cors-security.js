/**
 * CORS 安全配置工具
 * 实现严格的域名白名单和安全的CORS策略
 */

/**
 * 解析环境变量中的域名白名单
 * @returns {Array} 允许的域名数组
 */
function parseAllowedOrigins() {
    const origins = process.env.CORS_ALLOWED_ORIGINS || '';
    if (!origins.trim()) {

        return [];
    }

    // 解析逗号分隔的域名列表
    const originList = origins.split(',')
        .map(origin => origin.trim())
        .filter(origin => origin.length > 0);


    return originList;
}

/**
 * 验证域名是否合法
 * @param {string} origin 要验证的域名
 * @returns {boolean} 是否合法
 */
function isValidOrigin(origin) {
    if (!origin) return false;

    try {
        const url = new URL(origin);
        // 只允许 http 和 https 协议
        return ['http:', 'https:'].includes(url.protocol);
    } catch (error) {
        return false;
    }
}

/**
 * 记录被拒绝的CORS请求
 * @param {string} origin 被拒绝的域名
 * @param {string} reason 拒绝原因
 */
function logRejectedRequest(origin, reason) {
    if (process.env.CORS_LOG_REJECTED === 'true') {
        const timestamp = new Date().toISOString();

    }
}

/**
 * 创建安全的CORS配置
 * @returns {Object} CORS配置对象
 */
function createSecureCorsOptions() {
    const allowedOrigins = parseAllowedOrigins();
    const isStrictMode = process.env.CORS_STRICT_MODE === 'true';
    const isDevelopment = process.env.NODE_ENV === 'development';

    return {
        origin: (origin, callback) => {
            // 处理同源请求（origin为undefined）
            if (!origin) {
                // 同源请求通常是安全的，应该被允许
                // 只有在开发环境的严格模式下才拒绝（用于测试）
                if (isStrictMode && isDevelopment) {
                    logRejectedRequest('同源请求', '开发环境严格模式下拒绝同源请求');
                    return callback(null, false);
                }
                // 生产环境中允许同源请求
                return callback(null, true);
            }

            // 验证域名格式
            if (!isValidOrigin(origin)) {
                logRejectedRequest(origin, '无效的域名格式');
                return callback(null, false);
            }

            // 移除尾部斜杠进行比较
            const cleanOrigin = origin.replace(/\/$/, '');

            // 检查是否在白名单中
            const isAllowed = allowedOrigins.some(allowedOrigin => {
                return allowedOrigin.replace(/\/$/, '') === cleanOrigin;
            });

            if (isAllowed) {

                callback(null, true);
            } else {
                logRejectedRequest(origin, '不在域名白名单中');
                // 不抛出错误，而是返回false，这样会返回正确的CORS错误而不是500错误
                callback(null, false);
            }
        },

        // 是否允许携带凭据（Cookie、认证头等）
        credentials: process.env.CORS_CREDENTIALS === 'true',

        // 允许的HTTP方法
        methods: (process.env.CORS_METHODS || 'GET,POST,PUT,DELETE,OPTIONS').split(','),

        // 允许的请求头
        allowedHeaders: (process.env.CORS_HEADERS || 'Content-Type,Authorization').split(','),

        // 预检请求的缓存时间（秒）
        maxAge: parseInt(process.env.CORS_MAX_AGE || '86400'),

        // OPTIONS请求的成功状态码
        optionsSuccessStatus: 200,

        // 预检请求失败时的处理
        preflightContinue: false
    };
}

/**
 * 为特定路由设置安全的CORS头
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Array} allowedOrigins 允许的域名列表（可选，默认使用环境变量）
 */
function setSecureCorsHeaders(req, res, allowedOrigins = null) {
    const origins = allowedOrigins || parseAllowedOrigins();
    const origin = req.headers.origin;

    if (origin && origins.includes(origin.replace(/\/$/, ''))) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', process.env.CORS_CREDENTIALS || 'true');
        res.header('Access-Control-Allow-Methods', process.env.CORS_METHODS || 'GET,POST,PUT,DELETE,OPTIONS');
        res.header('Access-Control-Allow-Headers', process.env.CORS_HEADERS || 'Content-Type,Authorization');
        res.header('Access-Control-Max-Age', process.env.CORS_MAX_AGE || '86400');
    }
}

/**
 * 获取CORS配置信息（用于调试）
 * @returns {Object} 当前CORS配置信息
 */
function getCorsInfo() {
    return {
        allowedOrigins: parseAllowedOrigins(),
        credentials: process.env.CORS_CREDENTIALS === 'true',
        strictMode: process.env.CORS_STRICT_MODE === 'true',
        logRejected: process.env.CORS_LOG_REJECTED === 'true',
        environment: process.env.NODE_ENV || 'development'
    };
}

module.exports = {
    createSecureCorsOptions,
    setSecureCorsHeaders,
    getCorsInfo,
    parseAllowedOrigins,
    isValidOrigin
}; 