# 自动用户字段实现说明

## 概述

为了简化资产管理操作中创建人和更新人字段的处理，我们实现了一个自动用户字段中间件，该中间件会自动从JWT token中提取当前登录用户的用户名，并根据操作类型自动设置相应的字段。

## 实现逻辑

### 1. 新增资产时
- **创建人 (created_by)**：自动设置为当前登录用户的 `username`
- **更新人 (updated_by)**：自动设置为当前登录用户的 `username`

### 2. 编辑已有资产时
- **更新人 (updated_by)**：自动设置为当前登录用户的 `username`
- **创建人 (created_by)**：保持不变

## 技术实现

### 后端中间件

创建了 `backend/middlewares/auto-user-fields.js` 中间件，主要功能：

1. **JWT Token 验证**：从请求头中提取并验证JWT token
2. **用户信息提取**：从token中提取用户名、用户ID和角色信息
3. **操作类型识别**：根据请求路径和请求体判断是新增还是更新操作
4. **自动字段设置**：
   - 新增操作：设置 `created_by` 和 `updated_by`
   - 更新操作：只设置 `updated_by`
5. **向后兼容**：同时设置 `username` 字段，保持与现有代码的兼容性

### 中间件应用

在 `backend/index.js` 中应用中间件：

```javascript
// 应用自动用户字段中间件到所有CMDB API
app.use('/api', (req, res, next) => {
    if (req.path.includes('cmdb') && (req.path.includes('/add_') || req.path.includes('/update_') || req.path.includes('/del_'))) {
        return cmdbAutoUserFields(req, res, next);
    }
    next();
});
```

## 影响的API

该中间件影响所有CMDB相关的资产管理API，包括但不限于：

- `/api/add_cmdb_*` - 所有新增资产API
- `/api/update_cmdb_*` - 所有更新资产API
- `/api/del_cmdb_*` - 所有删除资产API

## 前端兼容性

### 当前状态
前端代码仍然可以正常工作，因为：
1. 中间件保持了向后兼容性，仍然设置 `username` 字段
2. 前端传递的 `username` 参数会被中间件自动覆盖为当前登录用户

### 建议优化
前端可以选择性地移除手动设置 `username` 的代码，因为后端现在会自动处理：

```javascript
// 之前的代码
const requestData = {
  ...this.formData,
  username: localStorage.getItem("loginUsername") || "unknown"
};

// 优化后的代码（可选）
const requestData = {
  ...this.formData
  // username 字段会由后端中间件自动设置
};
```

## 安全性

1. **Token验证**：中间件会验证JWT token的有效性
2. **用户身份确认**：确保操作记录的用户信息来自可信的token，而不是前端传递的参数
3. **防篡改**：即使前端传递了错误的用户名，也会被中间件覆盖为token中的真实用户

## 错误处理

中间件包含完整的错误处理：
- Token缺失：返回401错误
- Token无效：返回401错误
- Token过期：返回401错误
- 其他错误：返回500错误

## 测试验证

已通过测试验证：
1. 新增操作正确设置 `created_by` 和 `updated_by`
2. 更新操作只设置 `updated_by`
3. 用户信息正确提取并设置到请求对象中
4. 向后兼容性保持良好

## 部署说明

1. 该功能已集成到现有代码中，无需额外配置
2. 中间件会自动应用到所有CMDB相关API
3. 前端代码无需修改即可正常工作
4. 建议在生产环境部署前进行充分测试

## 维护说明

- 中间件代码位于：`backend/middlewares/auto-user-fields.js`
- 应用配置位于：`backend/index.js`
- 如需修改逻辑，请确保保持向后兼容性
- 任何修改都应该进行充分测试