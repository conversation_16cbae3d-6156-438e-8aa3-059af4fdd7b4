# 互联网线路管理用户手册

## 目录

1. [功能概述](#功能概述)
2. [系统要求](#系统要求)
3. [快速入门](#快速入门)
4. [线路管理](#线路管理)
5. [IP映射管理](#ip映射管理)
6. [费用统计](#费用统计)
7. [数据导入导出](#数据导入导出)
8. [常见问题](#常见问题)
9. [故障排除](#故障排除)

## 功能概述

互联网线路管理系统是CMDB系统的重要组成部分，专门用于管理企业机房的互联网线路资源。系统提供以下核心功能：

### 主要功能
- **线路信息管理**: 完整的线路生命周期管理，包括基本信息、合同信息和技术参数
- **IP映射管理**: 详细的IP地址映射关系管理，支持端口映射和白名单配置
- **费用统计**: 多维度的费用统计分析和合同到期提醒
- **数据导入导出**: 支持Excel格式的批量数据导入导出
- **搜索筛选**: 强大的搜索和筛选功能，快速定位所需信息

### 适用场景
- 企业IT部门管理机房网络资源
- 网络工程师进行网络规划和维护
- 财务部门进行成本控制和预算规划
- 运维团队进行日常网络资源监控

## 系统要求

### 浏览器要求
- Chrome 80+ (推荐)
- Firefox 75+
- Safari 13+
- Edge 80+

### 网络要求
- 稳定的网络连接
- 访问CMDB系统的权限

### 用户权限
- 查看权限：可以查看线路和IP映射信息
- 编辑权限：可以新增、修改线路和IP映射信息
- 删除权限：可以删除线路和IP映射信息
- 导出权限：可以导出数据到Excel文件

## 快速入门

### 1. 登录系统

1. 打开浏览器，访问CMDB系统地址
2. 输入用户名和密码进行登录
3. 登录成功后，在左侧导航菜单中找到"互联网线路管理"

### 2. 界面介绍

系统主界面包含以下几个部分：

- **顶部导航栏**: 显示当前用户信息和系统功能入口
- **左侧菜单**: 系统功能模块导航
- **主工作区**: 显示当前功能的具体内容
- **搜索区域**: 提供各种搜索和筛选条件
- **操作按钮区**: 新增、导出等操作按钮
- **数据表格**: 显示线路或IP映射的详细信息
- **分页控件**: 用于浏览大量数据

### 3. 基本操作流程

1. **查看数据**: 进入相应模块，系统自动加载数据列表
2. **搜索筛选**: 使用搜索条件快速定位所需信息
3. **新增记录**: 点击"新增"按钮，填写表单信息
4. **编辑记录**: 点击表格中的"编辑"按钮，修改信息
5. **删除记录**: 点击表格中的"删除"按钮，确认删除操作

## 线路管理

### 4.1 查看线路列表

1. 在左侧菜单中点击"互联网线路管理"
2. 系统显示所有线路的列表，包含以下信息：
   - 线路名称
   - 运营商
   - 线路类型
   - 带宽
   - 所属机房
   - 合同状态
   - 操作按钮

### 4.2 搜索和筛选线路

#### 基本搜索
- **线路名称**: 输入线路名称进行模糊搜索
- **运营商**: 从下拉列表中选择特定运营商
- **线路类型**: 选择专线、宽带等线路类型
- **所属机房**: 选择特定机房

#### 高级筛选
- **带宽范围**: 筛选特定带宽的线路
- **费用范围**: 设置最小和最大月费用进行筛选
- **合同状态**: 筛选有效、即将过期或已过期的合同
- **关键词搜索**: 在多个字段中同时搜索关键词

#### 操作步骤
1. 在搜索区域输入或选择筛选条件
2. 点击"搜索"按钮执行搜索
3. 点击"重置"按钮清空所有搜索条件

### 4.3 新增线路

#### 必填信息
- **线路名称**: 唯一标识线路的名称
- **运营商**: 选择中国电信、中国联通、中国移动等
- **线路类型**: 选择专线、宽带、光纤等类型
- **带宽**: 输入带宽信息，如"100M"、"1G"
- **所属机房**: 选择线路所在的机房

#### 可选信息
- **IP段**: 线路分配的IP地址段，格式如"***********/24"
- **接入方式**: 光纤接入、以太网接入等
- **线路用途**: 描述线路的主要用途
- **合同编号**: 与运营商签订的合同编号
- **合同开始日期**: 合同生效日期
- **合同结束日期**: 合同到期日期
- **月费用**: 每月支付给运营商的费用
- **备注**: 其他需要说明的信息

#### 操作步骤
1. 点击"新增线路"按钮
2. 在弹出的对话框中填写线路信息
3. 点击"确定"按钮保存，或点击"取消"放弃操作
4. 系统验证数据后保存成功，并刷新列表

#### 注意事项
- 线路名称不能重复
- IP段必须是有效的CIDR格式
- 合同开始日期必须早于结束日期
- 月费用必须为非负数

### 4.4 编辑线路

1. 在线路列表中找到要编辑的线路
2. 点击该行的"编辑"按钮
3. 在弹出的对话框中修改线路信息
4. 点击"更新"按钮保存修改

### 4.5 删除线路

1. 在线路列表中找到要删除的线路
2. 点击该行的"删除"按钮
3. 在确认对话框中点击"确认删除"

**重要提示**: 如果线路下存在IP映射记录，系统将阻止删除操作。需要先删除相关的IP映射记录，然后才能删除线路。

### 4.6 批量操作

- **批量删除**: 选择多条记录后，点击"批量删除"按钮
- **批量导出**: 选择特定条件后，导出符合条件的线路数据

## IP映射管理

### 5.1 查看IP映射列表

1. 在左侧菜单中点击"IP映射管理"
2. 系统显示所有IP映射的列表，包含以下信息：
   - 互联网IP
   - DMZ-IP
   - 管理IP
   - 源端口
   - 映射端口
   - 协议类型
   - 功能说明
   - 所属机房
   - 使用的线路
   - 使用人
   - 状态

### 5.2 搜索和筛选IP映射

#### 搜索条件
- **互联网IP**: 输入IP地址进行精确或模糊搜索
- **DMZ-IP**: 搜索特定的DMZ IP地址
- **管理IP**: 搜索管理网络IP地址
- **端口**: 搜索特定的源端口或映射端口
- **协议**: 选择TCP或UDP协议
- **机房**: 选择特定机房的映射记录
- **线路**: 选择特定线路的映射记录
- **使用人**: 搜索特定用户的映射记录
- **状态**: 筛选活跃或停用的映射记录

### 5.3 新增IP映射

#### 必填信息
- **互联网IP**: 公网IP地址，必须是有效的IPv4格式
- **协议**: 选择TCP或UDP
- **所属机房**: 选择映射所在的机房
- **使用的线路**: 从现有线路中选择

#### 可选信息
- **DMZ-IP**: 内网DMZ区域的IP地址
- **管理IP**: 管理网络的IP地址
- **源端口**: 内网服务的端口号
- **映射端口**: 公网访问的端口号
- **功能说明**: 描述该映射的用途
- **白名单**: 允许访问的IP地址列表
- **使用人**: 负责该映射的人员
- **状态**: 设置为活跃或停用状态

#### 白名单配置
白名单支持以下格式：
- 单个IP地址：`***********00`
- IP地址段：`***********/24`
- 多个地址：用逗号分隔，如`***********00, 10.0.0.0/8`

#### 操作步骤
1. 点击"新增映射"按钮
2. 填写映射信息，特别注意IP地址格式
3. 配置白名单（如需要）
4. 选择使用的线路
5. 点击"确定"保存

#### 冲突检测
系统会自动检测以下冲突：
- 相同互联网IP和端口的TCP/UDP映射冲突
- 端口范围冲突
- IP地址格式错误

### 5.4 编辑IP映射

1. 在IP映射列表中找到要编辑的记录
2. 点击"编辑"按钮
3. 修改相关信息
4. 点击"更新"保存修改

### 5.5 删除IP映射

1. 找到要删除的IP映射记录
2. 点击"删除"按钮
3. 确认删除操作

### 5.6 白名单管理

#### 添加白名单
1. 在编辑IP映射时，找到"白名单"字段
2. 输入允许访问的IP地址或网段
3. 多个地址用逗号分隔
4. 保存配置

#### 白名单格式示例
```
***********00
***********/24
10.0.0.0/8, **********/12, ***********/16
```

## 费用统计

### 6.1 费用统计概览

费用统计功能提供多维度的线路费用分析，帮助财务部门进行成本控制和预算规划。

### 6.2 查看费用统计

1. 在左侧菜单中点击"费用统计"
2. 选择统计维度：
   - **按运营商统计**: 查看各运营商的费用分布
   - **按线路类型统计**: 查看不同类型线路的费用
   - **按机房统计**: 查看各机房的线路费用

3. 设置时间范围（可选）
4. 点击"查询"按钮生成统计报表

### 6.3 统计报表内容

#### 汇总信息
- 线路总数
- 月费用总计
- 年费用总计
- 平均月费用
- 已过期合同数量
- 即将过期合同数量

#### 分组统计
- 各分组的线路数量
- 各分组的费用汇总
- 各分组的平均费用
- 费用占比分析

### 6.4 合同到期提醒

#### 查看到期提醒
1. 在费用统计页面找到"合同到期提醒"区域
2. 系统自动显示：
   - 已过期的合同列表
   - 即将到期的合同列表（默认30天内）
   - 到期天数倒计时

#### 设置提醒天数
1. 在提醒设置中修改提前提醒天数
2. 点击"更新"应用新的设置

#### 处理到期合同
1. 联系相关运营商续约
2. 更新合同信息中的结束日期
3. 如不再使用，及时删除线路记录

## 数据导入导出

### 7.1 导出数据

#### 导出线路数据
1. 在线路管理页面，设置筛选条件（可选）
2. 点击"导出Excel"按钮
3. 选择导出类型：
   - **全部数据**: 导出所有线路信息
   - **筛选数据**: 仅导出当前筛选结果
4. 系统生成Excel文件并自动下载

#### 导出IP映射数据
1. 在IP映射管理页面进行相同操作
2. 导出的文件包含完整的映射信息和关联的线路信息

#### 导出文件格式
- 文件格式：Excel (.xlsx)
- 文件名：包含导出日期，如"互联网线路数据_2024-01-01.xlsx"
- 内容：包含数据表和统计表两个工作表

### 7.2 导入数据

#### 准备导入文件
1. 点击"下载模板"按钮获取标准模板
2. 按照模板格式填写数据
3. 确保数据格式正确，特别是日期和数值字段

#### 执行导入
1. 点击"导入数据"按钮
2. 选择准备好的Excel文件
3. 系统验证数据格式和内容
4. 查看导入预览和错误提示
5. 确认无误后点击"开始导入"

#### 导入结果
系统会显示导入结果：
- 成功导入的记录数
- 失败的记录数和具体错误信息
- 重复数据的处理结果

#### 导入注意事项
- 线路名称不能重复
- IP地址必须是有效格式
- 日期格式必须正确（YYYY-MM-DD）
- 数值字段不能包含非数字字符
- 必填字段不能为空

### 7.3 模板说明

#### 线路导入模板字段
| 字段名 | 是否必填 | 格式说明 | 示例 |
|--------|----------|----------|------|
| 线路名称 | 是 | 文本，不能重复 | 电信专线1 |
| 运营商 | 是 | 从字典中选择 | 中国电信 |
| 线路类型 | 是 | 从字典中选择 | 专线 |
| 带宽 | 是 | 文本格式 | 100M |
| IP段 | 否 | CIDR格式 | ***********/24 |
| 所属机房 | 是 | 从字典中选择 | 机房A |
| 接入方式 | 否 | 文本 | 光纤接入 |
| 线路用途 | 否 | 文本 | 业务系统访问 |
| 合同编号 | 否 | 文本 | CT2024001 |
| 合同开始日期 | 否 | YYYY-MM-DD | 2024-01-01 |
| 合同结束日期 | 否 | YYYY-MM-DD | 2025-12-31 |
| 月费用 | 否 | 数值 | 5000 |
| 备注 | 否 | 文本 | 主要用于生产环境 |

#### IP映射导入模板字段
| 字段名 | 是否必填 | 格式说明 | 示例 |
|--------|----------|----------|------|
| 互联网IP | 是 | IPv4格式 | ************ |
| DMZ-IP | 否 | IPv4格式 | ************** |
| 管理IP | 否 | IPv4格式 | ********* |
| 源端口 | 否 | 1-65535 | 80 |
| 互联网映射端口 | 否 | 1-65535 | 8080 |
| 端口协议 | 是 | TCP或UDP | TCP |
| 功能说明 | 否 | 文本 | Web服务器 |
| 所属机房 | 是 | 从字典中选择 | 机房A |
| 白名单 | 否 | IP列表，逗号分隔 | ***********/24, 10.0.0.0/8 |
| 使用的互联网线路 | 是 | 现有线路名称 | 电信专线1 |
| 使用人 | 否 | 文本 | 张三 |
| 状态 | 否 | active或inactive | active |

## 常见问题

### 8.1 登录和权限问题

**Q: 无法访问互联网线路管理功能？**
A: 请检查以下几点：
- 确认已正确登录系统
- 确认用户账号具有相应的功能权限
- 联系系统管理员分配权限

**Q: 可以查看数据但无法编辑？**
A: 用户可能只有查看权限，需要联系管理员分配编辑权限。

### 8.2 数据操作问题

**Q: 为什么无法删除某条线路记录？**
A: 如果线路下存在IP映射记录，系统会阻止删除操作。请先删除相关的IP映射记录，然后再删除线路。

**Q: 添加线路时提示"线路名称已存在"？**
A: 线路名称必须唯一，请使用不同的名称或检查是否已有相同名称的线路。

**Q: IP地址格式验证失败？**
A: 请确保IP地址格式正确：
- IPv4格式：***********
- CIDR格式：***********/24
- 避免使用无效的IP地址如999.999.999.999

### 8.3 搜索和筛选问题

**Q: 搜索结果为空？**
A: 请检查：
- 搜索条件是否过于严格
- 是否存在符合条件的数据
- 尝试使用更宽泛的搜索条件

**Q: 关键词搜索不准确？**
A: 关键词搜索会在多个字段中查找，可能返回相关但不完全匹配的结果。建议使用具体字段进行精确搜索。

### 8.4 导入导出问题

**Q: 导出的Excel文件无法打开？**
A: 请确保：
- 使用支持.xlsx格式的软件（如Microsoft Excel、WPS等）
- 文件下载完整，没有中断
- 浏览器没有阻止文件下载

**Q: 导入数据时出现格式错误？**
A: 请检查：
- 使用标准模板格式
- 日期格式为YYYY-MM-DD
- 数值字段不包含文字
- 必填字段不为空
- IP地址格式正确

**Q: 导入时提示线路不存在？**
A: 在导入IP映射数据时，"使用的互联网线路"字段必须填写已存在的线路名称。请先确保相关线路已创建。

### 8.5 性能问题

**Q: 页面加载很慢？**
A: 可能的原因和解决方案：
- 数据量过大：使用筛选条件减少显示数据
- 网络连接问题：检查网络状况
- 服务器负载高：联系系统管理员

**Q: 导出大量数据时超时？**
A: 建议：
- 使用筛选条件减少导出数据量
- 分批次导出数据
- 在网络状况良好时进行操作

## 故障排除

### 9.1 常见错误信息

#### "网络请求失败"
- **原因**: 网络连接问题或服务器不可用
- **解决方案**: 
  - 检查网络连接
  - 刷新页面重试
  - 联系系统管理员

#### "权限不足"
- **原因**: 用户权限不够
- **解决方案**: 联系系统管理员分配相应权限

#### "数据验证失败"
- **原因**: 输入的数据不符合格式要求
- **解决方案**: 
  - 检查必填字段是否完整
  - 确认数据格式正确
  - 参考字段格式说明

#### "操作超时"
- **原因**: 网络延迟或服务器响应慢
- **解决方案**: 
  - 等待操作完成
  - 刷新页面查看结果
  - 重新执行操作

### 9.2 浏览器兼容性问题

#### 页面显示异常
- 清除浏览器缓存
- 更新浏览器到最新版本
- 尝试使用推荐的浏览器

#### 功能无法正常使用
- 检查浏览器是否启用JavaScript
- 禁用可能冲突的浏览器插件
- 尝试使用无痕模式访问

### 9.3 数据问题

#### 数据显示不一致
- 刷新页面获取最新数据
- 检查是否有其他用户同时修改数据
- 联系系统管理员检查数据完整性

#### 统计数据不准确
- 确认统计条件设置正确
- 检查基础数据是否完整
- 重新生成统计报表

### 9.4 联系支持

如果遇到无法解决的问题，请联系技术支持：

- **系统管理员**: 内部IT部门
- **技术支持邮箱**: <EMAIL>
- **支持电话**: 400-xxx-xxxx
- **在线帮助**: 系统内置帮助文档

提供问题反馈时，请包含以下信息：
- 具体的错误信息
- 操作步骤
- 浏览器类型和版本
- 截图（如适用）
- 用户账号信息

---

**版本信息**: v1.0.0  
**更新日期**: 2024-01-01  
**适用系统**: CMDB v2.2.3+