import React, { useState, useEffect } from 'react';
import { AlertTriangle } from 'lucide-react';
import EnhancedCard from './EnhancedCard';

interface AlertData {
  type: '一般告警' | '严重告警' | '紧急告警';
  count: number;
  color: string;
  glowColor: string;
  maxCount: number;
}

interface AlertWaterLevelChartProps {
  className?: string;
  animationDelay?: number;
}

const AlertWaterLevelChart: React.FC<AlertWaterLevelChartProps> = ({
  className = "",
  animationDelay = 1000
}) => {
  const [animatedValues, setAnimatedValues] = useState([0, 0, 0]);

  // 告警数据
  const alertData: AlertData[] = [
    {
      type: '一般告警',
      count: 342,
      maxCount: 500,
      color: '#10b981',
      glowColor: '#10b981'
    },
    {
      type: '严重告警',
      count: 287,
      maxCount: 400,
      color: '#f59e0b',
      glowColor: '#f59e0b'
    },
    {
      type: '紧急告警',
      count: 128,
      maxCount: 200,
      color: '#ef4444',
      glowColor: '#ef4444'
    }
  ];

  // 动画效果
  useEffect(() => {
    const timer = setTimeout(() => {
      alertData.forEach((alert, index) => {
        setTimeout(() => {
          setAnimatedValues(prev => {
            const newValues = [...prev];
            newValues[index] = alert.count;
            return newValues;
          });
        }, index * 200);
      });
    }, animationDelay);

    return () => clearTimeout(timer);
  }, [animationDelay]);

  return (
    <EnhancedCard
      title="当前告警未处理数"
      icon={<AlertTriangle className="w-5 h-5" />}
      className={`h-full flex flex-col ${className}`}
      animationDelay={animationDelay}
    >
      <div className="flex-1 flex justify-around items-end space-x-4 pb-2 -mt-2">
        {alertData.map((alert, index) => {
          const percentage = (animatedValues[index] / alert.maxCount) * 100;
          const waterHeight = Math.min(percentage, 100);

          return (
            <div key={alert.type} className="flex flex-col items-center flex-1">
              {/* 水位图容器 */}
              <div className="relative w-full h-40 mb-2">
                <div className="absolute inset-0 rounded-lg border-2 border-gray-600 bg-gray-800/50 overflow-hidden">
                  {/* 水位背景 */}
                  <div
                    className="absolute bottom-0 left-0 right-0 transition-all duration-1000 ease-out rounded-b-lg"
                    style={{
                      height: `${waterHeight}%`,
                      background: `linear-gradient(180deg, ${alert.color}40 0%, ${alert.color}80 100%)`,
                      boxShadow: `0 0 20px ${alert.glowColor}40`
                    }}
                  >
                    {/* 水波动画效果 */}
                    <div
                      className="absolute top-0 left-0 right-0 h-2 opacity-60"
                      style={{
                        background: `linear-gradient(90deg, transparent 0%, ${alert.color} 50%, transparent 100%)`,
                        animation: 'wave 2s ease-in-out infinite'
                      }}
                    />
                  </div>

                  {/* 刻度线 */}
                  {[25, 50, 75].map((mark) => (
                    <div
                      key={mark}
                      className="absolute left-0 right-0 border-t border-gray-500/30"
                      style={{ bottom: `${mark}%` }}
                    />
                  ))}

                  {/* 数值显示 */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div
                        className="text-2xl font-bold transition-all duration-1000"
                        style={{ color: alert.color }}
                      >
                        {animatedValues[index]}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        / {alert.maxCount}
                      </div>
                    </div>
                  </div>

                  {/* 百分比显示 */}
                  <div className="absolute top-2 right-2">
                    <div
                      className="text-xs font-bold px-2 py-1 rounded"
                      style={{
                        backgroundColor: `${alert.color}20`,
                        color: alert.color,
                        border: `1px solid ${alert.color}40`
                      }}
                    >
                      {waterHeight.toFixed(0)}%
                    </div>
                  </div>
                </div>
              </div>

              {/* 标签 */}
              <div className="text-center">
                <div
                  className="text-sm font-medium mb-1"
                  style={{ color: alert.color }}
                >
                  {alert.type}
                </div>
                <div className="text-xs text-gray-400">
                  未处理数量
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 总计信息 */}
      <div className="mt-2 pt-2 border-t border-gray-700/50">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-400">总未处理告警</span>
          <span className="text-sm font-bold text-white">
            {animatedValues.reduce((sum, val) => sum + val, 0)}
          </span>
        </div>
      </div>


    </EnhancedCard>
  );
};

export default AlertWaterLevelChart;