/**
 * VMware虚拟化信息自动同步服务
 * 定时从指定接口获取VMware主机信息并更新到数据库
 */

const axios = require('axios');
const cron = require('node-cron');

class VmwareSyncService {
    constructor(dbConnection) {
        this.db = dbConnection;
        this.dataSourceUrl = process.env.VMWARE_DATA_SOURCE_URL || 'http://**************:8081/config_files?file=vmware_hosts.json';
        this.syncInterval = process.env.VMWARE_SYNC_INTERVAL || '0 * * * *'; // 默认每小时执行一次
        this.isRunning = false;
        this.lastSyncTime = null;
        this.syncTask = null;
        this.tableName = 'cmdb_vmware_info_auto_update';
        
        console.log(`VMware同步服务初始化完成`);
        console.log(`数据源URL: ${this.dataSourceUrl}`);
        console.log(`同步间隔: ${this.syncInterval}`);
    }

    /**
     * 启动定时同步任务
     */
    start() {
        try {
            // 如果已经在运行，先停止
            if (this.syncTask) {
                this.stop();
            }

            // 创建定时任务
            this.syncTask = cron.schedule(this.syncInterval, async () => {
                await this.syncVmwareData();
            }, {
                scheduled: false,
                timezone: "Asia/Shanghai"
            });

            // 启动定时任务
            this.syncTask.start();
            
            console.log(`VMware数据同步定时任务已启动，执行间隔: ${this.syncInterval}`);
            
            // 立即执行一次同步
            setTimeout(() => {
                this.syncVmwareData();
            }, 5000); // 延迟5秒执行，避免启动时的资源竞争

            return true;
        } catch (error) {
            console.error('启动VMware同步服务失败:', error);
            return false;
        }
    }

    /**
     * 停止定时同步任务
     */
    stop() {
        if (this.syncTask) {
            this.syncTask.stop();
            this.syncTask.destroy();
            this.syncTask = null;
            console.log('VMware数据同步定时任务已停止');
        }
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isRunning: this.syncTask ? this.syncTask.getStatus() === 'scheduled' : false,
            lastSyncTime: this.lastSyncTime,
            dataSourceUrl: this.dataSourceUrl,
            syncInterval: this.syncInterval
        };
    }

    /**
     * 手动触发同步
     */
    async manualSync() {
        return await this.syncVmwareData();
    }

    /**
     * 同步VMware数据的核心方法
     */
    async syncVmwareData() {
        if (this.isRunning) {
            console.log('VMware数据同步正在进行中，跳过本次执行');
            return { success: false, message: '同步正在进行中' };
        }

        this.isRunning = true;
        const syncStartTime = new Date();
        let logId = null;

        try {
            console.log(`开始同步VMware数据 - ${syncStartTime.toISOString()}`);

            // 创建同步日志记录
            const logResult = await this.db.query(
                `INSERT INTO cmdb_vmware_sync_logs (sync_start_time, data_source_url, sync_status) 
                 VALUES ($1, $2, 'running') RETURNING id`,
                [syncStartTime, this.dataSourceUrl]
            );
            logId = logResult.rows[0].id;

            // 获取远程数据
            const response = await axios.get(this.dataSourceUrl, {
                timeout: 30000, // 30秒超时
                headers: {
                    'User-Agent': 'CMDB-VMware-Sync-Service/1.0'
                }
            });

            if (!response.data) {
                throw new Error('接口返回数据为空');
            }

            let responseData = response.data;
            
            // 如果返回的是字符串，尝试解析为JSON
            if (typeof responseData === 'string') {
                responseData = JSON.parse(responseData);
            }

            // 打印原始数据结构用于调试
            console.log('接口返回的原始数据结构:', JSON.stringify(responseData, null, 2).substring(0, 500) + '...');
            console.log('数据类型:', typeof responseData);
            console.log('是否为数组:', Array.isArray(responseData));
            console.log('包含的键:', Object.keys(responseData || {}));

            // 提取虚拟机数据和更新时间
            let vmwareData;
            let dataSourceTime;

            // 处理不同的数据格式
            if (Array.isArray(responseData)) {
                // 如果直接是数组格式
                vmwareData = responseData;
                dataSourceTime = new Date();
                console.log('检测到直接数组格式');
            } else if (responseData && typeof responseData === 'object') {
                // 如果是对象格式，尝试提取data字段
                if (Array.isArray(responseData.data)) {
                    vmwareData = responseData.data;
                    dataSourceTime = responseData.updatetime ? new Date(responseData.updatetime) : new Date();
                    console.log('检测到对象包装格式，提取data字段');
                } else if (responseData.data && Array.isArray(responseData.data.data)) {
                    // 处理嵌套的data结构
                    vmwareData = responseData.data.data;
                    dataSourceTime = responseData.data.updatetime ? new Date(responseData.data.updatetime) : new Date();
                    console.log('检测到嵌套对象格式');
                } else {
                    // 如果没有data字段，尝试将整个对象作为数组处理
                    const keys = Object.keys(responseData);
                    console.log('未找到标准data字段，可用键:', keys);
                    
                    // 尝试找到数组字段
                    const arrayKey = keys.find(key => Array.isArray(responseData[key]));
                    if (arrayKey) {
                        vmwareData = responseData[arrayKey];
                        const updateTimeStr = responseData.updatetime || responseData.timestamp;
                        dataSourceTime = updateTimeStr ? new Date(updateTimeStr) : new Date();
                        console.log(`使用字段 "${arrayKey}" 作为数据源`);
                    } else {
                        throw new Error(`接口返回的数据格式不正确，未找到数组数据。返回的键: ${keys.join(', ')}`);
                    }
                }
            } else {
                throw new Error(`接口返回的数据格式不正确，期望对象或数组格式，实际类型: ${typeof responseData}`);
            }

            // 验证最终的数据格式
            if (!Array.isArray(vmwareData)) {
                throw new Error(`提取的虚拟机数据不是数组格式，实际类型: ${typeof vmwareData}`);
            }

            console.log(`获取到 ${vmwareData.length} 条VMware虚拟机数据`);
            console.log(`数据源时间: ${dataSourceTime.toISOString()}`);

            // 处理数据并更新数据库
            const syncResult = await this.processVmwareData(vmwareData, dataSourceTime);
            
            const syncEndTime = new Date();
            const syncDuration = syncEndTime.getTime() - syncStartTime.getTime();

            // 更新同步日志
            await this.db.query(
                `UPDATE cmdb_vmware_sync_logs 
                 SET sync_end_time = $1, sync_duration_ms = $2, total_records = $3, 
                     success_records = $4, failed_records = $5, new_records = $6, 
                     updated_records = $7, sync_status = 'success'
                 WHERE id = $8`,
                [
                    syncEndTime, syncDuration, syncResult.total,
                    syncResult.success, syncResult.failed, syncResult.newRecords,
                    syncResult.updatedRecords, logId
                ]
            );

            this.lastSyncTime = syncEndTime;
            
            console.log(`VMware数据同步完成 - 耗时: ${syncDuration}ms`);
            console.log(`处理结果: 总计${syncResult.total}, 成功${syncResult.success}, 失败${syncResult.failed}, 新增${syncResult.newRecords}, 更新${syncResult.updatedRecords}`);

            return {
                success: true,
                message: '同步成功',
                data: syncResult,
                duration: syncDuration
            };

        } catch (error) {
            console.error('VMware数据同步失败:', error);
            
            const syncEndTime = new Date();
            const syncDuration = syncEndTime.getTime() - syncStartTime.getTime();

            // 更新同步日志为失败状态
            if (logId) {
                await this.db.query(
                    `UPDATE cmdb_vmware_sync_logs 
                     SET sync_end_time = $1, sync_duration_ms = $2, sync_status = 'failed', error_message = $3
                     WHERE id = $4`,
                    [syncEndTime, syncDuration, error.message, logId]
                ).catch(logError => {
                    console.error('更新同步日志失败:', logError);
                });
            }

            return {
                success: false,
                message: error.message,
                duration: syncDuration
            };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 处理VMware数据并更新数据库
     */
    async processVmwareData(vmwareData, dataSourceTime) {
        let total = 0;
        let success = 0;
        let failed = 0;
        let newRecords = 0;
        let updatedRecords = 0;

        for (const vmData of vmwareData) {
            total++;
            
            try {
                // 解析虚拟机数据
                const processedData = this.parseVmData(vmData, dataSourceTime);
                
                // 检查记录是否存在
                const existingRecord = await this.db.query(
                    `SELECT id, version_num FROM ${this.tableName} WHERE vm_name = $1 AND is_deleted = FALSE`,
                    [processedData.vm_name]
                );

                if (existingRecord.rows.length > 0) {
                    // 更新现有记录
                    const currentVersion = existingRecord.rows[0].version_num || 1;
                    await this.updateVmRecord(existingRecord.rows[0].id, processedData, currentVersion + 1);
                    updatedRecords++;
                } else {
                    // 插入新记录
                    await this.insertVmRecord(processedData);
                    newRecords++;
                }
                
                success++;
            } catch (error) {
                console.error(`处理虚拟机数据失败:`, error, vmData);
                failed++;
            }
        }

        return { total, success, failed, newRecords, updatedRecords };
    }

    /**
     * 解析虚拟机数据
     */
    parseVmData(vmData, dataSourceTime) {
        return {
            vm_name: vmData.name || 'unknown',
            vcenter_ip: this.parseIpAddress(vmData.vcenter_ip),
            esxi_ip: this.parseIpAddress(vmData.esxi_ip),
            vm_ip: this.parseIpAddress(vmData.vm_ip),
            data_source_time: dataSourceTime,
            raw_data: vmData, // 保存原始数据
            last_sync_time: new Date(),
            sync_status: 'success',
            updated_by: 'vmware-sync-service'
        };
    }

    /**
     * 插入新的虚拟机记录
     */
    async insertVmRecord(data) {
        const query = `
            INSERT INTO ${this.tableName} (
                vm_name, vcenter_ip, esxi_ip, vm_ip, data_source_time,
                raw_data, data_source_url, last_sync_time, sync_status, created_by, updated_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
            )
        `;

        const values = [
            data.vm_name, data.vcenter_ip, data.esxi_ip, data.vm_ip, data.data_source_time,
            JSON.stringify(data.raw_data), this.dataSourceUrl, data.last_sync_time,
            data.sync_status, 'vmware-sync-service', data.updated_by
        ];

        await this.db.query(query, values);
    }

    /**
     * 更新现有的虚拟机记录
     */
    async updateVmRecord(id, data, newVersion) {
        const query = `
            UPDATE ${this.tableName} SET
                vcenter_ip = $2, esxi_ip = $3, vm_ip = $4, data_source_time = $5,
                raw_data = $6, last_sync_time = $7, sync_status = $8,
                updated_at = CURRENT_TIMESTAMP, updated_by = $9, version_num = $10
            WHERE id = $1
        `;

        const values = [
            id, data.vcenter_ip, data.esxi_ip, data.vm_ip, data.data_source_time,
            JSON.stringify(data.raw_data), data.last_sync_time, data.sync_status,
            data.updated_by, newVersion
        ];

        await this.db.query(query, values);
    }

    /**
     * 工具方法：安全解析IP地址
     */
    parseIpAddress(value) {
        if (!value || value === '') return null;
        // 简单的IP地址验证，如果不是有效IP则返回null
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^fec0::/;
        return ipRegex.test(value) ? value : null;
    }

    /**
     * 工具方法：安全解析浮点数
     */
    parseFloat(value) {
        if (value === null || value === undefined || value === '') return null;
        const parsed = parseFloat(value);
        return isNaN(parsed) ? null : parsed;
    }

    /**
     * 工具方法：安全解析整数
     */
    parseInt(value) {
        if (value === null || value === undefined || value === '') return null;
        const parsed = parseInt(value);
        return isNaN(parsed) ? null : parsed;
    }

    /**
     * 工具方法：安全解析布尔值
     */
    parseBoolean(value) {
        if (value === null || value === undefined || value === '') return null;
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
            return value.toLowerCase() === 'true' || value === '1';
        }
        return Boolean(value);
    }

    /**
     * 工具方法：安全解析日期
     */
    parseDate(value) {
        if (!value) return null;
        try {
            const date = new Date(value);
            return isNaN(date.getTime()) ? null : date;
        } catch (error) {
            return null;
        }
    }

    /**
     * 获取同步历史记录
     */
    async getSyncHistory(limit = 10) {
        try {
            const result = await this.db.query(
                `SELECT * FROM cmdb_vmware_sync_logs 
                 ORDER BY sync_start_time DESC 
                 LIMIT $1`,
                [limit]
            );
            return result.rows;
        } catch (error) {
            console.error('获取同步历史失败:', error);
            return [];
        }
    }

    /**
     * 清理旧的同步日志
     */
    async cleanupOldLogs(daysToKeep = 30) {
        try {
            const result = await this.db.query(
                `DELETE FROM cmdb_vmware_sync_logs 
                 WHERE sync_start_time < NOW() - INTERVAL '${daysToKeep} days'`
            );
            console.log(`清理了 ${result.rowCount} 条旧的同步日志记录`);
            return result.rowCount;
        } catch (error) {
            console.error('清理旧同步日志失败:', error);
            return 0;
        }
    }
}

module.exports = VmwareSyncService;