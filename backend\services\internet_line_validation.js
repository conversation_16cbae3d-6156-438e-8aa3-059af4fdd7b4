/**
 * 互联网线路管理数据验证服务
 */

// 验证线路名称
const validateLineName = (lineName) => {
  if (!lineName || typeof lineName !== 'string') {
    return { valid: false, message: '线路名称不能为空' };
  }
  
  if (lineName.trim().length === 0) {
    return { valid: false, message: '线路名称不能为空' };
  }
  
  if (lineName.length > 100) {
    return { valid: false, message: '线路名称长度不能超过100个字符' };
  }
  
  return { valid: true };
};

// 验证运营商
const validateProvider = (provider) => {
  if (!provider || typeof provider !== 'string') {
    return { valid: false, message: '运营商不能为空' };
  }
  
  const validProviders = ['中国电信', '中国联通', '中国移动', '其他'];
  if (!validProviders.includes(provider)) {
    return { valid: false, message: '运营商必须是：中国电信、中国联通、中国移动或其他' };
  }
  
  return { valid: true };
};

// 验证线路类型
const validateLineType = (lineType) => {
  if (!lineType || typeof lineType !== 'string') {
    return { valid: false, message: '线路类型不能为空' };
  }
  
  const validTypes = ['专线', '宽带', '光纤', 'VPN'];
  if (!validTypes.includes(lineType)) {
    return { valid: false, message: '线路类型必须是：专线、宽带、光纤或VPN' };
  }
  
  return { valid: true };
};

// 验证带宽
const validateBandwidth = (bandwidth) => {
  if (!bandwidth || typeof bandwidth !== 'string') {
    return { valid: false, message: '带宽不能为空' };
  }
  
  // 验证带宽格式，支持如：100M、1G、500K等格式
  const bandwidthPattern = /^\d+(\.\d+)?(M|G|K|Mbps|Gbps|Kbps)$/i;
  if (!bandwidthPattern.test(bandwidth.trim())) {
    return { valid: false, message: '带宽格式不正确，请使用如：100M、1G、500K等格式' };
  }
  
  return { valid: true };
};

// 验证IP段
const validateIpRange = (ipRange) => {
  if (!ipRange) {
    return { valid: true }; // IP段为可选字段
  }
  
  if (typeof ipRange !== 'string') {
    return { valid: false, message: 'IP段格式不正确' };
  }
  
  const trimmedIpRange = ipRange.trim();
  
  if (!trimmedIpRange.includes('/')) {
    return { valid: false, message: 'IP段格式不正确，应为 ***********/24 或 2001:db8::/32 格式' };
  }
  
  const [ip, mask] = trimmedIpRange.split('/');
  const maskNum = parseInt(mask);
  
  // 检查是否为IPv4
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipv4Regex.test(ip)) {
    // IPv4验证
    const parts = ip.split('.');
    for (const part of parts) {
      const num = parseInt(part);
      if (num < 0 || num > 255) {
        return { valid: false, message: 'IPv4地址格式不正确，每段应在0-255之间' };
      }
    }
    if (maskNum < 0 || maskNum > 32) {
      return { valid: false, message: 'IPv4掩码应在0-32之间' };
    }
  } else {
    // IPv6验证
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/;
    
    if (!ipv6Regex.test(ip)) {
      return { valid: false, message: 'IPv6地址格式不正确' };
    }
    
    if (maskNum < 0 || maskNum > 128) {
      return { valid: false, message: 'IPv6掩码应在0-128之间' };
    }
  }
  
  return { valid: true };
};

// 验证机房
const validateDatacenter = (datacenter) => {
  if (!datacenter || typeof datacenter !== 'string') {
    return { valid: false, message: '机房不能为空' };
  }
  
  if (datacenter.trim().length === 0) {
    return { valid: false, message: '机房不能为空' };
  }
  
  if (datacenter.length > 100) {
    return { valid: false, message: '机房名称长度不能超过100个字符' };
  }
  
  return { valid: true };
};

// 验证月费用
const validateMonthlyFee = (monthlyFee) => {
  if (monthlyFee === null || monthlyFee === undefined || monthlyFee === '') {
    return { valid: true }; // 月费用为可选字段
  }
  
  const fee = parseFloat(monthlyFee);
  if (isNaN(fee)) {
    return { valid: false, message: '月费用必须是数字' };
  }
  
  if (fee < 0) {
    return { valid: false, message: '月费用不能为负数' };
  }
  
  if (fee > 999999.99) {
    return { valid: false, message: '月费用不能超过999999.99' };
  }
  
  return { valid: true };
};

// 验证合同日期
const validateContractDates = (startDate, endDate) => {
  if (!startDate && !endDate) {
    return { valid: true }; // 合同日期为可选字段
  }
  
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      return { valid: false, message: '合同开始日期必须早于结束日期' };
    }
  }
  
  return { valid: true };
};

// 验证合同编号
const validateContractNumber = (contractNumber) => {
  if (!contractNumber) {
    return { valid: true }; // 合同编号为可选字段
  }
  
  if (typeof contractNumber !== 'string') {
    return { valid: false, message: '合同编号格式不正确' };
  }
  
  if (contractNumber.length > 100) {
    return { valid: false, message: '合同编号长度不能超过100个字符' };
  }
  
  return { valid: true };
};

// 验证网关IP
const validateGatewayIp = (gatewayIp) => {
  if (!gatewayIp) {
    return { valid: true }; // 网关IP为可选字段
  }
  
  if (typeof gatewayIp !== 'string') {
    return { valid: false, message: '网关IP格式不正确' };
  }
  
  const trimmedIp = gatewayIp.trim();
  
  // 检查是否为IPv4
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipv4Regex.test(trimmedIp)) {
    // IPv4验证
    const parts = trimmedIp.split('.');
    for (const part of parts) {
      const num = parseInt(part);
      if (num < 0 || num > 255) {
        return { valid: false, message: '网关IPv4地址格式不正确，每段应在0-255之间' };
      }
    }
  } else {
    // IPv6验证
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/;
    
    if (!ipv6Regex.test(trimmedIp)) {
      return { valid: false, message: '网关IP地址格式不正确，请输入有效的IPv4或IPv6地址' };
    }
  }
  
  return { valid: true };
};

// 验证防火墙IP
const validateFirewallIp = (firewallIp) => {
  if (!firewallIp) {
    return { valid: true }; // 防火墙IP为可选字段
  }
  
  if (typeof firewallIp !== 'string') {
    return { valid: false, message: '防火墙IP格式不正确' };
  }
  
  const trimmedIp = firewallIp.trim();
  
  // 检查是否为IPv4
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipv4Regex.test(trimmedIp)) {
    // IPv4验证
    const parts = trimmedIp.split('.');
    for (const part of parts) {
      const num = parseInt(part);
      if (num < 0 || num > 255) {
        return { valid: false, message: '防火墙IPv4地址格式不正确，每段应在0-255之间' };
      }
    }
  } else {
    // IPv6验证
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/;
    
    if (!ipv6Regex.test(trimmedIp)) {
      return { valid: false, message: '防火墙IP地址格式不正确，请输入有效的IPv4或IPv6地址' };
    }
  }
  
  return { valid: true };
};

// 综合验证线路数据
const validateInternetLineData = (data) => {
  const validations = [
    validateLineName(data.lineName),
    validateProvider(data.provider),
    validateLineType(data.lineType),
    validateBandwidth(data.bandwidth),
    validateIpRange(data.ipRange),
    validateGatewayIp(data.gatewayIp),
    validateFirewallIp(data.firewallIp),
    validateDatacenter(data.datacenter),
    validateMonthlyFee(data.monthlyFee),
    validateContractDates(data.contractStartDate, data.contractEndDate),
    validateContractNumber(data.contractNumber)
  ];
  
  for (const validation of validations) {
    if (!validation.valid) {
      return validation;
    }
  }
  
  return { valid: true };
};

module.exports = {
  validateLineName,
  validateProvider,
  validateLineType,
  validateBandwidth,
  validateIpRange,
  validateGatewayIp,
  validateFirewallIp,
  validateDatacenter,
  validateMonthlyFee,
  validateContractDates,
  validateContractNumber,
  validateInternetLineData
};