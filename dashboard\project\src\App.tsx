import { useState, useEffect } from 'react';
import {
  Monitor, Server, Database, Activity, Wifi, HardDrive,
  MapPin, AlertTriangle
} from 'lucide-react';
import {
  EnhancedCard,
  TechProgressBar,
  MonitoringCoverageCarousel,
  DeviceOnlineCarousel,
  AlertRankingCarousel,
  AlertWaterLevelChart,
  CMDBDataCenterMap
} from './components';

// 覆盖率数据配置 - 支持18个指标
const coverageMetrics = [
  { name: '网络设备', value: 95.74, total: 235, covered: 225, icon: <Wifi className="w-5 h-5" /> },
  { name: '一级系统服务器', value: 98.74, total: 159, covered: 157, icon: <Server className="w-5 h-5" /> },
  { name: '二级系统服务器', value: 100.00, total: 128, covered: 128, icon: <Database className="w-5 h-5" /> },
  { name: '存储设备', value: 92.50, total: 80, covered: 74, icon: <HardDrive className="w-5 h-5" /> },
  { name: '安全设备', value: 89.30, total: 65, covered: 58, icon: <Monitor className="w-5 h-5" /> },
  { name: '负载均衡器', value: 96.20, total: 42, covered: 41, icon: <Activity className="w-5 h-5" /> },
  { name: '核心交换机', value: 100.00, total: 35, covered: 35, icon: <Wifi className="w-5 h-5" /> },
  { name: '汇聚交换机', value: 94.80, total: 58, covered: 55, icon: <Wifi className="w-5 h-5" /> },
  { name: '接入交换机', value: 91.20, total: 125, covered: 114, icon: <Wifi className="w-5 h-5" /> },
  { name: '防火墙', value: 100.00, total: 28, covered: 28, icon: <Monitor className="w-5 h-5" /> },
  { name: 'VPN网关', value: 87.50, total: 16, covered: 14, icon: <Monitor className="w-5 h-5" /> },
  { name: '应用服务器', value: 93.60, total: 89, covered: 83, icon: <Server className="w-5 h-5" /> },
  { name: '数据库服务器', value: 98.50, total: 67, covered: 66, icon: <Database className="w-5 h-5" /> },
  { name: '中间件服务器', value: 95.40, total: 43, covered: 41, icon: <Server className="w-5 h-5" /> },
  { name: '缓存服务器', value: 88.90, total: 27, covered: 24, icon: <Server className="w-5 h-5" /> },
  { name: '消息队列', value: 92.30, total: 13, covered: 12, icon: <Activity className="w-5 h-5" /> },
  { name: '监控探针', value: 97.80, total: 156, covered: 153, icon: <Monitor className="w-5 h-5" /> },
  { name: '日志收集器', value: 90.60, total: 32, covered: 29, icon: <Activity className="w-5 h-5" /> }
];

// 在线状态数据
const onlineMetrics = [
  { name: '网络设备', value: 100.00, total: 232, online: 232, icon: <Wifi className="w-5 h-5" /> },
  { name: '实体服务器', value: 100.00, total: 210, online: 210, icon: <Server className="w-5 h-5" /> },
  { name: '虚拟化服务器', value: 84.78, total: 361, online: 323, icon: <Database className="w-5 h-5" /> }
];

// 告警排名数据 - 按系统排名
const systemAlertRankings = [
  {
    systemName: '交易系统',
    adminName: '张三',
    alertCount: 149,
    trend: 'up' as const,
    dailyData: [8, 12, 15, 11, 9, 14, 18, 16, 13, 17, 19, 15, 12, 16, 20]
  },
  {
    systemName: '风控系统',
    adminName: '李四',
    alertCount: 122,
    trend: 'down' as const,
    dailyData: [15, 18, 16, 14, 12, 10, 8, 9, 7, 6, 8, 5, 4, 6, 8]
  },
  {
    systemName: '结算系统',
    adminName: '王五',
    alertCount: 117,
    trend: 'up' as const,
    dailyData: [5, 7, 8, 6, 9, 11, 10, 12, 14, 13, 15, 16, 14, 17, 18]
  },
  {
    systemName: '监控系统',
    adminName: '赵六',
    alertCount: 114,
    trend: 'down' as const,
    dailyData: [12, 14, 13, 11, 10, 9, 8, 7, 6, 8, 7, 5, 6, 4, 7]
  },
  {
    systemName: '报表系统',
    adminName: '钱七',
    alertCount: 110,
    trend: 'up' as const,
    dailyData: [4, 5, 6, 7, 8, 6, 9, 10, 8, 11, 12, 10, 13, 14, 15]
  },
  {
    systemName: '备份系统',
    adminName: '孙八',
    alertCount: 102,
    trend: 'down' as const,
    dailyData: [10, 12, 11, 9, 8, 7, 6, 5, 7, 6, 4, 5, 3, 4, 6]
  },
  {
    systemName: '日志系统',
    adminName: '周九',
    alertCount: 100,
    trend: 'up' as const,
    dailyData: [3, 4, 5, 4, 6, 7, 5, 8, 9, 7, 10, 11, 9, 12, 13]
  },
  {
    systemName: '认证系统',
    adminName: '吴十',
    alertCount: 97,
    trend: 'down' as const,
    dailyData: [9, 8, 7, 6, 8, 7, 5, 6, 4, 5, 3, 4, 2, 3, 5]
  },
  {
    systemName: '消息系统',
    adminName: '郑一',
    alertCount: 93,
    trend: 'up' as const,
    dailyData: [2, 3, 4, 3, 5, 6, 4, 7, 8, 6, 9, 10, 8, 11, 12]
  },
  {
    systemName: '存储系统',
    adminName: '冯二',
    alertCount: 92,
    trend: 'down' as const,
    dailyData: [8, 7, 6, 5, 7, 6, 4, 5, 3, 4, 2, 3, 1, 2, 4]
  }
];

// 告警排名数据 - 按管理员排名
const adminAlertRankings = [
  {
    systemName: '交易系统',
    adminName: '张三',
    alertCount: 149,
    trend: 'up' as const,
    dailyData: [8, 12, 15, 11, 9, 14, 18, 16, 13, 17, 19, 15, 12, 16, 20]
  },
  {
    systemName: '报表系统',
    adminName: '钱七',
    alertCount: 135,
    trend: 'down' as const,
    dailyData: [18, 16, 15, 14, 12, 11, 10, 9, 8, 7, 9, 6, 5, 7, 9]
  },
  {
    systemName: '风控系统',
    adminName: '李四',
    alertCount: 122,
    trend: 'up' as const,
    dailyData: [6, 8, 7, 9, 10, 8, 11, 12, 10, 13, 14, 12, 15, 16, 14]
  },
  {
    systemName: '结算系统',
    adminName: '王五',
    alertCount: 117,
    trend: 'down' as const,
    dailyData: [14, 13, 12, 11, 10, 9, 8, 7, 9, 8, 6, 7, 5, 6, 8]
  },
  {
    systemName: '监控系统',
    adminName: '赵六',
    alertCount: 114,
    trend: 'up' as const,
    dailyData: [5, 6, 7, 6, 8, 9, 7, 10, 11, 9, 12, 13, 11, 14, 15]
  },
  {
    systemName: '备份系统',
    adminName: '孙八',
    alertCount: 102,
    trend: 'down' as const,
    dailyData: [12, 11, 10, 9, 8, 7, 6, 5, 7, 6, 4, 5, 3, 4, 6]
  },
  {
    systemName: '日志系统',
    adminName: '周九',
    alertCount: 100,
    trend: 'up' as const,
    dailyData: [4, 5, 6, 5, 7, 8, 6, 9, 10, 8, 11, 12, 10, 13, 14]
  },
  {
    systemName: '认证系统',
    adminName: '吴十',
    alertCount: 97,
    trend: 'down' as const,
    dailyData: [10, 9, 8, 7, 6, 8, 7, 5, 6, 4, 5, 3, 4, 2, 5]
  },
  {
    systemName: '消息系统',
    adminName: '郑一',
    alertCount: 93,
    trend: 'up' as const,
    dailyData: [3, 4, 5, 4, 6, 7, 5, 8, 9, 7, 10, 11, 9, 12, 13]
  },
  {
    systemName: '存储系统',
    adminName: '冯二',
    alertCount: 92,
    trend: 'down' as const,
    dailyData: [9, 8, 7, 6, 5, 7, 6, 4, 5, 3, 4, 2, 3, 1, 4]
  }
];

// 新增：性能数据

// 新增：地理位置数据

function App() {
  const [currentTime, setCurrentTime] = useState(new Date());

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  return (
    <div className="min-h-screen bg-tech-gradient text-white overflow-hidden font-tech">
      {/* 增强的动态科技感背景 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 动态网格背景 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'gridMove 20s linear infinite'
          }}></div>
        </div>

        {/* 增强的浮动几何图形 */}
        <div className="absolute inset-0 opacity-15">
          {/* 大型浮动圆环 */}
          <div className="absolute top-10 left-10 w-96 h-96 border-2 border-neon-blue/40 rounded-full floating pulse-glow"></div>
          <div className="absolute top-1/2 right-20 w-72 h-72 border-2 border-neon-cyan/40 rounded-full floating-slow" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-20 left-1/3 w-48 h-48 border-2 border-neon-purple/40 rounded-full floating" style={{ animationDelay: '2s' }}></div>

          {/* 新增：六边形装饰 */}
          <div className="absolute top-1/4 right-1/4 w-32 h-32 border border-neon-green/30 transform rotate-45 floating" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute bottom-1/3 left-1/5 w-24 h-24 border border-neon-orange/30 transform rotate-12 floating-slow" style={{ animationDelay: '1.5s' }}></div>
        </div>

        {/* 增强粒子效果 */}
        <div className="absolute inset-0 opacity-25">
          {/* 更多粒子 */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-${Math.random() > 0.5 ? '2' : '1'} h-${Math.random() > 0.5 ? '2' : '1'} rounded-full particle-float`}
              style={{
                backgroundColor: ['#00d4ff', '#00ffff', '#10b981', '#8b5cf6', '#f59e0b'][Math.floor(Math.random() * 5)],
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`
              }}
            />
          ))}
        </div>

        {/* 数据流线条 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-blue/50 to-transparent"
            style={{ animation: 'dataFlow 6s ease-in-out infinite' }}></div>
          <div className="absolute top-2/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-cyan/50 to-transparent"
            style={{ animation: 'dataFlow 8s ease-in-out infinite', animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-green/50 to-transparent"
            style={{ animation: 'dataFlow 7s ease-in-out infinite', animationDelay: '4s' }}></div>
        </div>

        {/* 全息扫描线 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-neon-cyan to-transparent"
            style={{ animation: 'scanMove 8s ease-in-out infinite' }} />
          <div className="absolute h-full w-0.5 bg-gradient-to-b from-transparent via-neon-blue to-transparent"
            style={{ animation: 'scanLineVertical 10s ease-in-out infinite', animationDelay: '3s' }} />
        </div>
      </div>

      <div className="relative z-10 p-4">
        {/* 驾驶舱风格标题栏 */}
        <header className="relative flex items-center justify-center mb-8">
          {/* 驾驶舱角落装饰 */}
          {/* <div className="cockpit-corner cockpit-corner-tl"></div>
          <div className="cockpit-corner cockpit-corner-tr"></div>
          <div className="cockpit-corner cockpit-corner-bl"></div>
          <div className="cockpit-corner cockpit-corner-br"></div> */}

          {/* 左侧Logo和信息 - HUD风格 */}
          <div className="absolute left-0 flex items-center space-x-4">
            <div className="dashboard-border p-2">
              <div className="w-14 h-14 rounded-lg overflow-hidden neon-glow hover-lift">
                <img
                  src="/src/logo/logo.png"
                  alt="Logo"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <div className="hud-element px-4 py-2">
              <p className="text-lg font-bold text-white">长江期货</p>
              {/* <p className="text-sm text-gray-400">IT资源管理中心</p> */}
              <div className="data-flow-indicator mt-2"></div>
            </div>
          </div>

          {/* 居中标题 - 驾驶舱窗框装饰 */}
          <div className="text-center cockpit-frame">
            <div className="dashboard-border px-8 py-4 bg-gradient-to-r from-tech-surface/50 to-tech-darker/50 backdrop-blur-md">
              <h1 className="text-4xl font-bold tech-gradient-text mb-2">
                IT资源管理系统驾驶舱
              </h1>
              <div className="text-sm text-gray-400 mb-2">
                Enterprise IT Resource Management Dashboard
              </div>
              <div className="data-flow-indicator"></div>
            </div>
          </div>

          {/* 右侧系统信息 - 简洁版 */}
          <div className="absolute right-0 flex items-center space-x-4">
            {/* 系统时间显示器 */}
            <div className="hud-element px-4 py-2 hover-lift">
              <div className="text-xs text-gray-400 mb-1">系统时间</div>
              <div className="text-lg font-mono text-neon-blue">
                {formatTime(currentTime)}
              </div>
              <div className="data-flow-indicator mt-2"></div>
            </div>
          </div>
        </header>

        {/* 简洁分割线 */}
        <div className="relative mb-4">
          <div className="h-px bg-gradient-to-r from-transparent via-neon-blue/50 to-transparent"></div>
        </div>



        <div className="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
          {/* 左侧列 - 监控数据 */}
          <div className="col-span-3 flex flex-col space-y-6">
            {/* 3D指标卡片组 - 超透明玻璃效果 */}
            {/* <div className="grid grid-cols-2 gap-4 glass-ultra-transparent rounded-xl p-4">
              <MetricCard3D
                title="CPU使用率"
                value={75}
                unit="%"
                change={5}
                trend="up"
                icon={Cpu}
                color="#00d4ff"
                size="sm"
                animationDelay={100}
                className="glass-blue"
              />
              <MetricCard3D
                title="内存使用"
                value={68}
                unit="%"
                change={-2}
                trend="down"
                icon={MemoryStick}
                color="#10b981"
                size="sm"
                animationDelay={200}
                className="glass-green"
              />
              <MetricCard3D
                title="网络流量"
                value={82}
                unit="Mbps"
                change={8}
                trend="up"
                icon={Network}
                color="#f59e0b"
                size="sm"
                animationDelay={300}
                className="glass-gradient-bg"
              />
              <MetricCard3D
                title="存储空间"
                value={45}
                unit="%"
                change={1}
                trend="up"
                icon={HardDrive}
                color="#8b5cf6"
                size="sm"
                animationDelay={400}
                className="glass-purple"
              />
            </div> */}

            {/* 监控覆盖率 - 青色玻璃效果 */}
            <div className="h-[570px] glass-cyan rounded-xl p-2">
              <MonitoringCoverageCarousel
                metrics={coverageMetrics}
                itemsPerPage={3}
                autoPlayInterval={5000}
                className="h-full"
              />
            </div>

            {/* 设备在线情况 - 青色玻璃效果 */}
            <div className="h-[400px] glass-cyan rounded-xl p-2">
              <DeviceOnlineCarousel
                metrics={onlineMetrics}
                itemsPerPage={3}
                autoPlayInterval={5000}
                className="h-full"
              />
            </div>
          </div>

          {/* 中央区域 - 主要数据展示 */}
          <div className="col-span-6 flex flex-col space-y-6">
            {/* 顶部：设备管理总数 + 数据中心分布 */}
            <div className="grid grid-cols-2 gap-6 h-[570px]">
              {/* 设备管理总数 */}
              <div className="glass-cyan rounded-xl p-4">
                <EnhancedCard
                  title="设备管理总数"
                  icon={<Server className="w-5 h-5" />}
                  className="h-full flex flex-col glass-ultra-transparent"
                  animationDelay={400}
                >
                  <div className="text-center mb-4">
                    <div className="text-5xl font-bold tech-gradient-text count-animation mb-3">
                      826
                    </div>
                    <div className="text-xs text-gray-400">
                      Total Managed Devices
                    </div>
                  </div>

                  <div className="flex justify-center items-center space-x-4">
                    <div className="text-center glass-ultra-transparent rounded-lg p-3 hover-lift flex-1">
                      <div className="w-12 h-12 bg-gradient-to-r from-neon-blue/20 to-neon-cyan/20 rounded-lg flex items-center justify-center mb-2 neon-glow mx-auto">
                        <Server className="w-5 h-5 text-neon-blue" />
                      </div>
                      <div className="text-lg font-bold text-neon-blue mb-1">23</div>
                      <div className="text-xs text-gray-400 leading-relaxed">待登记设备</div>
                    </div>

                    <div className="text-center glass-ultra-transparent rounded-lg p-3 hover-lift flex-1">
                      <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan/20 to-neon-blue/20 rounded-lg flex items-center justify-center mb-2 neon-glow mx-auto">
                        <HardDrive className="w-5 h-5 text-neon-cyan" />
                      </div>
                      <div className="text-lg font-bold text-neon-cyan mb-1">15</div>
                      <div className="text-xs text-gray-400 leading-relaxed">待回收资源</div>
                    </div>

                    <div className="text-center glass-ultra-transparent rounded-lg p-3 hover-lift flex-1">
                      <div className="w-12 h-12 bg-gradient-to-r from-neon-green/20 to-neon-blue/20 rounded-lg flex items-center justify-center mb-2 neon-glow-green mx-auto">
                        <Database className="w-5 h-5 text-neon-green" />
                      </div>
                      <div className="text-lg font-bold text-neon-green mb-1">+47</div>
                      <div className="text-xs text-gray-400 leading-relaxed">虚拟化变化</div>
                    </div>
                  </div>
                </EnhancedCard>
              </div>

              {/* 数据中心分布 */}
              <div className="glass-cyan rounded-xl p-3">
                <CMDBDataCenterMap
                  title="数据中心分布"
                  icon={<MapPin className="w-5 h-5" />}
                  animationDelay={600}
                  refreshInterval={300000}
                  className="h-full glass-ultra-transparent"
                />
              </div>
            </div>

            {/* 性能数据可视化 - 蓝色玻璃效果 */}
            {/* <div className="h-[280px] glass-blue rounded-xl p-3">
              <DataVisualizationCard
                title="系统性能监控"
                data={performanceData}
                type="bar"
                animationDelay={600}
                showTrend={true}
                height={180}
                className="glass-ultra-transparent"
              />
            </div> */}

            {/* 15日内告警排名 - 青色玻璃效果 */}
            <div className="h-[500px] glass-cyan rounded-xl p-3">
              <AlertRankingCarousel
                systemRankings={systemAlertRankings}
                adminRankings={adminAlertRankings}
                autoPlayInterval={8000}
                className="h-full"
              />
            </div>
          </div>

          {/* 右侧列 - 地图和监控 */}
          <div className="col-span-3 flex flex-col space-y-6">

            {/* CJM告警统计 - 青色玻璃效果 */}
            <div className="h-[570px] glass-cyan rounded-xl p-3">
              <div className="glass-ultra-transparent rounded-lg p-4 h-full flex flex-col">
                <div className="flex flex-col items-center mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle className="w-5 h-5 text-neon-blue" />
                    <h3 className="text-lg font-bold tech-gradient-text">CJM告警统计</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-neon-green rounded-full pulse-glow" />
                    <span className="text-xs text-gray-400">实时数据</span>
                  </div>
                </div>

                <div className="flex justify-around mb-6">
                  <div className="text-center">
                    <TechProgressBar
                      value={75}
                      max={100}
                      label="今日告警数"
                      variant="circular"
                      size={60}
                      glowColor="#3b82f6"
                    />
                    <div className="text-lg font-bold text-neon-blue mt-2">6</div>
                  </div>
                  <div className="text-center">
                    <TechProgressBar
                      value={65}
                      max={100}
                      label="7日内告警数"
                      variant="circular"
                      size={60}
                      glowColor="#06b6d4"
                    />
                    <div className="text-lg font-bold text-neon-cyan mt-2">41</div>
                  </div>
                  <div className="text-center">
                    <TechProgressBar
                      value={85}
                      max={100}
                      label="30日内告警数"
                      variant="circular"
                      size={60}
                      glowColor="#10b981"
                    />
                    <div className="text-lg font-bold text-neon-green mt-2">757</div>
                  </div>
                </div>

                <div className="flex-1 flex flex-col">
                  <h4 className="text-sm text-gray-300 mb-3">30日内CJM告警级别统计</h4>
                  <div className="flex-1 flex flex-col items-center justify-center">
                    <div className="relative flex justify-center mb-4">
                      <svg width="140" height="140" viewBox="0 0 140 140" className="transform">
                        <defs>
                          {/* 渐变定义 */}
                          <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#10b981" />
                            <stop offset="100%" stopColor="#059669" />
                          </linearGradient>
                          <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#f59e0b" />
                            <stop offset="100%" stopColor="#d97706" />
                          </linearGradient>
                          <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#ef4444" />
                            <stop offset="100%" stopColor="#dc2626" />
                          </linearGradient>

                          {/* 发光滤镜 */}
                          <filter id="glowFilter">
                            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                            <feMerge>
                              <feMergeNode in="coloredBlur" />
                              <feMergeNode in="SourceGraphic" />
                            </feMerge>
                          </filter>
                        </defs>

                        {/* 背景圆环 */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="rgba(255, 255, 255, 0.1)"
                          strokeWidth="12"
                        />

                        {/* 一般告警 - 45.2% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#greenGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${45.2 * 2.827} ${(100 - 45.2) * 2.827}`}
                          strokeDashoffset="0"
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                        />

                        {/* 严重告警 - 37.9% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#orangeGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${37.9 * 2.827} ${(100 - 37.9) * 2.827}`}
                          strokeDashoffset={`-${45.2 * 2.827}`}
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                          style={{ animationDelay: '200ms' }}
                        />

                        {/* 紧急告警 - 16.9% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#redGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${16.9 * 2.827} ${(100 - 16.9) * 2.827}`}
                          strokeDashoffset={`-${(45.2 + 37.9) * 2.827}`}
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                          style={{ animationDelay: '400ms' }}
                        />

                        {/* 中心文字 */}
                        <text x="70" y="65" textAnchor="middle" className="text-lg font-bold fill-white count-animation">
                          757
                        </text>
                        <text x="70" y="80" textAnchor="middle" className="text-xs fill-gray-400">
                          总告警
                        </text>
                      </svg>
                    </div>
                    <div className="space-y-1 text-xs w-full">
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-green rounded mr-2"></div>
                          一般告警
                        </span>
                        <span>45.2%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-orange rounded mr-2"></div>
                          严重告警
                        </span>
                        <span>37.9%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-red rounded mr-2"></div>
                          紧急告警
                        </span>
                        <span>16.9%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 当前告警未处理数 - 青色玻璃效果 */}
            <div className="h-[400px] glass-cyan rounded-xl p-3">
              <AlertWaterLevelChart
                className="h-full glass-ultra-transparent"
                animationDelay={1400}
              />
            </div>
          </div>
        </div>

        {/* 驾驶舱底部控制台装饰 */}
        <div className="-mt-20 relative">
          {/* 底部控制面板 */}
          <div className="flex justify-between items-center">
            {/* 左侧控制组 */}
            <div className="flex space-x-4">
              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-green rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">主系统</span>
                  <div className="text-xs text-neon-green font-mono">ONLINE</div>
                </div>
              </div>

              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-blue rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">通信</span>
                  <div className="text-xs text-neon-blue font-mono">ACTIVE</div>
                </div>
              </div>

              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-orange rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">导航</span>
                  <div className="text-xs text-neon-orange font-mono">READY</div>
                </div>
              </div>
            </div>

            {/* 中央状态显示 */}
            {/* <div className="hud-element px-6 py-3">
              <div className="text-center">
                <div className="text-xs text-gray-400 mb-1">MISSION STATUS</div>
                <div className="text-sm text-neon-green font-bold">OPERATIONAL</div>
                <div className="data-flow-indicator mt-2"></div>
              </div>
            </div> */}

            {/* 右侧控制组 */}
            <div className="flex space-x-4">
              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-cyan rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">传感器</span>
                  <div className="text-xs text-neon-cyan font-mono">SCAN</div>
                </div>
              </div>

              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-purple rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">防护</span>
                  <div className="text-xs text-neon-purple font-mono">SHIELD</div>
                </div>
              </div>

              <div className="dashboard-border px-4 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-neon-red rounded-full pulse-glow"></div>
                  <span className="text-xs text-gray-400">警报</span>
                  <div className="text-xs text-neon-red font-mono">CLEAR</div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部装饰线条 */}
          <div className="mt-4 relative">
            <div className="h-px bg-gradient-to-r from-transparent via-neon-blue/50 to-transparent"></div>
            <div className="absolute left-1/2 top-0 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 border-2 border-neon-blue bg-tech-darker rounded-full">
              <div className="absolute inset-1 bg-neon-blue rounded-full pulse-glow"></div>
            </div>
          </div>

          {/* 驾驶舱网格背景装饰 */}
          <div className="absolute inset-0 cockpit-grid opacity-5 pointer-events-none"></div>
        </div>
      </div>
    </div>
  );
}

export default App;