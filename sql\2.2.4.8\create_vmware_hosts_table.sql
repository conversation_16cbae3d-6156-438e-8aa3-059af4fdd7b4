-- 创建虚拟化信息自动更新表
-- 版本: *******
-- 创建时间: 2025-08-14

-- 创建虚拟化信息自动更新表
CREATE TABLE IF NOT EXISTS cmdb_vmware_info_auto_update (
    id SERIAL PRIMARY KEY,
    vm_name VARCHAR(255) NOT NULL,
    vcenter_ip INET,
    esxi_ip INET,
    vm_ip INET,
    data_source_time TIMESTAMP,
    raw_data JSONB,
    data_source_url VARCHAR(500) NOT NULL DEFAULT 'http://**************:8081/config_files?file=vmware_hosts.json',
    last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status VARCHAR(20) DEFAULT 'pending',
    sync_error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) DEFAULT 'system',
    is_deleted BOOLEAN DEFAULT FALSE,
    version_num INTEGER DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_vm_name ON cmdb_vmware_info_auto_update(vm_name);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_vcenter_ip ON cmdb_vmware_info_auto_update(vcenter_ip);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_esxi_ip ON cmdb_vmware_info_auto_update(esxi_ip);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_vm_ip ON cmdb_vmware_info_auto_update(vm_ip);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_last_sync_time ON cmdb_vmware_info_auto_update(last_sync_time);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_sync_status ON cmdb_vmware_info_auto_update(sync_status);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_info_is_deleted ON cmdb_vmware_info_auto_update(is_deleted);

-- 创建唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS uk_cmdb_vmware_info_vm_name ON cmdb_vmware_info_auto_update(vm_name) WHERE is_deleted = FALSE;

-- 添加表注释
COMMENT ON TABLE cmdb_vmware_info_auto_update IS '虚拟化信息自动更新表 - 存储从VMware接口获取的虚拟机信息';

-- 添加列注释
COMMENT ON COLUMN cmdb_vmware_info_auto_update.id IS '主键ID';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.vm_name IS '虚拟机名称';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.vcenter_ip IS 'vCenter IP地址';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.esxi_ip IS 'ESXi主机IP地址';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.vm_ip IS '虚拟机IP地址';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.data_source_time IS '数据源时间';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.raw_data IS '原始JSON数据';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.data_source_url IS '数据源URL';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.last_sync_time IS '最后同步时间';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.sync_status IS '同步状态: pending, success, failed';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.sync_error_message IS '同步错误信息';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.created_at IS '创建时间';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.created_by IS '创建人';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.updated_at IS '更新时间';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.updated_by IS '更新人';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.is_deleted IS '是否删除';
COMMENT ON COLUMN cmdb_vmware_info_auto_update.version_num IS '版本号';

-- 创建视图
CREATE OR REPLACE VIEW v_cmdb_vmware_info_auto_update AS
SELECT 
    id,
    vm_name,
    vcenter_ip,
    esxi_ip,
    vm_ip,
    data_source_time,
    data_source_url,
    last_sync_time,
    sync_status,
    sync_error_message,
    TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at_formatted,
    created_by,
    TO_CHAR(updated_at, 'YYYY-MM-DD HH24:MI:SS') as updated_at_formatted,
    updated_by,
    version_num
FROM cmdb_vmware_info_auto_update 
WHERE is_deleted = FALSE
ORDER BY last_sync_time DESC, id DESC;

-- 添加视图注释
COMMENT ON VIEW v_cmdb_vmware_info_auto_update IS '虚拟化信息自动更新视图 - 用于查询显示';

-- 创建同步日志表
CREATE TABLE IF NOT EXISTS cmdb_vmware_sync_logs (
    id SERIAL PRIMARY KEY,
    sync_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_end_time TIMESTAMP,
    sync_duration_ms INTEGER,
    total_records INTEGER DEFAULT 0,
    success_records INTEGER DEFAULT 0,
    failed_records INTEGER DEFAULT 0,
    new_records INTEGER DEFAULT 0,
    updated_records INTEGER DEFAULT 0,
    sync_status VARCHAR(20) DEFAULT 'running',
    error_message TEXT,
    data_source_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建同步日志索引
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_sync_logs_sync_start_time ON cmdb_vmware_sync_logs(sync_start_time);
CREATE INDEX IF NOT EXISTS idx_cmdb_vmware_sync_logs_sync_status ON cmdb_vmware_sync_logs(sync_status);

-- 添加同步日志表注释
COMMENT ON TABLE cmdb_vmware_sync_logs IS 'VMware数据同步日志表 - 记录每次同步的详细信息';

-- 添加同步日志表列注释
COMMENT ON COLUMN cmdb_vmware_sync_logs.id IS '主键ID';
COMMENT ON COLUMN cmdb_vmware_sync_logs.sync_start_time IS '同步开始时间';
COMMENT ON COLUMN cmdb_vmware_sync_logs.sync_end_time IS '同步结束时间';
COMMENT ON COLUMN cmdb_vmware_sync_logs.sync_duration_ms IS '同步耗时(毫秒)';
COMMENT ON COLUMN cmdb_vmware_sync_logs.total_records IS '总记录数';
COMMENT ON COLUMN cmdb_vmware_sync_logs.success_records IS '成功记录数';
COMMENT ON COLUMN cmdb_vmware_sync_logs.failed_records IS '失败记录数';
COMMENT ON COLUMN cmdb_vmware_sync_logs.new_records IS '新增记录数';
COMMENT ON COLUMN cmdb_vmware_sync_logs.updated_records IS '更新记录数';
COMMENT ON COLUMN cmdb_vmware_sync_logs.sync_status IS '同步状态: running, success, failed';
COMMENT ON COLUMN cmdb_vmware_sync_logs.error_message IS '错误信息';
COMMENT ON COLUMN cmdb_vmware_sync_logs.data_source_url IS '数据源URL';
COMMENT ON COLUMN cmdb_vmware_sync_logs.created_at IS '创建时间';