# 服务器管理IP重复性检查功能

## 功能描述

在CMDB服务器管理模块中，新增了管理IP重复性检查功能。该功能确保生命周期状态为"正常"的服务器的管理IP不能重复。

## 实现逻辑

### 检查规则
- 只有当服务器的生命周期状态为"正常"时，才进行管理IP重复性检查
- 其他生命周期状态（如故障、闲置、报废、预报废）的服务器可以使用相同的管理IP
- 检查时会同时考虑数据字典中的状态名称和直接存储的状态值

### 后端实现

#### 1. 新增API端点
- **路径**: `/api/check_server_management_ip`
- **方法**: POST
- **参数**: 
  - `management_ip`: 要检查的管理IP
  - `id`: 当前记录ID（编辑时使用，新增时为null）
- **返回**: 
  - `exists`: 是否存在重复
  - `msg`: 提示信息

#### 2. 修改现有API
- **添加服务器**: `/api/add_cmdb_server_management`
  - 在插入数据前检查管理IP重复性
  - 如果重复，返回错误信息
  
- **更新服务器**: `/api/update_cmdb_server_management`
  - 在更新管理IP时检查重复性
  - 排除当前记录进行检查

#### 3. SQL查询逻辑
```sql
SELECT COUNT(*) as count 
FROM cmdb_server_management s
LEFT JOIN cmdb_data_dictionary d ON d.dict_code = s.operation_status AND d.del_flag = '0'
WHERE s.management_ip = $1 
AND s.del_flag = '0' 
AND (COALESCE(d.dict_name, s.operation_status) = '正常' OR s.operation_status = '正常')
```

### 前端实现

#### 1. 表单验证规则
- 在管理IP字段的验证规则中添加异步验证器
- 实时检查管理IP是否重复
- 显示友好的错误提示信息

#### 2. 验证时机
- 用户输入管理IP后失去焦点时触发验证
- 表单提交前进行最终验证

## 使用场景

### 场景1：新增服务器
1. 用户填写新服务器信息
2. 输入管理IP后，系统自动检查是否与"正常"状态的服务器重复
3. 如果重复，显示错误提示，阻止提交
4. 如果不重复，允许继续操作

### 场景2：编辑服务器
1. 用户修改现有服务器的管理IP
2. 系统检查新IP是否与其他"正常"状态的服务器重复（排除当前记录）
3. 如果重复，显示错误提示，阻止提交
4. 如果不重复，允许保存修改

### 场景3：状态变更
- 当服务器状态从其他状态变更为"正常"时，如果管理IP与其他"正常"状态的服务器重复，会被阻止
- 当服务器状态从"正常"变更为其他状态时，其管理IP可以被其他"正常"状态的服务器使用

## 错误提示信息

- **新增时重复**: "管理IP xxx.xxx.xxx.xxx 已存在于生命周期状态为'正常'的服务器中，不能重复添加"
- **编辑时重复**: "管理IP xxx.xxx.xxx.xxx 已存在于生命周期状态为'正常'的服务器中，不能重复使用"
- **检查失败**: "检查管理IP重复性失败，请稍后重试"

## 技术细节

### 数据库查询优化
- 使用LEFT JOIN关联数据字典表获取状态名称
- 使用COALESCE函数处理空值情况
- 添加适当的索引提高查询性能

### 前端异步验证
- 使用async/await处理异步验证
- 合理的错误处理和用户提示
- 避免频繁的API调用

### 兼容性考虑
- 兼容直接存储状态值和通过数据字典存储的情况
- 处理各种边界情况和异常情况

## 测试建议

1. **正常流程测试**
   - 新增不重复的管理IP
   - 编辑不重复的管理IP
   
2. **重复检查测试**
   - 尝试新增重复的管理IP
   - 尝试编辑为重复的管理IP
   
3. **状态变更测试**
   - 将非正常状态的服务器改为正常状态
   - 将正常状态的服务器改为其他状态
   
4. **边界情况测试**
   - 空IP处理
   - 无效IP格式处理
   - 网络异常处理