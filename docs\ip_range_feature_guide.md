# IP地址段功能使用指南

## 功能概述

互联网线路管理系统现在支持"IP地址段"字段，允许用户为每条互联网线路记录相关的IP地址段信息。

## 功能特性

### 1. 支持的IP格式
- **IPv4 CIDR格式**: `***********/24`、`10.0.0.0/16`
- **IPv6 CIDR格式**: `2001:db8:85a3::8a2e:370:7334/64`、`fe80::/10`

### 2. 字段属性
- **字段名称**: IP地址段
- **是否必填**: 否（可选字段）
- **数据库字段**: `ip_range`
- **最大长度**: 50字符

### 3. 前端显示
- 在新增/编辑对话框中显示IP地址段输入框
- 在数据表格中显示IP地址段列
- 对于过长的IPv6地址，自动进行缩写显示
- 空值显示为 "-"

## 使用方法

### 1. 新增线路时添加IP地址段
1. 点击"新增线路"按钮
2. 填写必填字段（线路名称、运营商、线路类型、带宽、所属机房）
3. 在"IP地址段"字段中输入IP段，如：`***********/24`
4. 点击"确定"保存

### 2. 编辑现有线路的IP地址段
1. 在线路列表中点击"编辑"按钮
2. 修改"IP地址段"字段
3. 点击"更新"保存

### 3. 查看IP地址段信息
- 在线路列表的"IP地址段"列中查看
- 对于IPv6地址，系统会自动缩写显示以节省空间

## 输入格式示例

### IPv4格式
```
***********/24
10.0.0.0/16
**********/12
```

### IPv6格式
```
2001:db8:85a3::8a2e:370:7334/64
fe80::/10
::1/128
```

## 验证规则

### IPv4验证
- 必须符合CIDR格式：`IP地址/掩码`
- IP地址必须是有效的IPv4地址
- 掩码范围：0-32

### IPv6验证
- 必须符合CIDR格式：`IPv6地址/掩码`
- IPv6地址必须是有效的IPv6地址格式
- 掩码范围：0-128

## 错误处理

### 常见错误信息
- `IP段格式不正确，应为 ***********/24 或 2001:db8::/32 格式`
- `IP段的IPv4掩码应在0-32之间`
- `IP段的IPv6掩码应在0-128之间`
- `IP段中的IPv6地址格式不正确`

### 解决方法
1. 检查IP地址格式是否正确
2. 确保包含斜杠和掩码位数
3. 验证掩码位数在有效范围内

## 数据导出

在导出Excel时，IP地址段信息会包含在导出文件中，列名为"IP段"。

## 技术实现

### 前端实现
- 在Vue组件中添加`ip_range`字段
- 实现`formatIpRange()`方法用于显示格式化
- 支持IPv6地址的缩写显示
- **新增验证规则**：
  - 添加了`validateIpRange`自定义验证函数
  - 支持IPv4和IPv6格式的实时验证
  - 非必填字段，空值自动通过验证
  - 提供清晰的错误提示信息

### 后端实现
- 数据库字段：`ip_range VARCHAR(50)`
- 验证逻辑：支持IPv4和IPv6的CIDR格式验证
- API接口：在所有相关接口中支持IP地址段字段
- **增强的验证**：扩展了`validateCIDR`函数以支持IPv6格式

## 注意事项

1. IP地址段为可选字段，可以留空
2. 输入时必须包含掩码位数（如/24、/64）
3. 系统会自动验证IP地址和掩码的有效性
4. IPv6地址在表格中会自动缩写显示以节省空间
5. 导出功能会包含完整的IP地址段信息

## 更新日志

- **版本 *********: 新增IP地址段功能
  - 支持IPv4和IPv6 CIDR格式
  - 前端表格显示和编辑支持
  - 后端验证和存储支持
  - 导出功能包含IP段信息
#
# 问题解决记录

### 前端更新功能400错误修复

**问题描述**: 
在更新互联网线路数据时，前端提示 `POST http://localhost:3000/api/update_internet_line 400 (Bad Request)`

**问题原因**: 
1. **IP验证不一致**: 后端有两个不同的IP验证函数，更新接口使用的函数只支持IPv4
2. **数据映射问题**: 前端发送的是代码值（如 `inet_telecom`, `line_dedicated`），但后端验证函数期望中文名称（如 `中国电信`, `专线`）

**解决方案**: 
1. **修复IP验证**: 更新了 `backend/services/internet_line_validation.js` 中的 `validateIpRange` 函数，使其支持IPv6格式

2. **修复数据映射**: 更新了前端代码，在发送请求前将代码值转换为中文名称：

```javascript
// 前端修复 - 新增功能
async submitAdd() {
  const response = await this.$axios.post('/api/add_internet_line', {
    lineName: this.formData.line_name,
    provider: this.getProviderName(this.formData.provider), // 转换代码值为中文名称
    lineType: this.getLineTypeName(this.formData.line_type), // 转换代码值为中文名称
    bandwidth: this.formData.bandwidth,
    ipRange: this.formData.ip_range,
    datacenter: this.formData.datacenter,
    loginUsername: 'admin'
  })
}

// 前端修复 - 更新功能
async submitEdit() {
  const response = await this.$axios.post('/api/update_internet_line', {
    id: this.formData.id,
    lineName: this.formData.line_name,
    provider: this.getProviderName(this.formData.provider), // 转换代码值为中文名称
    lineType: this.getLineTypeName(this.formData.line_type), // 转换代码值为中文名称
    bandwidth: this.formData.bandwidth,
    ipRange: this.formData.ip_range,
    datacenter: this.formData.datacenter,
    loginUsername: 'admin'
  })
}
```

**验证结果**: 
- ✅ IPv4地址段更新正常
- ✅ IPv6地址段更新正常  
- ✅ 空IP地址段更新正常
- ✅ 无效格式正确拒绝
- ✅ 前端验证与后端验证一致

### 测试覆盖

创建了专门的测试脚本验证更新功能：
- `test_frontend_update_feature.js` - 专门测试前端更新功能
- `frontend/src/test/components/InternetLineManagement.test.js` - 前端组件测试
- `frontend/src/test/components/IpMappingManagement.test.js` - IP验证逻辑测试

所有测试均通过，确保功能稳定可靠。
## 网关I
P和防火墙IP功能

### 功能概述

互联网线路管理系统现在支持"网关IP"和"防火墙IP"字段，允许用户为每条互联网线路记录相关的网关和防火墙IP地址信息。

### 新增字段

1. **网关IP** (`gateway_ip`)
   - 字段类型：VARCHAR(50)
   - 是否必填：否（可选字段）
   - 支持格式：IPv4和IPv6地址
   - 示例：`***********`、`2001:db8:85a3::1`

2. **防火墙IP** (`firewall_ip`)
   - 字段类型：VARCHAR(50)
   - 是否必填：否（可选字段）
   - 支持格式：IPv4和IPv6地址
   - 示例：`*************`、`2001:db8:85a3::254`

### 数据库更改

**迁移脚本**: `sql/*******/add_gateway_firewall_ip_fields.sql`

```sql
-- 添加网关IP字段
ALTER TABLE public.cmdb_internet_lines 
ADD COLUMN IF NOT EXISTS gateway_ip character varying(50);

-- 添加防火墙IP字段
ALTER TABLE public.cmdb_internet_lines 
ADD COLUMN IF NOT EXISTS firewall_ip character varying(50);

-- 添加字段注释
COMMENT ON COLUMN public.cmdb_internet_lines.gateway_ip IS '网关IP地址，支持IPv4和IPv6格式';
COMMENT ON COLUMN public.cmdb_internet_lines.firewall_ip IS '防火墙IP地址，支持IPv4和IPv6格式';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_internet_lines_gateway_ip ON public.cmdb_internet_lines(gateway_ip);
CREATE INDEX IF NOT EXISTS idx_internet_lines_firewall_ip ON public.cmdb_internet_lines(firewall_ip);
```

### 前端实现

#### 1. 表单字段
- 在新增和编辑对话框中添加了网关IP和防火墙IP输入框
- 输入框提供了清晰的占位符提示
- 支持清空功能

#### 2. 表格显示
- 在数据表格中添加了"网关IP"和"防火墙IP"列
- 空值显示为"-"
- 列宽设置为120px以适应IP地址显示

#### 3. 验证规则
- **网关IP验证**：`validateGatewayIp`函数
- **防火墙IP验证**：`validateFirewallIp`函数
- 支持IPv4和IPv6格式验证
- 非必填字段，空值自动通过验证

### 后端实现

#### 1. 验证服务更新
- 添加了`validateGatewayIp`和`validateFirewallIp`验证函数
- 集成到`validateInternetLineData`综合验证中
- 支持IPv4和IPv6格式的完整验证

#### 2. 控制器更新
- **添加线路**：支持网关IP和防火墙IP字段
- **更新线路**：支持网关IP和防火墙IP字段的修改
- **查询线路**：返回网关IP和防火墙IP信息

#### 3. API接口
所有相关API接口均已更新以支持新字段：
- `POST /api/add_internet_line`
- `POST /api/update_internet_line`
- `POST /api/get_internet_lines`

### 验证规则详情

#### IPv4验证
- 格式：`xxx.xxx.xxx.xxx`
- 每段范围：0-255
- 示例：`***********`、`********`

#### IPv6验证
- 支持标准IPv6格式
- 支持缩写形式（如`::`）
- 示例：`2001:db8:85a3::1`、`fe80::1`

### 错误处理

#### 常见错误信息
- `网关IPv4地址格式不正确，每段应在0-255之间`
- `网关IP地址格式不正确，请输入有效的IPv4或IPv6地址`
- `防火墙IPv4地址格式不正确，每段应在0-255之间`
- `防火墙IP地址格式不正确，请输入有效的IPv4或IPv6地址`

### 使用示例

#### 1. 添加线路时设置网关和防火墙IP
```javascript
{
  lineName: '测试线路',
  provider: '中国电信',
  lineType: '专线',
  bandwidth: '100M',
  ipRange: '***********/24',
  gatewayIp: '***********',      // 网关IP
  firewallIp: '*************',   // 防火墙IP
  datacenter: 'A'
}
```

#### 2. IPv6示例
```javascript
{
  lineName: 'IPv6测试线路',
  provider: '中国联通',
  lineType: '光纤',
  bandwidth: '200M',
  ipRange: '2001:db8:85a3::/64',
  gatewayIp: '2001:db8:85a3::1',      // IPv6网关IP
  firewallIp: '2001:db8:85a3::254',   // IPv6防火墙IP
  datacenter: 'B'
}
```

### 测试覆盖

创建了专门的测试脚本验证功能：
- `test_gateway_firewall_ip_feature.js` - 网关IP和防火墙IP功能测试
- 测试IPv4和IPv6格式
- 测试空值处理
- 测试无效格式拒绝
- 测试更新功能

### 注意事项

1. **可选字段**：网关IP和防火墙IP均为可选字段，可以留空
2. **格式验证**：系统会自动验证IP地址格式的有效性
3. **IPv6支持**：完全支持IPv6地址格式，包括缩写形式
4. **性能优化**：为网关IP和防火墙IP字段添加了数据库索引
5. **向后兼容**：新字段不影响现有数据和功能

### 版本信息

- **版本**: *******
- **发布日期**: 2025-01-31
- **新增功能**: 网关IP和防火墙IP支持
- **兼容性**: 向后兼容，不影响现有功能