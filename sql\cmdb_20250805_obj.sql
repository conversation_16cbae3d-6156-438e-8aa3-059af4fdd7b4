--
-- PostgreSQL database cluster dump
--

-- Started on 2025-08-05 08:48:47

SET default_transaction_read_only = off;

SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

--
-- Roles
--

CREATE ROLE cjmonitor;
ALTER ROLE cjmonitor WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB LOGIN NOREPLICATION NOBYPASSRLS;
CREATE ROLE postgres;
ALTER ROLE postgres WITH SUPERUSER INHERIT CREATEROLE CREATEDB LOGIN REPLICATION BYPASSRLS;
CREATE ROLE replica;
ALTER ROLE replica WITH NOSUPERUSER INHERIT NOCREATEROLE NOCREATEDB LOGIN REPLICATION NOBYPASSRLS;

--
-- User Configurations
--








--
-- Databases
--

--
-- Database "template1" dump
--

\connect template1

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12
-- Dumped by pg_dump version 17.0

-- Started on 2025-08-05 08:48:48

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Completed on 2025-08-05 08:48:48

--
-- PostgreSQL database dump complete
--

--
-- Database "cmdb" dump
--

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12
-- Dumped by pg_dump version 17.0

-- Started on 2025-08-05 08:48:48

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 4722 (class 1262 OID 16426)
-- Name: cmdb; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE cmdb WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.UTF-8';


ALTER DATABASE cmdb OWNER TO postgres;

\connect cmdb

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 984 (class 1247 OID 16761)
-- Name: breakpoint; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.breakpoint AS (
	func oid,
	linenumber integer,
	targetname text
);


ALTER TYPE public.breakpoint OWNER TO postgres;

--
-- TOC entry 987 (class 1247 OID 16764)
-- Name: frame; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.frame AS (
	level integer,
	targetname text,
	func oid,
	linenumber integer,
	args text
);


ALTER TYPE public.frame OWNER TO postgres;

--
-- TOC entry 990 (class 1247 OID 16767)
-- Name: proxyinfo; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.proxyinfo AS (
	serverversionstr text,
	serverversionnum integer,
	proxyapiver integer,
	serverprocessid integer
);


ALTER TYPE public.proxyinfo OWNER TO postgres;

--
-- TOC entry 939 (class 1247 OID 16438)
-- Name: targetinfo; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.targetinfo AS (
	target oid,
	schema oid,
	nargs integer,
	argtypes oidvector,
	targetname name,
	argmodes "char"[],
	argnames text[],
	targetlang oid,
	fqname text,
	returnsset boolean,
	returntype oid,
	isfunc boolean,
	pkg oid,
	argdefvals text[]
);


ALTER TYPE public.targetinfo OWNER TO postgres;

--
-- TOC entry 993 (class 1247 OID 16770)
-- Name: var; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.var AS (
	name text,
	varclass character(1),
	linenumber integer,
	isunique boolean,
	isconst boolean,
	isnotnull boolean,
	dtype oid,
	value text
);


ALTER TYPE public.var OWNER TO postgres;

--
-- TOC entry 309 (class 1255 OID 16792)
-- Name: add_cmdb_monitored_ip_list(character varying, text, character varying, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.add_cmdb_monitored_ip_list(p_ip_address character varying, p_description text, p_status character varying, p_last_checked timestamp without time zone) RETURNS TABLE(code integer, msg character varying)
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- 尝试插入数据
    INSERT INTO cmdb_monitored_ip_list (ip_address, description, status, last_checked)
    VALUES (p_ip_address, p_description, p_status, p_last_checked);

    -- 如果插入成功，返回成功信息
    RETURN QUERY SELECT 0, 'Success'::VARCHAR;

EXCEPTION
    WHEN OTHERS THEN
        -- 捕获任何异常，返回错误代码和错误信息
        RETURN QUERY SELECT 1, SQLERRM::VARCHAR;
END;
$$;


ALTER FUNCTION public.add_cmdb_monitored_ip_list(p_ip_address character varying, p_description text, p_status character varying, p_last_checked timestamp without time zone) OWNER TO postgres;

--
-- TOC entry 321 (class 1255 OID 16803)
-- Name: pldbg_get_target_info(text, character); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.pldbg_get_target_info(signature text, targettype character) RETURNS public.targetinfo
    LANGUAGE sql
    AS $_$
  SELECT p.oid AS target,
         pronamespace AS schema,
         pronargs::int4 AS nargs,
         -- The returned argtypes column is of type oidvector, but unlike
         -- proargtypes, it's supposed to include OUT params. So we
         -- essentially have to return proallargtypes, converted to an
         -- oidvector. There is no oid[] -> oidvector cast, so we have to
         -- do it via text.
         CASE WHEN proallargtypes IS NOT NULL THEN
           translate(proallargtypes::text, ',{}', ' ')::oidvector
         ELSE
           proargtypes
         END AS argtypes,
         proname AS targetname,
         proargmodes AS argmodes,
         proargnames AS proargnames,
         prolang AS targetlang,
         quote_ident(nspname) || '.' || quote_ident(proname) AS fqname,
         proretset AS returnsset,
         prorettype AS returntype,

         't'::bool AS isfunc,
         0::oid AS pkg,
	 NULL::text[] AS argdefvals

  FROM pg_proc p, pg_namespace n
  WHERE p.pronamespace = n.oid
  AND p.oid = $1::oid
  -- We used to support querying by function name or trigger name/oid as well,
  -- but that was never used in the client, so the support for that has been
  -- removed. The targeType argument remains as a legacy of that. You're
  -- expected to pass 'o' as target type, but it doesn't do anything.
  AND $2 = 'o'
$_$;


ALTER FUNCTION public.pldbg_get_target_info(signature text, targettype character) OWNER TO postgres;

--
-- TOC entry 215 (class 1259 OID 16442)
-- Name: application_system_info_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.application_system_info_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.application_system_info_id_seq OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 225 (class 1259 OID 16454)
-- Name: cmdb_application_system_info; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_application_system_info (
    id integer DEFAULT nextval('public.application_system_info_id_seq'::regclass) NOT NULL,
    management_ip character varying(15) NOT NULL,
    hostname character varying(255),
    function_purpose character varying(255),
    server_admin1 character varying(25),
    server_admin2 character varying(25),
    data_center character varying(255),
    machine_usage_status character varying(50),
    remarks character varying(255),
    business_system_name character varying(255),
    system_administrator character varying(255),
    system_classification character varying(25),
    is_monitored character varying(10),
    deployed_applications character varying(255),
    production_attributes character varying(25),
    master_slave_role character varying(50),
    backup_mode character varying(255),
    internet_ip character varying(50),
    internet_port character varying(255),
    operating_system character varying(255),
    has_antivirus_software character varying(10),
    patch_update_configured character varying(10),
    has_system_administrator character varying(40),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    related_master_slave_ips character varying(255),
    monitoring_requirement boolean DEFAULT true NOT NULL,
    monitoring_requirement_description text
);


ALTER TABLE public.cmdb_application_system_info OWNER TO postgres;

--
-- TOC entry 4725 (class 0 OID 0)
-- Dependencies: 225
-- Name: TABLE cmdb_application_system_info; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_application_system_info IS '应用系统信息登记表';


--
-- TOC entry 4726 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.id IS '序号';


--
-- TOC entry 4727 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.management_ip IS '管理IP
（系统管理员填写）';


--
-- TOC entry 4728 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.hostname; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.hostname IS '主机名
（公式自动获取）';


--
-- TOC entry 4729 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.function_purpose; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.function_purpose IS '功能用途
（公式自动获取）';


--
-- TOC entry 4730 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.server_admin1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.server_admin1 IS '服务器管理员1
（公式自动获取）
从“实体服务器设备登记表“和”虚拟机登记表（系统管理员认领）“中获取';


--
-- TOC entry 4731 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.server_admin2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.server_admin2 IS '服务器管理员2
（公式自动获取）';


--
-- TOC entry 4732 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.data_center; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.data_center IS '所属机房
（公式自动获取）';


--
-- TOC entry 4733 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.machine_usage_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.machine_usage_status IS '机器在用情况
（公式自动获取）';


--
-- TOC entry 4734 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.remarks IS '备注
（系统管理员填写）';


--
-- TOC entry 4735 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.business_system_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.business_system_name IS '归属业务系统名称
（系统管理员填写）';


--
-- TOC entry 4736 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.system_administrator; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.system_administrator IS '系统管理员
（公式自动获取）';


--
-- TOC entry 4737 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.system_classification; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.system_classification IS '系统分级
（公式自动获取）';


--
-- TOC entry 4738 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.is_monitored; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.is_monitored IS '是否监控
（公式自动获取）';


--
-- TOC entry 4739 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.deployed_applications; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.deployed_applications IS '部署应用
（系统管理员填写）';


--
-- TOC entry 4740 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.production_attributes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.production_attributes IS '生产属性
（系统管理员填写）';


--
-- TOC entry 4741 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.master_slave_role; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.master_slave_role IS '主从角色
（系统管理员填写）';


--
-- TOC entry 4742 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.backup_mode; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.backup_mode IS '备份模式
（系统管理员填写）';


--
-- TOC entry 4743 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.internet_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.internet_ip IS '互联网IP
（系统管理员填写）';


--
-- TOC entry 4744 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.internet_port; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.internet_port IS '互联网端口
（系统管理员填写）';


--
-- TOC entry 4745 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.operating_system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.operating_system IS '操作系统
（公式自动获取）';


--
-- TOC entry 4746 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.has_antivirus_software; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.has_antivirus_software IS '是否安装杀毒软件
（系统管理员填写）';


--
-- TOC entry 4747 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.patch_update_configured; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.patch_update_configured IS '是否配置补丁更新服务
（系统管理员填写）';


--
-- TOC entry 4748 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.has_system_administrator; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.has_system_administrator IS '系统级别是否有管理员（公式自动获取）';


--
-- TOC entry 4749 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.created_at IS '创建时间';


--
-- TOC entry 4750 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.created_by IS '创建人';


--
-- TOC entry 4751 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.updated_at IS '更新时间';


--
-- TOC entry 4752 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.updated_by IS '更新人';


--
-- TOC entry 4753 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 4754 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.related_master_slave_ips; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.related_master_slave_ips IS '关联主从机IP（当主从角色不为单机时必填，多个IP用英文逗号分隔）';


--
-- TOC entry 4755 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.monitoring_requirement; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.monitoring_requirement IS '监控需求（必填）：TRUE=是，FALSE=否';


--
-- TOC entry 4756 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN cmdb_application_system_info.monitoring_requirement_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_application_system_info.monitoring_requirement_description IS '监控需求说明：当monitoring_requirement为FALSE时为必填项';


--
-- TOC entry 216 (class 1259 OID 16443)
-- Name: cmdb_data_dictionary_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_data_dictionary_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_data_dictionary_id_seq OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 16465)
-- Name: cmdb_data_dictionary; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_data_dictionary (
    id integer DEFAULT nextval('public.cmdb_data_dictionary_id_seq'::regclass) NOT NULL,
    dict_type character varying(100) NOT NULL,
    dict_type_name character varying(255) NOT NULL,
    dict_code character varying(255) NOT NULL,
    dict_name character varying(255) NOT NULL,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_data_dictionary OWNER TO postgres;

--
-- TOC entry 4757 (class 0 OID 0)
-- Dependencies: 226
-- Name: TABLE cmdb_data_dictionary; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_data_dictionary IS '数据字典表';


--
-- TOC entry 4758 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.id IS '主键ID';


--
-- TOC entry 4759 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.dict_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.dict_type IS '字典类型';


--
-- TOC entry 4760 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.dict_type_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.dict_type_name IS '字典类型名称';


--
-- TOC entry 4761 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.dict_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.dict_code IS '字典代码';


--
-- TOC entry 4762 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.dict_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.dict_name IS '字典名称';


--
-- TOC entry 4763 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.created_at IS '创建时间';


--
-- TOC entry 4764 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.created_by IS '创建人';


--
-- TOC entry 4765 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.updated_at IS '更新时间';


--
-- TOC entry 4766 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.updated_by IS '更新人';


--
-- TOC entry 4767 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN cmdb_data_dictionary.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_data_dictionary.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 220 (class 1259 OID 16449)
-- Name: device_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.device_management_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.device_management_id_seq OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 16476)
-- Name: cmdb_device_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_device_management (
    id integer DEFAULT nextval('public.device_management_id_seq'::regclass) NOT NULL,
    management_ip character varying(15) NOT NULL,
    out_of_band_management character varying(15),
    hostname character varying(255) NOT NULL,
    function_purpose character varying(255) NOT NULL,
    admin1 character varying(25) NOT NULL,
    admin2 character varying(25),
    device_type character varying(255) NOT NULL,
    production_attributes character varying(40) NOT NULL,
    data_center character varying(255) NOT NULL,
    operation_status character varying(50) NOT NULL,
    asset_number character varying(255),
    purchase_date date,
    maintenance_years integer,
    maintenance_end_date date,
    serial_number character varying(255),
    model character varying(255),
    version character varying(50),
    is_monitored character varying(10),
    monitoring_ip character varying(15),
    architecture_mode character varying(255),
    is_single_point character varying(10),
    managed_addresses character varying(255),
    remarks character varying(255),
    year_category character varying(255),
    in_monitoring_list character varying(10),
    pre_monitoring_verified character varying(10),
    inspection character varying(10),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    is_innovative_tech character varying(10) DEFAULT '否'::character varying NOT NULL,
    cpu_model character varying(255),
    monitoring_requirement boolean DEFAULT true NOT NULL,
    monitoring_requirement_description text
);


ALTER TABLE public.cmdb_device_management OWNER TO postgres;

--
-- TOC entry 4768 (class 0 OID 0)
-- Dependencies: 227
-- Name: TABLE cmdb_device_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_device_management IS '网络设备登记表';


--
-- TOC entry 4769 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.id IS '序号';


--
-- TOC entry 4770 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.management_ip IS '管理IP';


--
-- TOC entry 4771 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.out_of_band_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.out_of_band_management IS '带外管理';


--
-- TOC entry 4772 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.hostname; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.hostname IS '主机名';


--
-- TOC entry 4773 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.function_purpose; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.function_purpose IS '功能用途';


--
-- TOC entry 4774 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.admin1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.admin1 IS '管理员1';


--
-- TOC entry 4775 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.admin2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.admin2 IS '管理员2';


--
-- TOC entry 4776 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.device_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.device_type IS '设备类型';


--
-- TOC entry 4777 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.production_attributes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.production_attributes IS '生产属性';


--
-- TOC entry 4778 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.data_center; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.data_center IS '所属机房';


--
-- TOC entry 4779 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.operation_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.operation_status IS '运行状态';


--
-- TOC entry 4780 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.asset_number; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.asset_number IS '财务资产编号';


--
-- TOC entry 4781 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.purchase_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.purchase_date IS '采购时间';


--
-- TOC entry 4782 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.maintenance_years; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.maintenance_years IS '维保年限';


--
-- TOC entry 4783 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.maintenance_end_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.maintenance_end_date IS '维保截止日期
（公式自动计算）';


--
-- TOC entry 4784 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.serial_number; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.serial_number IS '序列号';


--
-- TOC entry 4785 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.model IS '型号';


--
-- TOC entry 4786 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.version IS '版本';


--
-- TOC entry 4787 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.is_monitored; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.is_monitored IS '是否监控
（自动获取）';


--
-- TOC entry 4788 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.monitoring_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.monitoring_ip IS '监控IP';


--
-- TOC entry 4789 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.architecture_mode; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.architecture_mode IS '架构模式';


--
-- TOC entry 4790 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.is_single_point; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.is_single_point IS '是否单点';


--
-- TOC entry 4791 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.managed_addresses; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.managed_addresses IS '通过该设备管理的地址';


--
-- TOC entry 4792 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.remarks IS '备注';


--
-- TOC entry 4793 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.year_category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.year_category IS '年份分类';


--
-- TOC entry 4794 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.in_monitoring_list; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.in_monitoring_list IS '是否在监控清单中';


--
-- TOC entry 4795 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.pre_monitoring_verified; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.pre_monitoring_verified IS '监控前期是否验证';


--
-- TOC entry 4796 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.inspection; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.inspection IS '抽查';


--
-- TOC entry 4797 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.created_at IS '创建时间';


--
-- TOC entry 4798 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.created_by IS '创建人';


--
-- TOC entry 4799 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.updated_at IS '更新时间';


--
-- TOC entry 4800 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.updated_by IS '更新人';


--
-- TOC entry 4801 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 4802 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.is_innovative_tech; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.is_innovative_tech IS '是否信创';


--
-- TOC entry 4803 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.cpu_model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.cpu_model IS 'CPU型号';


--
-- TOC entry 4804 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.monitoring_requirement; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.monitoring_requirement IS '监控需求：TRUE表示需要监控，FALSE表示不需要监控';


--
-- TOC entry 4805 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN cmdb_device_management.monitoring_requirement_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_device_management.monitoring_requirement_description IS '监控需求说明：当监控需求为否时必填';


--
-- TOC entry 300 (class 1259 OID 18334)
-- Name: cmdb_discovery_cleanup; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_discovery_cleanup (
    id integer NOT NULL,
    config_key character varying(100) NOT NULL,
    config_value text NOT NULL,
    config_name character varying(200) NOT NULL,
    description text,
    config_type character varying(50) DEFAULT 'string'::character varying,
    default_value text,
    is_enabled boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) DEFAULT 'system'::character varying NOT NULL,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar NOT NULL
);


ALTER TABLE public.cmdb_discovery_cleanup OWNER TO postgres;

--
-- TOC entry 4806 (class 0 OID 0)
-- Dependencies: 300
-- Name: TABLE cmdb_discovery_cleanup; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_discovery_cleanup IS '发现结果清理配置表';


--
-- TOC entry 4807 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.id IS '主键ID';


--
-- TOC entry 4808 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.config_key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.config_key IS '配置键名';


--
-- TOC entry 4809 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.config_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.config_value IS '配置值';


--
-- TOC entry 4810 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.config_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.config_name IS '配置名称';


--
-- TOC entry 4811 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.description IS '配置描述';


--
-- TOC entry 4812 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.config_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.config_type IS '配置类型(string/number/boolean/json)';


--
-- TOC entry 4813 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.default_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.default_value IS '默认值';


--
-- TOC entry 4814 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.is_enabled; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.is_enabled IS '是否启用';


--
-- TOC entry 4815 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.sort_order; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.sort_order IS '排序顺序';


--
-- TOC entry 4816 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.created_at IS '创建时间';


--
-- TOC entry 4817 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.created_by IS '创建人';


--
-- TOC entry 4818 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.updated_at IS '更新时间';


--
-- TOC entry 4819 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.updated_by IS '更新人';


--
-- TOC entry 4820 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN cmdb_discovery_cleanup.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_cleanup.del_flag IS '删除标志(0:未删除,1:已删除)';


--
-- TOC entry 299 (class 1259 OID 18333)
-- Name: cmdb_discovery_cleanup_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_discovery_cleanup_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_discovery_cleanup_id_seq OWNER TO postgres;

--
-- TOC entry 4822 (class 0 OID 0)
-- Dependencies: 299
-- Name: cmdb_discovery_cleanup_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_discovery_cleanup_id_seq OWNED BY public.cmdb_discovery_cleanup.id;


--
-- TOC entry 251 (class 1259 OID 17203)
-- Name: cmdb_discovery_results_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_discovery_results_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_discovery_results_id_seq OWNER TO postgres;

--
-- TOC entry 253 (class 1259 OID 17218)
-- Name: cmdb_discovery_results; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_discovery_results (
    id integer DEFAULT nextval('public.cmdb_discovery_results_id_seq'::regclass) NOT NULL,
    task_id integer NOT NULL,
    ip_address character varying(20) NOT NULL,
    hostname character varying(100),
    mac_address character varying(20),
    device_type character varying(50),
    open_ports character varying(255),
    os_info character varying(255),
    status character varying(20) DEFAULT 'active'::character varying,
    discovery_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) NOT NULL,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar NOT NULL
);


ALTER TABLE public.cmdb_discovery_results OWNER TO postgres;

--
-- TOC entry 4823 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.task_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.task_id IS '关联的任务ID';


--
-- TOC entry 4824 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.ip_address; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.ip_address IS 'IP地址';


--
-- TOC entry 4825 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.hostname; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.hostname IS '主机名';


--
-- TOC entry 4826 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.mac_address; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.mac_address IS 'MAC地址';


--
-- TOC entry 4827 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.device_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.device_type IS '设备类型';


--
-- TOC entry 4828 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.open_ports; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.open_ports IS '开放端口，逗号分隔';


--
-- TOC entry 4829 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.os_info; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.os_info IS '操作系统信息';


--
-- TOC entry 4830 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.status IS '状态：active, inactive';


--
-- TOC entry 4831 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.discovery_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.discovery_time IS '发现时间';


--
-- TOC entry 4832 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.created_at IS '创建时间';


--
-- TOC entry 4833 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.created_by IS '创建人';


--
-- TOC entry 4834 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.updated_at IS '更新时间';


--
-- TOC entry 4835 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.updated_by IS '更新人';


--
-- TOC entry 4836 (class 0 OID 0)
-- Dependencies: 253
-- Name: COLUMN cmdb_discovery_results.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_results.del_flag IS '删除标记：0-正常，1-删除';


--
-- TOC entry 250 (class 1259 OID 17202)
-- Name: cmdb_discovery_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_discovery_tasks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_discovery_tasks_id_seq OWNER TO postgres;

--
-- TOC entry 252 (class 1259 OID 17204)
-- Name: cmdb_discovery_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_discovery_tasks (
    id integer DEFAULT nextval('public.cmdb_discovery_tasks_id_seq'::regclass) NOT NULL,
    task_name character varying(100) NOT NULL,
    task_type character varying(50) NOT NULL,
    description text,
    ip_range_start character varying(20) NOT NULL,
    ip_range_end character varying(20) NOT NULL,
    ip_cidr character varying(50),
    ip_range_type character varying(20) DEFAULT 'range'::character varying,
    scan_ports character varying(255),
    schedule_type character varying(20) DEFAULT 'manual'::character varying,
    schedule_value character varying(100),
    last_run_time timestamp(6) without time zone,
    status character varying(20) DEFAULT 'inactive'::character varying,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) NOT NULL,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar NOT NULL,
    run_duration_seconds integer DEFAULT 0
);


ALTER TABLE public.cmdb_discovery_tasks OWNER TO postgres;

--
-- TOC entry 4837 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.task_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.task_name IS '任务名称';


--
-- TOC entry 4838 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.task_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.task_type IS '任务类型：network_scan, port_scan';


--
-- TOC entry 4839 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.description IS '任务描述';


--
-- TOC entry 4840 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.ip_range_start; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.ip_range_start IS 'IP范围起始';


--
-- TOC entry 4841 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.ip_range_end; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.ip_range_end IS 'IP范围结束';


--
-- TOC entry 4842 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.ip_cidr; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.ip_cidr IS 'IP网段，支持CIDR格式，如***********/24';


--
-- TOC entry 4843 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.ip_range_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.ip_range_type IS 'IP范围类型：range-起止IP, cidr-网段';


--
-- TOC entry 4844 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.scan_ports; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.scan_ports IS '扫描端口，逗号分隔';


--
-- TOC entry 4845 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.schedule_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.schedule_type IS '调度类型：manual, once, daily, weekly';


--
-- TOC entry 4846 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.schedule_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.schedule_value IS '调度值，根据类型不同而不同';


--
-- TOC entry 4847 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.last_run_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.last_run_time IS '上次运行时间';


--
-- TOC entry 4848 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.status IS '状态：inactive, running, completed, failed';


--
-- TOC entry 4849 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.created_at IS '创建时间';


--
-- TOC entry 4850 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.created_by IS '创建人';


--
-- TOC entry 4851 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.updated_at IS '更新时间';


--
-- TOC entry 4852 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.updated_by IS '更新人';


--
-- TOC entry 4853 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.del_flag IS '删除标记：0-正常，1-删除';


--
-- TOC entry 4854 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN cmdb_discovery_tasks.run_duration_seconds; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_discovery_tasks.run_duration_seconds IS '任务运行时间（秒）';


--
-- TOC entry 270 (class 1259 OID 17554)
-- Name: cmdb_function_domain_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_function_domain_types (
    id integer NOT NULL,
    primary_category_code character varying(50) NOT NULL,
    primary_category_name character varying(100) NOT NULL,
    function_domain_code character varying(50) NOT NULL,
    function_domain_name character varying(100) NOT NULL,
    is_core_domain boolean DEFAULT false,
    function_domain_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50) DEFAULT 'admin'::character varying,
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.cmdb_function_domain_types OWNER TO postgres;

--
-- TOC entry 4855 (class 0 OID 0)
-- Dependencies: 270
-- Name: TABLE cmdb_function_domain_types; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_function_domain_types IS '功能域类型汇总表';


--
-- TOC entry 4856 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.id IS '主键ID';


--
-- TOC entry 4857 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.primary_category_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.primary_category_code IS '一级分类编号，例如：GFD表示通用功能域，FFD表示期货经营机构功能域';


--
-- TOC entry 4858 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.primary_category_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.primary_category_name IS '一级类别名称，例如：通用功能域，期货经营机构功能域';


--
-- TOC entry 4859 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.function_domain_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.function_domain_code IS '功能域编号，例如：001，002等';


--
-- TOC entry 4860 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.function_domain_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.function_domain_name IS '功能域名称，例如：用户管理，权限管理等';


--
-- TOC entry 4861 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.is_core_domain; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.is_core_domain IS '是否为核心功能域:是，否';


--
-- TOC entry 4862 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.function_domain_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.function_domain_type IS '功能域类型:通用功能域，期货经营机构';


--
-- TOC entry 4863 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.created_at IS '创建时间';


--
-- TOC entry 4864 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.created_by IS '创建人';


--
-- TOC entry 4865 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.updated_at IS '更新时间';


--
-- TOC entry 4866 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.updated_by IS '更新人';


--
-- TOC entry 4867 (class 0 OID 0)
-- Dependencies: 270
-- Name: COLUMN cmdb_function_domain_types.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_function_domain_types.del_flag IS '删除标志，0表示未删除，1表示已删除';


--
-- TOC entry 269 (class 1259 OID 17553)
-- Name: cmdb_function_domain_types_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_function_domain_types_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_function_domain_types_id_seq OWNER TO postgres;

--
-- TOC entry 4868 (class 0 OID 0)
-- Dependencies: 269
-- Name: cmdb_function_domain_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_function_domain_types_id_seq OWNED BY public.cmdb_function_domain_types.id;


--
-- TOC entry 301 (class 1259 OID 18391)
-- Name: cmdb_host_ip_update_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_host_ip_update_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_host_ip_update_logs_id_seq OWNER TO postgres;

--
-- TOC entry 302 (class 1259 OID 18392)
-- Name: cmdb_host_ip_update_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_host_ip_update_logs (
    id integer DEFAULT nextval('public.cmdb_host_ip_update_logs_id_seq'::regclass) NOT NULL,
    vm_id integer NOT NULL,
    management_ip character varying(15) NOT NULL,
    old_host_ip character varying(15),
    new_host_ip character varying(15),
    update_source character varying(50) NOT NULL,
    update_method character varying(50) NOT NULL,
    success boolean DEFAULT true NOT NULL,
    error_message text,
    response_data jsonb,
    execution_time_ms integer,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) DEFAULT 'system'::character varying
);


ALTER TABLE public.cmdb_host_ip_update_logs OWNER TO postgres;

--
-- TOC entry 4869 (class 0 OID 0)
-- Dependencies: 302
-- Name: TABLE cmdb_host_ip_update_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_host_ip_update_logs IS '虚拟机宿主机IP更新日志表';


--
-- TOC entry 4870 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.id IS '日志ID';


--
-- TOC entry 4871 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.vm_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.vm_id IS '虚拟机ID（关联cmdb_vm_registry.id）';


--
-- TOC entry 4872 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.management_ip IS '虚拟机管理IP';


--
-- TOC entry 4873 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.old_host_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.old_host_ip IS '更新前的宿主机IP';


--
-- TOC entry 4874 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.new_host_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.new_host_ip IS '更新后的宿主机IP';


--
-- TOC entry 4875 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.update_source; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.update_source IS '更新来源：zabbix_api=Zabbix API，manual=手动更新，batch_update=批量更新';


--
-- TOC entry 4876 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.update_method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.update_method IS '更新方式：realtime=实时查询，scheduled=定时任务，manual=手动触发';


--
-- TOC entry 4877 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.success; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.success IS '更新是否成功';


--
-- TOC entry 4878 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.error_message; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.error_message IS '错误信息（更新失败时记录）';


--
-- TOC entry 4879 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.response_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.response_data IS 'API响应数据（JSON格式）';


--
-- TOC entry 4880 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.execution_time_ms; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.execution_time_ms IS 'API调用执行时间（毫秒）';


--
-- TOC entry 4881 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.created_at IS '日志创建时间';


--
-- TOC entry 4882 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN cmdb_host_ip_update_logs.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_ip_update_logs.created_by IS '操作用户';


--
-- TOC entry 228 (class 1259 OID 16487)
-- Name: cmdb_host_scan_results; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_host_scan_results (
    management_ip character varying(20) NOT NULL,
    admin1 character varying(100),
    admin2 character varying(100),
    designated_admin character varying(100),
    datacenter character varying(255),
    management_status character varying(50),
    is_virtual_machine character varying(50),
    vm_status character varying(50),
    cmdb_registration_status character varying(50),
    remarks text,
    scan_date character varying(10) DEFAULT CURRENT_TIMESTAMP,
    id integer NOT NULL,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_host_scan_results OWNER TO postgres;

--
-- TOC entry 4884 (class 0 OID 0)
-- Dependencies: 228
-- Name: TABLE cmdb_host_scan_results; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_host_scan_results IS 'IP资产管理';


--
-- TOC entry 4885 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.management_ip IS '管理网IP';


--
-- TOC entry 4886 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.admin1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.admin1 IS '管理员1';


--
-- TOC entry 4887 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.admin2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.admin2 IS '管理员2';


--
-- TOC entry 4888 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.designated_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.designated_admin IS '指定管理员（手动）';


--
-- TOC entry 4889 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.datacenter; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.datacenter IS '所属机房';


--
-- TOC entry 4890 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.management_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.management_status IS '管理状态';


--
-- TOC entry 4891 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.is_virtual_machine; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.is_virtual_machine IS '是否虚拟机';


--
-- TOC entry 4892 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.vm_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.vm_status IS '虚拟机状态';


--
-- TOC entry 4893 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.cmdb_registration_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.cmdb_registration_status IS 'CMDB登记状态（手动）';


--
-- TOC entry 4894 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.remarks IS '备注';


--
-- TOC entry 4895 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.scan_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.scan_date IS '扫描日期';


--
-- TOC entry 4896 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.created_at IS '创建时间';


--
-- TOC entry 4897 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.created_by IS '创建人';


--
-- TOC entry 4898 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.updated_at IS '更新时间';


--
-- TOC entry 4899 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.updated_by IS '更新人';


--
-- TOC entry 4900 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN cmdb_host_scan_results.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_host_scan_results.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 217 (class 1259 OID 16444)
-- Name: cmdb_host_scan_results_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_host_scan_results_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_host_scan_results_id_seq OWNER TO postgres;

--
-- TOC entry 4901 (class 0 OID 0)
-- Dependencies: 217
-- Name: cmdb_host_scan_results_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_host_scan_results_id_seq OWNED BY public.cmdb_host_scan_results.id;


--
-- TOC entry 241 (class 1259 OID 16773)
-- Name: cmdb_issue_collection; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_issue_collection (
    id integer NOT NULL,
    description text NOT NULL,
    raised_time timestamp(6) without time zone NOT NULL,
    raised_by character varying(100) NOT NULL,
    is_solved character varying(10) DEFAULT '未解决'::character varying NOT NULL,
    solution text,
    solved_by character varying(100),
    solved_time timestamp(6) without time zone,
    notification character varying(255),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(100),
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_by character varying(100),
    del_flag smallint DEFAULT 0 NOT NULL,
    solution_type character varying(50),
    planned_dev_time date,
    dev_cycle_days integer,
    CONSTRAINT cmdb_issue_collection_del_flag_check CHECK ((del_flag = ANY (ARRAY[0, 1]))),
    CONSTRAINT cmdb_issue_collection_is_solved_check CHECK (((is_solved)::text = ANY (ARRAY[('未解决'::character varying)::text, ('已解决'::character varying)::text])))
);


ALTER TABLE public.cmdb_issue_collection OWNER TO postgres;

--
-- TOC entry 4902 (class 0 OID 0)
-- Dependencies: 241
-- Name: TABLE cmdb_issue_collection; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_issue_collection IS '填表问题需求收集';


--
-- TOC entry 4903 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.id IS '序号';


--
-- TOC entry 4904 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.description IS '问题描述（必填）';


--
-- TOC entry 4905 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.raised_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.raised_time IS '提出时间（必填）';


--
-- TOC entry 4906 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.raised_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.raised_by IS '提出人员（必填）';


--
-- TOC entry 4907 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.is_solved; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.is_solved IS '是否解决';


--
-- TOC entry 4908 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.solution; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.solution IS '解决方案';


--
-- TOC entry 4909 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.solved_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.solved_by IS '解决人';


--
-- TOC entry 4910 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.solved_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.solved_time IS '解决时间';


--
-- TOC entry 4911 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.notification; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.notification IS '通知';


--
-- TOC entry 4912 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.created_at IS '创建时间';


--
-- TOC entry 4913 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.created_by IS '创建人';


--
-- TOC entry 4914 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.updated_at IS '更新时间';


--
-- TOC entry 4915 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.updated_by IS '更新人';


--
-- TOC entry 4916 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.del_flag IS '是否删除(0:正常;1:已删除)';


--
-- TOC entry 4917 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.solution_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.solution_type IS '方案类型：新需求开发、代码优化、BUG修复、数据处理、不做任何处理';


--
-- TOC entry 4918 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.planned_dev_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.planned_dev_time IS '计划开发时间';


--
-- TOC entry 4919 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN cmdb_issue_collection.dev_cycle_days; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_issue_collection.dev_cycle_days IS '开发周期（人天）';


--
-- TOC entry 239 (class 1259 OID 16771)
-- Name: cmdb_issue_collection_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_issue_collection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_issue_collection_id_seq OWNER TO postgres;

--
-- TOC entry 4920 (class 0 OID 0)
-- Dependencies: 239
-- Name: cmdb_issue_collection_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_issue_collection_id_seq OWNED BY public.cmdb_issue_collection.id;


--
-- TOC entry 218 (class 1259 OID 16446)
-- Name: cmdb_monitored_ip_list_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_monitored_ip_list_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_monitored_ip_list_id_seq OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 16504)
-- Name: cmdb_monitored_ip_list; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_monitored_ip_list (
    id integer DEFAULT nextval('public.cmdb_monitored_ip_list_id_seq'::regclass) NOT NULL,
    ip_address character varying(30) NOT NULL,
    description text,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    last_checked timestamp(6) without time zone,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_monitored_ip_list OWNER TO postgres;

--
-- TOC entry 4921 (class 0 OID 0)
-- Dependencies: 229
-- Name: TABLE cmdb_monitored_ip_list; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_monitored_ip_list IS '监控IP列表';


--
-- TOC entry 4922 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.id IS '主键';


--
-- TOC entry 4923 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.ip_address; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.ip_address IS '存储IP地址';


--
-- TOC entry 4924 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.description IS '描述';


--
-- TOC entry 4925 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.status IS '状态，默认为active';


--
-- TOC entry 4926 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.last_checked; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.last_checked IS '最后一次检查的时间戳';


--
-- TOC entry 4927 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.created_at IS '创建时间';


--
-- TOC entry 4928 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.created_by IS '创建人';


--
-- TOC entry 4929 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.updated_at IS '更新时间';


--
-- TOC entry 4930 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.updated_by IS '更新人';


--
-- TOC entry 4931 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN cmdb_monitored_ip_list.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_monitored_ip_list.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 261 (class 1259 OID 17308)
-- Name: cmdb_pages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_pages (
    id integer NOT NULL,
    page_code character varying(50) NOT NULL,
    page_name character varying(100) NOT NULL,
    page_path character varying(100) NOT NULL,
    page_description character varying(255),
    parent_id integer,
    sort_order integer DEFAULT 0,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_pages OWNER TO postgres;

--
-- TOC entry 4932 (class 0 OID 0)
-- Dependencies: 261
-- Name: TABLE cmdb_pages; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_pages IS '系统页面表';


--
-- TOC entry 4933 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.page_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.page_code IS '页面代码';


--
-- TOC entry 4934 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.page_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.page_name IS '页面名称';


--
-- TOC entry 4935 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.page_path; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.page_path IS '页面路径';


--
-- TOC entry 4936 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.page_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.page_description IS '页面描述';


--
-- TOC entry 4937 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.parent_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.parent_id IS '父页面ID';


--
-- TOC entry 4938 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.sort_order; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.sort_order IS '排序顺序';


--
-- TOC entry 4939 (class 0 OID 0)
-- Dependencies: 261
-- Name: COLUMN cmdb_pages.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_pages.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 260 (class 1259 OID 17307)
-- Name: cmdb_pages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_pages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_pages_id_seq OWNER TO postgres;

--
-- TOC entry 4940 (class 0 OID 0)
-- Dependencies: 260
-- Name: cmdb_pages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_pages_id_seq OWNED BY public.cmdb_pages.id;


--
-- TOC entry 259 (class 1259 OID 17277)
-- Name: cmdb_schedule_task_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_schedule_task_history (
    id integer NOT NULL,
    schedule_task_id integer NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone,
    status character varying(20) NOT NULL,
    result text,
    total_tasks integer DEFAULT 0,
    completed_tasks integer DEFAULT 0,
    failed_tasks integer DEFAULT 0,
    created_by character varying(50) NOT NULL
);


ALTER TABLE public.cmdb_schedule_task_history OWNER TO postgres;

--
-- TOC entry 4941 (class 0 OID 0)
-- Dependencies: 259
-- Name: TABLE cmdb_schedule_task_history; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_schedule_task_history IS '调度任务执行历史表';


--
-- TOC entry 4942 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.id IS '主键ID';


--
-- TOC entry 4943 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.schedule_task_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.schedule_task_id IS '调度任务ID';


--
-- TOC entry 4944 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.start_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.start_time IS '开始时间';


--
-- TOC entry 4945 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.end_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.end_time IS '结束时间';


--
-- TOC entry 4946 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.status IS '状态：started, completed, failed';


--
-- TOC entry 4947 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.result; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.result IS '执行结果';


--
-- TOC entry 4948 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.total_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.total_tasks IS '总任务数';


--
-- TOC entry 4949 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.completed_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.completed_tasks IS '完成任务数';


--
-- TOC entry 4950 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.failed_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.failed_tasks IS '失败任务数';


--
-- TOC entry 4951 (class 0 OID 0)
-- Dependencies: 259
-- Name: COLUMN cmdb_schedule_task_history.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_history.created_by IS '执行人';


--
-- TOC entry 258 (class 1259 OID 17276)
-- Name: cmdb_schedule_task_history_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_schedule_task_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_schedule_task_history_id_seq OWNER TO postgres;

--
-- TOC entry 4952 (class 0 OID 0)
-- Dependencies: 258
-- Name: cmdb_schedule_task_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_schedule_task_history_id_seq OWNED BY public.cmdb_schedule_task_history.id;


--
-- TOC entry 257 (class 1259 OID 17254)
-- Name: cmdb_schedule_task_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_schedule_task_items (
    id integer NOT NULL,
    schedule_task_id integer NOT NULL,
    discovery_task_id integer NOT NULL,
    execution_order integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar NOT NULL
);


ALTER TABLE public.cmdb_schedule_task_items OWNER TO postgres;

--
-- TOC entry 4953 (class 0 OID 0)
-- Dependencies: 257
-- Name: TABLE cmdb_schedule_task_items; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_schedule_task_items IS '调度任务项表';


--
-- TOC entry 4954 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.id IS '主键ID';


--
-- TOC entry 4955 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.schedule_task_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.schedule_task_id IS '调度任务ID';


--
-- TOC entry 4956 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.discovery_task_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.discovery_task_id IS '发现任务ID';


--
-- TOC entry 4957 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.execution_order; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.execution_order IS '执行顺序';


--
-- TOC entry 4958 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.created_at IS '创建时间';


--
-- TOC entry 4959 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.created_by IS '创建人';


--
-- TOC entry 4960 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.updated_at IS '更新时间';


--
-- TOC entry 4961 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.updated_by IS '更新人';


--
-- TOC entry 4962 (class 0 OID 0)
-- Dependencies: 257
-- Name: COLUMN cmdb_schedule_task_items.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_task_items.del_flag IS '删除标记：0-正常，1-删除';


--
-- TOC entry 256 (class 1259 OID 17253)
-- Name: cmdb_schedule_task_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_schedule_task_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_schedule_task_items_id_seq OWNER TO postgres;

--
-- TOC entry 4963 (class 0 OID 0)
-- Dependencies: 256
-- Name: cmdb_schedule_task_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_schedule_task_items_id_seq OWNED BY public.cmdb_schedule_task_items.id;


--
-- TOC entry 255 (class 1259 OID 17240)
-- Name: cmdb_schedule_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_schedule_tasks (
    id integer NOT NULL,
    task_name character varying(100) NOT NULL,
    description text,
    schedule_type character varying(20) DEFAULT 'manual'::character varying NOT NULL,
    schedule_value character varying(100),
    last_run_time timestamp without time zone,
    next_run_time timestamp without time zone,
    status character varying(20) DEFAULT 'inactive'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar NOT NULL
);


ALTER TABLE public.cmdb_schedule_tasks OWNER TO postgres;

--
-- TOC entry 4964 (class 0 OID 0)
-- Dependencies: 255
-- Name: TABLE cmdb_schedule_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_schedule_tasks IS '调度任务表';


--
-- TOC entry 4965 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.id IS '主键ID';


--
-- TOC entry 4966 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.task_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.task_name IS '调度任务名称';


--
-- TOC entry 4967 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.description IS '调度任务描述';


--
-- TOC entry 4968 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.schedule_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.schedule_type IS '调度类型：manual, once, daily, weekly';


--
-- TOC entry 4969 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.schedule_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.schedule_value IS '调度值，根据类型不同而不同';


--
-- TOC entry 4970 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.last_run_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.last_run_time IS '上次运行时间';


--
-- TOC entry 4971 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.next_run_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.next_run_time IS '下次计划运行时间';


--
-- TOC entry 4972 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.status IS '状态：inactive, active, paused';


--
-- TOC entry 4973 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.created_at IS '创建时间';


--
-- TOC entry 4974 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.created_by IS '创建人';


--
-- TOC entry 4975 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.updated_at IS '更新时间';


--
-- TOC entry 4976 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.updated_by IS '更新人';


--
-- TOC entry 4977 (class 0 OID 0)
-- Dependencies: 255
-- Name: COLUMN cmdb_schedule_tasks.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_schedule_tasks.del_flag IS '删除标记：0-正常，1-删除';


--
-- TOC entry 254 (class 1259 OID 17239)
-- Name: cmdb_schedule_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_schedule_tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_schedule_tasks_id_seq OWNER TO postgres;

--
-- TOC entry 4978 (class 0 OID 0)
-- Dependencies: 254
-- Name: cmdb_schedule_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_schedule_tasks_id_seq OWNED BY public.cmdb_schedule_tasks.id;


--
-- TOC entry 221 (class 1259 OID 16450)
-- Name: server_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.server_management_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.server_management_id_seq OWNER TO postgres;

--
-- TOC entry 230 (class 1259 OID 16516)
-- Name: cmdb_server_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_server_management (
    id integer DEFAULT nextval('public.server_management_id_seq'::regclass) NOT NULL,
    management_ip character varying(15) NOT NULL,
    hostname character varying(255) NOT NULL,
    function_purpose character varying(255) NOT NULL,
    admin1 character varying(255) NOT NULL,
    admin2 character varying(255),
    server_type character varying(255) NOT NULL,
    production_attributes character varying(255) NOT NULL,
    data_center character varying(255),
    out_of_band_management_ilo character varying(255),
    operation_status character varying(50) NOT NULL,
    asset_number character varying(255),
    purchase_date date,
    maintenance_years integer,
    maintenance_end_date date,
    serial_number character varying(255),
    server_model character varying(255),
    is_monitored character varying(10),
    cpu_model character varying(255),
    memory character varying(255),
    disk character varying(255),
    network_card character varying(255),
    operating_system character varying(255),
    os_category character varying(255),
    remarks character varying(255),
    year_category character varying(255),
    weak_password_exists character varying(10),
    weak_password_correction_date date,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    is_single_point character varying(10),
    managed_addresses character varying(255),
    is_innovative_tech character varying(10) DEFAULT '否'::character varying NOT NULL,
    monitoring_requirement boolean DEFAULT true NOT NULL,
    monitoring_requirement_description text
);


ALTER TABLE public.cmdb_server_management OWNER TO postgres;

--
-- TOC entry 4979 (class 0 OID 0)
-- Dependencies: 230
-- Name: TABLE cmdb_server_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_server_management IS '实体服务器设备登记表';


--
-- TOC entry 4980 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.id IS '序号';


--
-- TOC entry 4981 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.management_ip IS '管理IP
（系统管理员填写）';


--
-- TOC entry 4982 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.hostname; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.hostname IS '主机名
（系统管理员填写）';


--
-- TOC entry 4983 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.function_purpose; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.function_purpose IS '功能用途
（系统管理员填写）';


--
-- TOC entry 4984 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.admin1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.admin1 IS '管理员1
（系统管理员填写）';


--
-- TOC entry 4985 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.admin2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.admin2 IS '管理员2
（系统管理员填写）';


--
-- TOC entry 4986 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.server_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.server_type IS '服务器类型
（系统管理员填写）';


--
-- TOC entry 4987 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.production_attributes; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.production_attributes IS '生产属性
（系统管理员填写）';


--
-- TOC entry 4988 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.data_center; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.data_center IS '所属机房
（系统管理员填写）';


--
-- TOC entry 4989 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.out_of_band_management_ilo; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.out_of_band_management_ilo IS '带外管理iLO
（系统管理员填写）';


--
-- TOC entry 4990 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.operation_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.operation_status IS '运行状态
（系统管理员填写）';


--
-- TOC entry 4991 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.asset_number; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.asset_number IS '财务资产编号
（系统管理员填写）';


--
-- TOC entry 4992 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.purchase_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.purchase_date IS '采购时间
（系统管理员填写）';


--
-- TOC entry 4993 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.maintenance_years; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.maintenance_years IS '维保年限
（系统管理员填写）';


--
-- TOC entry 4994 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.maintenance_end_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.maintenance_end_date IS '维保截止日期
（公式自动计算）';


--
-- TOC entry 4995 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.serial_number; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.serial_number IS '序列号
（系统管理员填写）';


--
-- TOC entry 4996 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.server_model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.server_model IS '服务器型号
（系统管理员填写）';


--
-- TOC entry 4997 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.is_monitored; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.is_monitored IS '是否监控
（公式自动计算）';


--
-- TOC entry 4998 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.cpu_model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.cpu_model IS 'CPU型号
（系统管理员填写）';


--
-- TOC entry 4999 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.memory; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.memory IS '内存
（系统管理员填写）';


--
-- TOC entry 5000 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.disk; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.disk IS '硬盘
（系统管理员填写）';


--
-- TOC entry 5001 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.network_card; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.network_card IS '网卡
（系统管理员填写）';


--
-- TOC entry 5002 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.operating_system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.operating_system IS '操作系统
（系统管理员填写）';


--
-- TOC entry 5003 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.os_category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.os_category IS '操作系统分类
（公式自动获取）';


--
-- TOC entry 5004 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.remarks IS '备注
（系统管理员填写）';


--
-- TOC entry 5005 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.year_category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.year_category IS '年份分类
（公式自动获取）';


--
-- TOC entry 5006 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.weak_password_exists; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.weak_password_exists IS '是否存在弱密码';


--
-- TOC entry 5007 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.weak_password_correction_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.weak_password_correction_date IS '弱密码修正时间';


--
-- TOC entry 5008 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.created_at IS '创建时间';


--
-- TOC entry 5009 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.created_by IS '创建人';


--
-- TOC entry 5010 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.updated_at IS '更新时间';


--
-- TOC entry 5011 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.updated_by IS '更新人';


--
-- TOC entry 5012 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 5013 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.is_single_point; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.is_single_point IS '是否单点';


--
-- TOC entry 5014 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.managed_addresses; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.managed_addresses IS '互备主机IP';


--
-- TOC entry 5015 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.is_innovative_tech; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.is_innovative_tech IS '是否信创';


--
-- TOC entry 5016 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.monitoring_requirement; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.monitoring_requirement IS '监控需求：TRUE表示"是"，FALSE表示"否"，必填项';


--
-- TOC entry 5017 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN cmdb_server_management.monitoring_requirement_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_server_management.monitoring_requirement_description IS '监控需求说明：当监控需求为FALSE时必填';


--
-- TOC entry 223 (class 1259 OID 16452)
-- Name: system_admin_responsibility_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.system_admin_responsibility_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_admin_responsibility_id_seq OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 16527)
-- Name: cmdb_system_admin_responsibility; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_system_admin_responsibility (
    id integer DEFAULT nextval('public.system_admin_responsibility_id_seq'::regclass) NOT NULL,
    external_system_id character varying(50),
    system_abbreviation character varying(50),
    main_admin character varying(100),
    backup_admin character varying(100),
    production_attribute character varying(50),
    system_provider character varying(255),
    system_function_summary text,
    business_department character varying(100),
    system_form character varying(20),
    cs_client_name character varying(255),
    bs_url character varying(255),
    ip_port character varying(2000),
    monitoring_system_name character varying(255),
    major_milestones text,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_system_admin_responsibility OWNER TO postgres;

--
-- TOC entry 5018 (class 0 OID 0)
-- Dependencies: 231
-- Name: TABLE cmdb_system_admin_responsibility; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_system_admin_responsibility IS '系统管理员责任表（外部机构）';


--
-- TOC entry 5019 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.external_system_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.external_system_id IS '外部系统编号，用于配置管理编号，保证唯一';


--
-- TOC entry 5020 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.system_abbreviation; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.system_abbreviation IS '业务系统简称，保证唯一';


--
-- TOC entry 5021 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.main_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.main_admin IS '主岗，即主要管理员的名字或标识';


--
-- TOC entry 5022 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.backup_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.backup_admin IS '备岗，即备用管理员的名字或标识';


--
-- TOC entry 5023 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.production_attribute; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.production_attribute IS '生产属性，例如开发、测试、生产等';


--
-- TOC entry 5024 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.system_provider; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.system_provider IS '系统提供方，即开发或维护该系统的组织或公司';


--
-- TOC entry 5025 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.system_function_summary; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.system_function_summary IS '系统功能简述，对系统的主要功能进行描述';


--
-- TOC entry 5026 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.business_department; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.business_department IS '负责该系统的业务部门';


--
-- TOC entry 5027 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.system_form; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.system_form IS '系统形态，如客户端/服务器(CS)、浏览器/服务器(BS)、基于IP的服务等';


--
-- TOC entry 5028 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.cs_client_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.cs_client_name IS '如果系统是CS架构，则填写客户端程序名称';


--
-- TOC entry 5029 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.bs_url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.bs_url IS '如果系统是BS架构，则填写URL地址';


--
-- TOC entry 5030 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.ip_port; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.ip_port IS '如果系统通过特定IP和端口访问，则填写此信息';


--
-- TOC entry 5031 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.monitoring_system_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.monitoring_system_name IS '用于监控调用的业务系统名称';


--
-- TOC entry 5032 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.major_milestones; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.major_milestones IS '记录系统的重要事件，如上线、下线、更换服务器等';


--
-- TOC entry 5033 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.created_at IS '创建时间';


--
-- TOC entry 5034 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.created_by IS '创建人';


--
-- TOC entry 5035 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.updated_at IS '更新时间';


--
-- TOC entry 5036 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.updated_by IS '更新人';


--
-- TOC entry 5037 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN cmdb_system_admin_responsibility.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 222 (class 1259 OID 16451)
-- Name: system_admin_responsibility_company_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.system_admin_responsibility_company_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_admin_responsibility_company_id_seq OWNER TO postgres;

--
-- TOC entry 232 (class 1259 OID 16538)
-- Name: cmdb_system_admin_responsibility_company; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_system_admin_responsibility_company (
    id integer DEFAULT nextval('public.system_admin_responsibility_company_id_seq'::regclass) NOT NULL,
    self_build_system_id character varying(50),
    system_abbreviation character varying(100),
    main_admin character varying(100),
    backup_admin character varying(100),
    business_department character varying(255),
    system_attribute character varying(255),
    go_live_date character varying(10),
    decommission_date character varying(10),
    major_milestones text,
    industry_name character varying(255),
    monitoring_system_name character varying(255),
    system_function_summary text,
    system_form character varying(20),
    cs_client_name character varying(255),
    bs_url character varying(255),
    ip_port text,
    business_line character varying(255),
    system_category character varying(100),
    system_level character varying(255),
    has_backup_strategy character varying(10),
    server_count integer,
    remarks text,
    monitoring_coverage character varying(20),
    digital_classification character varying(100),
    jrt_0059_backup_standard character varying(255),
    xinchuang_category_major character varying(100),
    xinchuang_category_minor character varying(100),
    is_reported_to_external character varying(10),
    centos7_count integer,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    technical_route character varying(50),
    software_copyright_name character varying(255),
    construction_method character varying(50),
    operation_status character varying(50),
    xinchuang_status character varying(50),
    security_level character varying(50),
    general_function_domains text,
    futures_function_domains text
);


ALTER TABLE public.cmdb_system_admin_responsibility_company OWNER TO postgres;

--
-- TOC entry 5038 (class 0 OID 0)
-- Dependencies: 232
-- Name: TABLE cmdb_system_admin_responsibility_company; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_system_admin_responsibility_company IS '系统管理员责任表（公司管理）';


--
-- TOC entry 5039 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.self_build_system_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.self_build_system_id IS '自建系统编号（用于配置管理编号，唯一编号）';


--
-- TOC entry 5040 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_abbreviation; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_abbreviation IS '业务系统简称（应用系统信息登记表/系统变更名称）';


--
-- TOC entry 5041 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.main_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.main_admin IS '系统管理员主岗';


--
-- TOC entry 5042 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.backup_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.backup_admin IS '备岗';


--
-- TOC entry 5043 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.business_department; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.business_department IS '业务主管部门（变更流程中涉及到的部门）';


--
-- TOC entry 5044 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_attribute; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_attribute IS '系统属性';


--
-- TOC entry 5045 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.go_live_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.go_live_date IS '上线时间';


--
-- TOC entry 5046 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.decommission_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.decommission_date IS '下线时间';


--
-- TOC entry 5047 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.major_milestones; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.major_milestones IS '重大历程（上线、下线、更换服务器等）';


--
-- TOC entry 5048 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.industry_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.industry_name IS '行业名称（对外报送使用）';


--
-- TOC entry 5049 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.monitoring_system_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.monitoring_system_name IS '业务系统名称（监控及主机命名）';


--
-- TOC entry 5050 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_function_summary; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_function_summary IS '系统功能简述';


--
-- TOC entry 5051 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_form; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_form IS '系统形态';


--
-- TOC entry 5052 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.cs_client_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.cs_client_name IS 'CS:客户端程序名称';


--
-- TOC entry 5053 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.bs_url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.bs_url IS 'BS:URL地址';


--
-- TOC entry 5054 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.ip_port; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.ip_port IS 'IP:端口';


--
-- TOC entry 5055 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.business_line; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.business_line IS '业务线条';


--
-- TOC entry 5056 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_category IS '系统大类';


--
-- TOC entry 5057 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.system_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.system_level IS '系统分级（一级为必须监控实时交易，二级为必须监控非实时交易，三级为办公类系统）';


--
-- TOC entry 5058 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.has_backup_strategy; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.has_backup_strategy IS '是否建立备份策略';


--
-- TOC entry 5059 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.server_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.server_count IS '对应服务器数量';


--
-- TOC entry 5060 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.remarks IS '备注';


--
-- TOC entry 5061 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.monitoring_coverage; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.monitoring_coverage IS '监控覆盖率';


--
-- TOC entry 5062 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.digital_classification; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.digital_classification IS '数字化分类';


--
-- TOC entry 5063 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.jrt_0059_backup_standard; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.jrt_0059_backup_standard IS 'JR/T 0059—2010备份能力标准';


--
-- TOC entry 5064 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.xinchuang_category_major; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.xinchuang_category_major IS '信创大类';


--
-- TOC entry 5065 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.xinchuang_category_minor; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.xinchuang_category_minor IS '信创小类';


--
-- TOC entry 5066 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.is_reported_to_external; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.is_reported_to_external IS '是否上报外部机构';


--
-- TOC entry 5067 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.centos7_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.centos7_count IS 'CentOS7数量';


--
-- TOC entry 5068 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.created_at IS '创建时间';


--
-- TOC entry 5069 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.created_by IS '创建人';


--
-- TOC entry 5070 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.updated_at IS '更新时间';


--
-- TOC entry 5071 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.updated_by IS '更新人';


--
-- TOC entry 5072 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 5073 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.technical_route; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.technical_route IS '技术路线';


--
-- TOC entry 5074 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.construction_method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.construction_method IS '建设方式';


--
-- TOC entry 5075 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.operation_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.operation_status IS '运行状态（建设中、运行中、已下线）';


--
-- TOC entry 5076 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.xinchuang_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.xinchuang_status IS '信创状态（未信创、完成开发或测试、非全栈双轨、非全栈单轨、全栈双轨、全栈单轨）';


--
-- TOC entry 5077 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.security_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.security_level IS '等保等级（一级、二级、三级）';


--
-- TOC entry 5078 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.general_function_domains; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.general_function_domains IS '通用功能域（多选，JSON格式存储功能域编号）';


--
-- TOC entry 5079 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN cmdb_system_admin_responsibility_company.futures_function_domains; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company.futures_function_domains IS '期货经营机构功能域（多选，JSON格式存储功能域编号）';


--
-- TOC entry 304 (class 1259 OID 18429)
-- Name: cmdb_system_admin_responsibility_company_20250731; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_system_admin_responsibility_company_20250731 (
    id integer DEFAULT nextval('public.system_admin_responsibility_company_id_seq'::regclass) NOT NULL,
    self_build_system_id character varying(50),
    system_abbreviation character varying(100),
    main_admin character varying(100),
    backup_admin character varying(100),
    business_department character varying(255),
    system_attribute character varying(255),
    go_live_date character varying(10),
    decommission_date character varying(10),
    major_milestones text,
    industry_name character varying(255),
    monitoring_system_name character varying(255),
    system_function_summary text,
    system_form character varying(20),
    cs_client_name character varying(255),
    bs_url character varying(255),
    ip_port text,
    business_line character varying(255),
    system_category character varying(100),
    system_level character varying(255),
    has_backup_strategy character varying(10),
    server_count integer,
    remarks text,
    monitoring_coverage character varying(20),
    digital_classification character varying(100),
    jrt_0059_backup_standard character varying(255),
    xinchuang_category_major character varying(100),
    xinchuang_category_minor character varying(100),
    is_reported_to_external character varying(10),
    centos7_count integer,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    technical_route character varying(50),
    software_copyright_name character varying(255),
    construction_method character varying(50),
    operation_status character varying(50),
    xinchuang_status character varying(50),
    security_level character varying(50),
    general_function_domains text,
    futures_function_domains text
);


ALTER TABLE public.cmdb_system_admin_responsibility_company_20250731 OWNER TO postgres;

--
-- TOC entry 5080 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.self_build_system_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.self_build_system_id IS '自建系统编号（用于配置管理编号，唯一编号）';


--
-- TOC entry 5081 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_abbreviation; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_abbreviation IS '业务系统简称（应用系统信息登记表/系统变更名称）';


--
-- TOC entry 5082 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.main_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.main_admin IS '系统管理员主岗';


--
-- TOC entry 5083 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.backup_admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.backup_admin IS '备岗';


--
-- TOC entry 5084 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.business_department; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.business_department IS '业务主管部门（变更流程中涉及到的部门）';


--
-- TOC entry 5085 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_attribute; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_attribute IS '系统属性';


--
-- TOC entry 5086 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.go_live_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.go_live_date IS '上线时间';


--
-- TOC entry 5087 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.decommission_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.decommission_date IS '下线时间';


--
-- TOC entry 5088 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.major_milestones; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.major_milestones IS '重大历程（上线、下线、更换服务器等）';


--
-- TOC entry 5089 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.industry_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.industry_name IS '行业名称（对外报送使用）';


--
-- TOC entry 5090 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.monitoring_system_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.monitoring_system_name IS '业务系统名称（监控及主机命名）';


--
-- TOC entry 5091 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_function_summary; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_function_summary IS '系统功能简述';


--
-- TOC entry 5092 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_form; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_form IS '系统形态';


--
-- TOC entry 5093 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.cs_client_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.cs_client_name IS 'CS:客户端程序名称';


--
-- TOC entry 5094 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.bs_url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.bs_url IS 'BS:URL地址';


--
-- TOC entry 5095 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.ip_port; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.ip_port IS 'IP:端口';


--
-- TOC entry 5096 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.business_line; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.business_line IS '业务线条';


--
-- TOC entry 5097 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_category; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_category IS '系统大类';


--
-- TOC entry 5098 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.system_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.system_level IS '系统分级（一级为必须监控实时交易，二级为必须监控非实时交易，三级为办公类系统）';


--
-- TOC entry 5099 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.has_backup_strategy; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.has_backup_strategy IS '是否建立备份策略';


--
-- TOC entry 5100 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.server_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.server_count IS '对应服务器数量';


--
-- TOC entry 5101 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.remarks IS '备注';


--
-- TOC entry 5102 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.monitoring_coverage; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.monitoring_coverage IS '监控覆盖率';


--
-- TOC entry 5103 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.digital_classification; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.digital_classification IS '数字化分类';


--
-- TOC entry 5104 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.jrt_0059_backup_standard; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.jrt_0059_backup_standard IS 'JR/T 0059—2010备份能力标准';


--
-- TOC entry 5105 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.xinchuang_category_major; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.xinchuang_category_major IS '信创大类';


--
-- TOC entry 5106 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.xinchuang_category_minor; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.xinchuang_category_minor IS '信创小类';


--
-- TOC entry 5107 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.is_reported_to_external; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.is_reported_to_external IS '是否上报外部机构';


--
-- TOC entry 5108 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.centos7_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.centos7_count IS 'CentOS7数量';


--
-- TOC entry 5109 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.created_at IS '创建时间';


--
-- TOC entry 5110 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.created_by IS '创建人';


--
-- TOC entry 5111 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.updated_at IS '更新时间';


--
-- TOC entry 5112 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.updated_by IS '更新人';


--
-- TOC entry 5113 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 5114 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.technical_route; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.technical_route IS '技术路线';


--
-- TOC entry 5115 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.construction_method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.construction_method IS '建设方式';


--
-- TOC entry 5116 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.operation_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.operation_status IS '运行状态（建设中、运行中、已下线）';


--
-- TOC entry 5117 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.xinchuang_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.xinchuang_status IS '信创状态（未信创、完成开发或测试、非全栈双轨、非全栈单轨、全栈双轨、全栈单轨）';


--
-- TOC entry 5118 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.security_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.security_level IS '等保等级（一级、二级、三级）';


--
-- TOC entry 5119 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.general_function_domains; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.general_function_domains IS '通用功能域（多选，JSON格式存储功能域编号）';


--
-- TOC entry 5120 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN cmdb_system_admin_responsibility_company_20250731.futures_function_domains; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_admin_responsibility_company_20250731.futures_function_domains IS '期货经营机构功能域（多选，JSON格式存储功能域编号）';


--
-- TOC entry 266 (class 1259 OID 17369)
-- Name: cmdb_system_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_system_config (
    id integer NOT NULL,
    config_key character varying(100) NOT NULL,
    config_value text,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by character varying(50) NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50)
);


ALTER TABLE public.cmdb_system_config OWNER TO postgres;

--
-- TOC entry 5122 (class 0 OID 0)
-- Dependencies: 266
-- Name: TABLE cmdb_system_config; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_system_config IS '系统配置表';


--
-- TOC entry 5123 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.id IS '主键ID';


--
-- TOC entry 5124 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.config_key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.config_key IS '配置键';


--
-- TOC entry 5125 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.config_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.config_value IS '配置值';


--
-- TOC entry 5126 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.description IS '配置描述';


--
-- TOC entry 5127 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.created_at IS '创建时间';


--
-- TOC entry 5128 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.created_by IS '创建人';


--
-- TOC entry 5129 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.updated_at IS '更新时间';


--
-- TOC entry 5130 (class 0 OID 0)
-- Dependencies: 266
-- Name: COLUMN cmdb_system_config.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_system_config.updated_by IS '更新人';


--
-- TOC entry 265 (class 1259 OID 17368)
-- Name: cmdb_system_config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_system_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_system_config_id_seq OWNER TO postgres;

--
-- TOC entry 5131 (class 0 OID 0)
-- Dependencies: 265
-- Name: cmdb_system_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_system_config_id_seq OWNED BY public.cmdb_system_config.id;


--
-- TOC entry 249 (class 1259 OID 17189)
-- Name: cmdb_user_login_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_user_login_logs (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    login_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    login_ip character varying(50),
    login_status character varying(20) NOT NULL,
    login_method character varying(20),
    login_device character varying(255),
    remarks text
);


ALTER TABLE public.cmdb_user_login_logs OWNER TO postgres;

--
-- TOC entry 5132 (class 0 OID 0)
-- Dependencies: 249
-- Name: TABLE cmdb_user_login_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_user_login_logs IS '用户登录记录表';


--
-- TOC entry 5133 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.id IS '主键ID';


--
-- TOC entry 5134 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.username; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.username IS '登录用户名';


--
-- TOC entry 5135 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.login_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.login_time IS '登录时间';


--
-- TOC entry 5136 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.login_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.login_ip IS '登录IP地址';


--
-- TOC entry 5137 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.login_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.login_status IS '登录状态：success-成功，failed-失败';


--
-- TOC entry 5138 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.login_method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.login_method IS '登录方式：local-本地认证，ldap-LDAP认证';


--
-- TOC entry 5139 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.login_device; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.login_device IS '登录设备信息';


--
-- TOC entry 5140 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN cmdb_user_login_logs.remarks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_login_logs.remarks IS '备注信息';


--
-- TOC entry 248 (class 1259 OID 17188)
-- Name: cmdb_user_login_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_user_login_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_user_login_logs_id_seq OWNER TO postgres;

--
-- TOC entry 5141 (class 0 OID 0)
-- Dependencies: 248
-- Name: cmdb_user_login_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_user_login_logs_id_seq OWNED BY public.cmdb_user_login_logs.id;


--
-- TOC entry 233 (class 1259 OID 16549)
-- Name: cmdb_user_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_user_logs (
    id integer NOT NULL,
    method character varying(10),
    url text,
    body jsonb,
    username character varying(255),
    operation_type character varying(50),
    "timestamp" timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.cmdb_user_logs OWNER TO postgres;

--
-- TOC entry 5142 (class 0 OID 0)
-- Dependencies: 233
-- Name: TABLE cmdb_user_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_user_logs IS '用户操作日志表';


--
-- TOC entry 5143 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.id IS '主键ID';


--
-- TOC entry 5144 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.method IS '请求方式';


--
-- TOC entry 5145 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.url IS '请求url';


--
-- TOC entry 5146 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.body; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.body IS '请求体';


--
-- TOC entry 5147 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.username; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.username IS '请求用户名';


--
-- TOC entry 5148 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs.operation_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs.operation_type IS '请求操作类型';


--
-- TOC entry 5149 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN cmdb_user_logs."timestamp"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_logs."timestamp" IS '请求时间';


--
-- TOC entry 219 (class 1259 OID 16447)
-- Name: cmdb_user_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_user_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_user_logs_id_seq OWNER TO postgres;

--
-- TOC entry 5150 (class 0 OID 0)
-- Dependencies: 219
-- Name: cmdb_user_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_user_logs_id_seq OWNED BY public.cmdb_user_logs.id;


--
-- TOC entry 263 (class 1259 OID 17323)
-- Name: cmdb_user_page_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_user_page_permissions (
    id integer NOT NULL,
    user_id integer NOT NULL,
    page_id integer NOT NULL,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.cmdb_user_page_permissions OWNER TO postgres;

--
-- TOC entry 5151 (class 0 OID 0)
-- Dependencies: 263
-- Name: TABLE cmdb_user_page_permissions; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_user_page_permissions IS '用户页面权限表';


--
-- TOC entry 5152 (class 0 OID 0)
-- Dependencies: 263
-- Name: COLUMN cmdb_user_page_permissions.user_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_page_permissions.user_id IS '用户ID';


--
-- TOC entry 5153 (class 0 OID 0)
-- Dependencies: 263
-- Name: COLUMN cmdb_user_page_permissions.page_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_page_permissions.page_id IS '页面ID';


--
-- TOC entry 5154 (class 0 OID 0)
-- Dependencies: 263
-- Name: COLUMN cmdb_user_page_permissions.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_user_page_permissions.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 262 (class 1259 OID 17322)
-- Name: cmdb_user_page_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_user_page_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cmdb_user_page_permissions_id_seq OWNER TO postgres;

--
-- TOC entry 5155 (class 0 OID 0)
-- Dependencies: 262
-- Name: cmdb_user_page_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_user_page_permissions_id_seq OWNED BY public.cmdb_user_page_permissions.id;


--
-- TOC entry 242 (class 1259 OID 16783)
-- Name: cmdb_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_users (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(255) NOT NULL,
    role_code character varying(20),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    real_name character varying(50),
    phone character varying(20),
    email character varying(100),
    wechat_id character varying(50)
);


ALTER TABLE public.cmdb_users OWNER TO postgres;

--
-- TOC entry 5156 (class 0 OID 0)
-- Dependencies: 242
-- Name: TABLE cmdb_users; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_users IS '系统用户表';


--
-- TOC entry 5157 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.id IS '主键ID';


--
-- TOC entry 5158 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.username; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.username IS '用户名';


--
-- TOC entry 5159 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.password; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.password IS '密码';


--
-- TOC entry 5160 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.role_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.role_code IS '角色代码；I增,D删,U改,(查询默认都有)';


--
-- TOC entry 5161 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.created_at IS '创建时间';


--
-- TOC entry 5162 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.created_by IS '创建人';


--
-- TOC entry 5163 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.updated_at IS '更新时间';


--
-- TOC entry 5164 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.updated_by IS '更新人';


--
-- TOC entry 5165 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.del_flag IS '是否删除;0:正常;1:已删除';


--
-- TOC entry 5166 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.real_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.real_name IS '用户姓名';


--
-- TOC entry 5167 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.phone; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.phone IS '联系电话';


--
-- TOC entry 5168 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.email; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.email IS '电子邮箱';


--
-- TOC entry 5169 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN cmdb_users.wechat_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_users.wechat_id IS '企业微信ID';


--
-- TOC entry 240 (class 1259 OID 16772)
-- Name: cmdb_users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cmdb_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


ALTER SEQUENCE public.cmdb_users_id_seq OWNER TO postgres;

--
-- TOC entry 5170 (class 0 OID 0)
-- Dependencies: 240
-- Name: cmdb_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cmdb_users_id_seq OWNED BY public.cmdb_users.id;


--
-- TOC entry 224 (class 1259 OID 16453)
-- Name: vm_registry_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vm_registry_id_seq
    START WITH 1111
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vm_registry_id_seq OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 16565)
-- Name: cmdb_vm_registry; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cmdb_vm_registry (
    id integer DEFAULT nextval('public.vm_registry_id_seq'::regclass) NOT NULL,
    management_ip character varying(15) NOT NULL,
    hostname character varying(255) NOT NULL,
    function_purpose character varying(255) NOT NULL,
    admin1 character varying(255) NOT NULL,
    admin2 character varying(255),
    host_ip character varying(15),
    operating_system character varying(255) NOT NULL,
    data_center1 character varying(255),
    admin character varying(255),
    app_system_id character varying(255),
    virtual_host_ip character varying(15),
    data_center2 character varying(255),
    is_monitored character varying(10),
    weak_password_exists character varying(10) DEFAULT ''::character varying,
    weak_password_correction_date date,
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(20) DEFAULT 'admin'::character varying,
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(20) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying,
    monitoring_requirement boolean DEFAULT true NOT NULL,
    monitoring_requirement_description text,
    host_ip_last_updated timestamp(6) without time zone,
    host_ip_update_source character varying(50) DEFAULT 'manual'::character varying,
    operation_status character varying(50) NOT NULL
);


ALTER TABLE public.cmdb_vm_registry OWNER TO postgres;

--
-- TOC entry 5171 (class 0 OID 0)
-- Dependencies: 234
-- Name: TABLE cmdb_vm_registry; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.cmdb_vm_registry IS '虚拟机登记表（系统管理员认领）';


--
-- TOC entry 5172 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.id IS '序号';


--
-- TOC entry 5173 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.management_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.management_ip IS '管理IP
（系统管理员填写）';


--
-- TOC entry 5174 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.hostname; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.hostname IS '主机名
（系统管理员填写）';


--
-- TOC entry 5175 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.function_purpose; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.function_purpose IS '功能用途
（系统管理员填写）';


--
-- TOC entry 5176 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.admin1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.admin1 IS '管理员1
（系统管理员填写）';


--
-- TOC entry 5177 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.admin2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.admin2 IS '管理员2
（系统管理员填写）';


--
-- TOC entry 5178 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.host_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.host_ip IS '宿主机IP
（系统管理员填写）';


--
-- TOC entry 5179 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.operating_system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.operating_system IS '操作系统
（系统管理员填写）';


--
-- TOC entry 5180 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.data_center1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.data_center1 IS '所属机房
（公式自动获取）';


--
-- TOC entry 5181 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.admin; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.admin IS '管理员
（公式自动获取）
"应用系统信息登记表"';


--
-- TOC entry 5182 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.app_system_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.app_system_id IS '虚拟机所属系统
（公式自动获取）
关联 "应用系统信息登记表"';


--
-- TOC entry 5183 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.virtual_host_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.virtual_host_ip IS '虚拟机所属宿主机IP
（公式自动获取）';


--
-- TOC entry 5184 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.data_center2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.data_center2 IS '所属机房
（公式自动获取）';


--
-- TOC entry 5185 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.is_monitored; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.is_monitored IS '是否监控
（公式自动获取）';


--
-- TOC entry 5186 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.weak_password_exists; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.weak_password_exists IS '是否存在弱密码';


--
-- TOC entry 5187 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.weak_password_correction_date; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.weak_password_correction_date IS '弱密码修正时间';


--
-- TOC entry 5188 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.monitoring_requirement; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.monitoring_requirement IS '监控需求（必填）：TRUE=是，FALSE=否';


--
-- TOC entry 5189 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.monitoring_requirement_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.monitoring_requirement_description IS '监控需求说明：当monitoring_requirement为FALSE时为必填项';


--
-- TOC entry 5190 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.host_ip_last_updated; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.host_ip_last_updated IS '宿主机IP最后更新时间';


--
-- TOC entry 5191 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.host_ip_update_source; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.host_ip_update_source IS '宿主机IP更新来源：manual=手动更新，zabbix_api=Zabbix API自动更新，batch_update=批量更新';


--
-- TOC entry 5192 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN cmdb_vm_registry.operation_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cmdb_vm_registry.operation_status IS '生命周期
（系统管理员填写）';


--
-- TOC entry 289 (class 1259 OID 18026)
-- Name: duplicate_count; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.duplicate_count (
    count bigint
);


ALTER TABLE public.duplicate_count OWNER TO postgres;

--
-- TOC entry 291 (class 1259 OID 18033)
-- Name: msg_push_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.msg_push_config (
    id integer NOT NULL,
    config_name character varying(100) NOT NULL,
    config_type character varying(20) DEFAULT 'webhook'::character varying NOT NULL,
    webhook_url text,
    webhook_method character varying(10) DEFAULT 'POST'::character varying,
    webhook_headers jsonb,
    email_smtp_host character varying(100),
    email_smtp_port integer,
    email_username character varying(100),
    email_password character varying(255),
    email_from character varying(100),
    email_to text,
    dingtalk_webhook text,
    dingtalk_secret character varying(255),
    wechat_webhook text,
    is_active boolean DEFAULT true,
    description text,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_by character varying(50) DEFAULT 'admin'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.msg_push_config OWNER TO postgres;

--
-- TOC entry 5194 (class 0 OID 0)
-- Dependencies: 291
-- Name: TABLE msg_push_config; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.msg_push_config IS '消息推送配置表';


--
-- TOC entry 5195 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.config_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.config_name IS '配置名称';


--
-- TOC entry 5196 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.config_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.config_type IS '推送类型：webhook, email, dingtalk, wechat';


--
-- TOC entry 5197 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.webhook_url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.webhook_url IS 'Webhook URL地址';


--
-- TOC entry 5198 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.webhook_method; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.webhook_method IS 'HTTP请求方法';


--
-- TOC entry 5199 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.webhook_headers; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.webhook_headers IS 'HTTP请求头';


--
-- TOC entry 5200 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_smtp_host; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_smtp_host IS '邮件SMTP主机';


--
-- TOC entry 5201 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_smtp_port; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_smtp_port IS '邮件SMTP端口';


--
-- TOC entry 5202 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_username; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_username IS '邮件用户名';


--
-- TOC entry 5203 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_password; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_password IS '邮件密码(加密存储)';


--
-- TOC entry 5204 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_from; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_from IS '发送方邮箱';


--
-- TOC entry 5205 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.email_to; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.email_to IS '接收方邮箱列表(逗号分隔)';


--
-- TOC entry 5206 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.dingtalk_webhook; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.dingtalk_webhook IS '钉钉机器人Webhook地址';


--
-- TOC entry 5207 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.dingtalk_secret; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.dingtalk_secret IS '钉钉机器人签名密钥';


--
-- TOC entry 5208 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.wechat_webhook; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.wechat_webhook IS '企业微信机器人Webhook地址';


--
-- TOC entry 5209 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.is_active IS '是否启用';


--
-- TOC entry 5210 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.description IS '配置描述';


--
-- TOC entry 5211 (class 0 OID 0)
-- Dependencies: 291
-- Name: COLUMN msg_push_config.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_config.del_flag IS '删除标志';


--
-- TOC entry 290 (class 1259 OID 18032)
-- Name: msg_push_config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.msg_push_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.msg_push_config_id_seq OWNER TO postgres;

--
-- TOC entry 5213 (class 0 OID 0)
-- Dependencies: 290
-- Name: msg_push_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.msg_push_config_id_seq OWNED BY public.msg_push_config.id;


--
-- TOC entry 297 (class 1259 OID 18144)
-- Name: msg_push_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.msg_push_history (
    id integer NOT NULL,
    task_id integer NOT NULL,
    config_id integer NOT NULL,
    message_title character varying(200),
    message_content text,
    push_status character varying(20) DEFAULT 'pending'::character varying,
    response_code integer,
    response_message text,
    retry_count integer DEFAULT 0,
    max_retry integer DEFAULT 3,
    push_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    success_time timestamp without time zone,
    error_message text,
    related_data jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.msg_push_history OWNER TO postgres;

--
-- TOC entry 5214 (class 0 OID 0)
-- Dependencies: 297
-- Name: TABLE msg_push_history; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.msg_push_history IS '消息推送历史表';


--
-- TOC entry 5215 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.task_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.task_id IS '推送任务ID';


--
-- TOC entry 5216 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.config_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.config_id IS '推送配置ID';


--
-- TOC entry 5217 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.message_title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.message_title IS '消息标题';


--
-- TOC entry 5218 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.message_content; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.message_content IS '消息内容';


--
-- TOC entry 5219 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.push_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.push_status IS '推送状态：pending, sending, success, failed';


--
-- TOC entry 5220 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.response_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.response_code IS '响应状态码';


--
-- TOC entry 5221 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.response_message; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.response_message IS '响应消息';


--
-- TOC entry 5222 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.retry_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.retry_count IS '重试次数';


--
-- TOC entry 5223 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.max_retry; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.max_retry IS '最大重试次数';


--
-- TOC entry 5224 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.push_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.push_time IS '推送时间';


--
-- TOC entry 5225 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.success_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.success_time IS '成功时间';


--
-- TOC entry 5226 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.error_message; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.error_message IS '错误信息';


--
-- TOC entry 5227 (class 0 OID 0)
-- Dependencies: 297
-- Name: COLUMN msg_push_history.related_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_history.related_data IS '关联数据(变更信息等)';


--
-- TOC entry 296 (class 1259 OID 18143)
-- Name: msg_push_history_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.msg_push_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.msg_push_history_id_seq OWNER TO postgres;

--
-- TOC entry 5229 (class 0 OID 0)
-- Dependencies: 296
-- Name: msg_push_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.msg_push_history_id_seq OWNED BY public.msg_push_history.id;


--
-- TOC entry 295 (class 1259 OID 18112)
-- Name: msg_push_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.msg_push_tasks (
    id integer NOT NULL,
    task_name character varying(100) NOT NULL,
    task_type character varying(20) DEFAULT 'change_notification'::character varying NOT NULL,
    config_id integer NOT NULL,
    schedule_type character varying(20) DEFAULT 'manual'::character varying NOT NULL,
    schedule_value character varying(100),
    next_run_time timestamp without time zone,
    template_id integer,
    filter_conditions jsonb,
    is_active boolean DEFAULT true,
    status character varying(20) DEFAULT 'pending'::character varying,
    description text,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_by character varying(50) DEFAULT 'admin'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.msg_push_tasks OWNER TO postgres;

--
-- TOC entry 5230 (class 0 OID 0)
-- Dependencies: 295
-- Name: TABLE msg_push_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.msg_push_tasks IS '消息推送任务表';


--
-- TOC entry 5231 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.task_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.task_name IS '任务名称';


--
-- TOC entry 5232 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.task_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.task_type IS '任务类型：change_notification, event_notification, custom';


--
-- TOC entry 5233 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.config_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.config_id IS '推送配置ID';


--
-- TOC entry 5234 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.schedule_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.schedule_type IS '调度类型：manual, once, daily, weekly, monthly, cron';


--
-- TOC entry 5235 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.schedule_value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.schedule_value IS '调度值(cron表达式或时间)';


--
-- TOC entry 5236 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.next_run_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.next_run_time IS '下次执行时间';


--
-- TOC entry 5237 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.template_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.template_id IS '消息模板ID';


--
-- TOC entry 5238 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.filter_conditions; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.filter_conditions IS '过滤条件JSON';


--
-- TOC entry 5239 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.is_active IS '是否启用';


--
-- TOC entry 5240 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.status IS '状态：pending, running, success, failed';


--
-- TOC entry 5241 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.description IS '任务描述';


--
-- TOC entry 5242 (class 0 OID 0)
-- Dependencies: 295
-- Name: COLUMN msg_push_tasks.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_tasks.del_flag IS '删除标志';


--
-- TOC entry 294 (class 1259 OID 18111)
-- Name: msg_push_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.msg_push_tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.msg_push_tasks_id_seq OWNER TO postgres;

--
-- TOC entry 5244 (class 0 OID 0)
-- Dependencies: 294
-- Name: msg_push_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.msg_push_tasks_id_seq OWNED BY public.msg_push_tasks.id;


--
-- TOC entry 293 (class 1259 OID 18094)
-- Name: msg_push_templates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.msg_push_templates (
    id integer NOT NULL,
    template_name character varying(100) NOT NULL,
    template_type character varying(20) NOT NULL,
    message_format character varying(20) DEFAULT 'text'::character varying,
    title_template text,
    content_template text NOT NULL,
    variables jsonb,
    is_default boolean DEFAULT false,
    description text,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_by character varying(50) DEFAULT 'admin'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.msg_push_templates OWNER TO postgres;

--
-- TOC entry 5245 (class 0 OID 0)
-- Dependencies: 293
-- Name: TABLE msg_push_templates; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.msg_push_templates IS '消息模板表';


--
-- TOC entry 5246 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.template_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.template_name IS '模板名称';


--
-- TOC entry 5247 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.template_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.template_type IS '模板类型：change_notification, event_notification, custom';


--
-- TOC entry 5248 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.message_format; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.message_format IS '消息格式：text, html, markdown, json';


--
-- TOC entry 5249 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.title_template; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.title_template IS '标题模板';


--
-- TOC entry 5250 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.content_template; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.content_template IS '内容模板';


--
-- TOC entry 5251 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.variables; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.variables IS '模板变量定义';


--
-- TOC entry 5252 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.is_default; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.is_default IS '是否默认模板';


--
-- TOC entry 5253 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.description IS '模板描述';


--
-- TOC entry 5254 (class 0 OID 0)
-- Dependencies: 293
-- Name: COLUMN msg_push_templates.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.msg_push_templates.del_flag IS '删除标志';


--
-- TOC entry 292 (class 1259 OID 18093)
-- Name: msg_push_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.msg_push_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.msg_push_templates_id_seq OWNER TO postgres;

--
-- TOC entry 5256 (class 0 OID 0)
-- Dependencies: 292
-- Name: msg_push_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.msg_push_templates_id_seq OWNED BY public.msg_push_templates.id;


--
-- TOC entry 277 (class 1259 OID 17714)
-- Name: ops_calendar; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_calendar (
    day character varying(8) NOT NULL,
    dat smallint NOT NULL,
    wrk smallint NOT NULL,
    tra smallint NOT NULL,
    sun smallint NOT NULL,
    mon smallint NOT NULL,
    tue smallint NOT NULL,
    wed smallint NOT NULL,
    thu smallint NOT NULL,
    fri smallint NOT NULL,
    sat smallint NOT NULL,
    str smallint NOT NULL,
    tal smallint NOT NULL,
    spr smallint NOT NULL,
    main_duty_user character varying(50),
    deputy_duty_user character varying(50),
    duty_manager character varying(50),
    simulation_user character varying(50),
    inspection_user character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_by character varying(50) DEFAULT 'admin'::character varying
);


ALTER TABLE public.ops_calendar OWNER TO postgres;

--
-- TOC entry 5257 (class 0 OID 0)
-- Dependencies: 277
-- Name: TABLE ops_calendar; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ops_calendar IS '交易日历';


--
-- TOC entry 5258 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.day; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.day IS '（自然）日期';


--
-- TOC entry 5259 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.dat; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.dat IS '公历日';


--
-- TOC entry 5260 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.wrk; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.wrk IS '工作日';


--
-- TOC entry 5261 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.tra; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.tra IS '交易日';


--
-- TOC entry 5262 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.sun; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.sun IS '周日';


--
-- TOC entry 5263 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.mon; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.mon IS '周一';


--
-- TOC entry 5264 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.tue; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.tue IS '周二';


--
-- TOC entry 5265 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.wed; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.wed IS '周三';


--
-- TOC entry 5266 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.thu; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.thu IS '周四';


--
-- TOC entry 5267 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.fri; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.fri IS '周五';


--
-- TOC entry 5268 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.sat; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.sat IS '周六';


--
-- TOC entry 5269 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.str; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.str IS '月初';


--
-- TOC entry 5270 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.tal; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.tal IS '月末';


--
-- TOC entry 5271 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.spr; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.spr IS '春节';


--
-- TOC entry 5272 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.main_duty_user; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.main_duty_user IS '主班人员用户名';


--
-- TOC entry 5273 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.deputy_duty_user; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.deputy_duty_user IS '副班人员用户名';


--
-- TOC entry 5274 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.duty_manager; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.duty_manager IS '值班经理用户名';


--
-- TOC entry 5275 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.simulation_user; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.simulation_user IS '仿真人员用户名';


--
-- TOC entry 5276 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.inspection_user; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.inspection_user IS '巡检人员用户名';


--
-- TOC entry 5277 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.created_at IS '创建时间';


--
-- TOC entry 5278 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.updated_at IS '更新时间';


--
-- TOC entry 5279 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.created_by IS '创建人';


--
-- TOC entry 5280 (class 0 OID 0)
-- Dependencies: 277
-- Name: COLUMN ops_calendar.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar.updated_by IS '更新人';


--
-- TOC entry 279 (class 1259 OID 17729)
-- Name: ops_calendar_duty_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_calendar_duty_permissions (
    id integer NOT NULL,
    user_id integer NOT NULL,
    permission_type character varying(20) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50) DEFAULT 'admin'::character varying,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50) DEFAULT 'admin'::character varying,
    del_flag character varying(2) DEFAULT '0'::character varying
);


ALTER TABLE public.ops_calendar_duty_permissions OWNER TO postgres;

--
-- TOC entry 5282 (class 0 OID 0)
-- Dependencies: 279
-- Name: TABLE ops_calendar_duty_permissions; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ops_calendar_duty_permissions IS '值班排班权限表';


--
-- TOC entry 5283 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.user_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.user_id IS '用户ID';


--
-- TOC entry 5284 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.permission_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.permission_type IS '权限类型：edit-编辑权限，view-查看权限';


--
-- TOC entry 5285 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.created_at IS '创建时间';


--
-- TOC entry 5286 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.created_by IS '创建人';


--
-- TOC entry 5287 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.updated_at IS '更新时间';


--
-- TOC entry 5288 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.updated_by IS '更新人';


--
-- TOC entry 5289 (class 0 OID 0)
-- Dependencies: 279
-- Name: COLUMN ops_calendar_duty_permissions.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_calendar_duty_permissions.del_flag IS '删除标记';


--
-- TOC entry 278 (class 1259 OID 17728)
-- Name: ops_calendar_duty_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_calendar_duty_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_calendar_duty_permissions_id_seq OWNER TO postgres;

--
-- TOC entry 5291 (class 0 OID 0)
-- Dependencies: 278
-- Name: ops_calendar_duty_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_calendar_duty_permissions_id_seq OWNED BY public.ops_calendar_duty_permissions.id;


--
-- TOC entry 273 (class 1259 OID 17638)
-- Name: ops_change_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 9999
    CACHE 1
    CYCLE;


ALTER SEQUENCE public.ops_change_id_seq OWNER TO postgres;

--
-- TOC entry 272 (class 1259 OID 17609)
-- Name: ops_change_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_change_management (
    id integer NOT NULL,
    change_id character varying(20) NOT NULL,
    title character varying(255) NOT NULL,
    system character varying(100) NOT NULL,
    change_level character varying(10) NOT NULL,
    planned_change_time date NOT NULL,
    requester character varying(50) NOT NULL,
    implementers text NOT NULL,
    description text,
    oa_process boolean DEFAULT false,
    oa_process_file character varying(255),
    signed_archive boolean DEFAULT false,
    signed_archive_file character varying(255),
    operation_sheet character varying(255),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50),
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar,
    supplementary_material character varying(255)
);


ALTER TABLE public.ops_change_management OWNER TO postgres;

--
-- TOC entry 5292 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.change_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.change_id IS '变更编号，格式：BG-YYYYMMDD-序号';


--
-- TOC entry 5293 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.title IS '变更名称';


--
-- TOC entry 5294 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.system IS '所属系统，来自系统管理员责任表（公司）的业务系统简称';


--
-- TOC entry 5295 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.change_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.change_level IS '变更级别，使用数据字典P';


--
-- TOC entry 5296 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.planned_change_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.planned_change_time IS '计划变更时间';


--
-- TOC entry 5297 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.requester; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.requester IS '变更负责人，存储username';


--
-- TOC entry 5298 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.implementers; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.implementers IS '变更实施人，存储多个username，以逗号分隔';


--
-- TOC entry 5299 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.description IS '变更描述';


--
-- TOC entry 5300 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.oa_process; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.oa_process IS 'OA流程是否上传';


--
-- TOC entry 5301 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.oa_process_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.oa_process_file IS 'OA流程文件路径';


--
-- TOC entry 5302 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.signed_archive; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.signed_archive IS '签字存档是否上传';


--
-- TOC entry 5303 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.signed_archive_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.signed_archive_file IS '签字存档文件路径';


--
-- TOC entry 5304 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.operation_sheet; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.operation_sheet IS '变更操作表文件路径';


--
-- TOC entry 5305 (class 0 OID 0)
-- Dependencies: 272
-- Name: COLUMN ops_change_management.supplementary_material; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.supplementary_material IS '补充资料文件路径';


--
-- TOC entry 271 (class 1259 OID 17608)
-- Name: ops_change_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_management_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_change_management_id_seq OWNER TO postgres;

--
-- TOC entry 5306 (class 0 OID 0)
-- Dependencies: 271
-- Name: ops_change_management_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_change_management_id_seq OWNED BY public.ops_change_management.id;


--
-- TOC entry 275 (class 1259 OID 17650)
-- Name: ops_change_templates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_change_templates (
    id integer NOT NULL,
    template_name character varying(255) NOT NULL,
    template_description text,
    file_path character varying(255) NOT NULL,
    file_type character varying(200) NOT NULL,
    file_size integer NOT NULL,
    original_filename character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50) NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50) NOT NULL,
    del_flag character(1) DEFAULT '0'::bpchar,
    is_default boolean DEFAULT false
);


ALTER TABLE public.ops_change_templates OWNER TO postgres;

--
-- TOC entry 5307 (class 0 OID 0)
-- Dependencies: 275
-- Name: TABLE ops_change_templates; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ops_change_templates IS '变更模板管理表';


--
-- TOC entry 5308 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.id IS '主键';


--
-- TOC entry 5309 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.template_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.template_name IS '模板名称';


--
-- TOC entry 5310 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.template_description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.template_description IS '模板描述';


--
-- TOC entry 5311 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.file_path; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.file_path IS '文件路径';


--
-- TOC entry 5312 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.file_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.file_type IS '文件类型';


--
-- TOC entry 5313 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.file_size; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.file_size IS '文件大小';


--
-- TOC entry 5314 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.original_filename; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.original_filename IS '原始文件名';


--
-- TOC entry 5315 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.created_at IS '创建时间';


--
-- TOC entry 5316 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.created_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.created_by IS '创建人';


--
-- TOC entry 5317 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.updated_at IS '更新时间';


--
-- TOC entry 5318 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.updated_by; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.updated_by IS '更新人';


--
-- TOC entry 5319 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.del_flag; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.del_flag IS '删除标记';


--
-- TOC entry 5320 (class 0 OID 0)
-- Dependencies: 275
-- Name: COLUMN ops_change_templates.is_default; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_templates.is_default IS '是否为默认模板';


--
-- TOC entry 274 (class 1259 OID 17649)
-- Name: ops_change_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_change_templates_id_seq OWNER TO postgres;

--
-- TOC entry 5321 (class 0 OID 0)
-- Dependencies: 274
-- Name: ops_change_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_change_templates_id_seq OWNED BY public.ops_change_templates.id;


--
-- TOC entry 283 (class 1259 OID 17815)
-- Name: ops_event_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_event_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 9999
    CACHE 1
    CYCLE;


ALTER SEQUENCE public.ops_event_id_seq OWNER TO postgres;

--
-- TOC entry 282 (class 1259 OID 17791)
-- Name: ops_event_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_event_management (
    id integer NOT NULL,
    event_id character varying(20) NOT NULL,
    title character varying(255) NOT NULL,
    event_type character varying(20) DEFAULT 'T00002'::character varying NOT NULL,
    status character varying(20) DEFAULT '待处理'::character varying NOT NULL,
    priority character varying(10) DEFAULT 'U00002'::character varying NOT NULL,
    system character varying(100),
    reporter character varying(50) NOT NULL,
    assignee character varying(50),
    report_time timestamp(6) without time zone NOT NULL,
    close_time timestamp(6) without time zone,
    description text,
    process text,
    solution text,
    screenshot boolean DEFAULT false,
    screenshot_file character varying(255),
    process_doc boolean DEFAULT false,
    process_doc_file character varying(255),
    supplementary_material character varying(255),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50),
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar,
    improvement_plan text,
    consequence_cause_analysis text
);


ALTER TABLE public.ops_event_management OWNER TO postgres;

--
-- TOC entry 5322 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.event_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.event_id IS '事件编号，格式：SJ-YYYYMMDD-序号';


--
-- TOC entry 5323 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.title IS '事件标题';


--
-- TOC entry 5324 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.event_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.event_type IS '事件类型，使用数据字典T';


--
-- TOC entry 5325 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.status IS '事件状态，使用数据字典EVENT_STATUS';


--
-- TOC entry 5326 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.priority; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.priority IS '事件级别，使用数据字典U';


--
-- TOC entry 5327 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.system IS '影响系统，来自系统管理员责任表（公司）的业务系统简称';


--
-- TOC entry 5328 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.reporter; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.reporter IS '报告人，存储username';


--
-- TOC entry 5329 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.assignee; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.assignee IS '处理人，存储username';


--
-- TOC entry 5330 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.report_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.report_time IS '报告时间';


--
-- TOC entry 5331 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.close_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.close_time IS '关闭时间';


--
-- TOC entry 5332 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.description IS '事件描述';


--
-- TOC entry 5333 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.process; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process IS '处理过程';


--
-- TOC entry 5334 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.solution; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.solution IS '解决方案';


--
-- TOC entry 5335 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.screenshot; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.screenshot IS '事件截图是否上传';


--
-- TOC entry 5336 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.screenshot_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.screenshot_file IS '事件截图文件路径';


--
-- TOC entry 5337 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.process_doc; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process_doc IS '处理过程文档是否上传';


--
-- TOC entry 5338 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.process_doc_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process_doc_file IS '处理过程文档文件路径';


--
-- TOC entry 5339 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.supplementary_material; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.supplementary_material IS '补充材料文件路径';


--
-- TOC entry 5340 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.improvement_plan; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.improvement_plan IS '总结及知识库';


--
-- TOC entry 5341 (class 0 OID 0)
-- Dependencies: 282
-- Name: COLUMN ops_event_management.consequence_cause_analysis; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.consequence_cause_analysis IS '事件导致的后果、发生原因和事件性质判断';


--
-- TOC entry 281 (class 1259 OID 17790)
-- Name: ops_event_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_event_management_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_event_management_id_seq OWNER TO postgres;

--
-- TOC entry 5343 (class 0 OID 0)
-- Dependencies: 281
-- Name: ops_event_management_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_event_management_id_seq OWNED BY public.ops_event_management.id;


--
-- TOC entry 247 (class 1259 OID 17157)
-- Name: report_filter_favorites; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.report_filter_favorites (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    data_centers text DEFAULT '[]'::text NOT NULL,
    operation_statuses text DEFAULT '[]'::text NOT NULL,
    time_periods text DEFAULT '[]'::text NOT NULL,
    username character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    del_flag character(1) DEFAULT '0'::bpchar,
    innovative_tech_flags text DEFAULT '[]'::text NOT NULL
);


ALTER TABLE public.report_filter_favorites OWNER TO postgres;

--
-- TOC entry 246 (class 1259 OID 17156)
-- Name: report_filter_favorites_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.report_filter_favorites_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_filter_favorites_id_seq OWNER TO postgres;

--
-- TOC entry 5344 (class 0 OID 0)
-- Dependencies: 246
-- Name: report_filter_favorites_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.report_filter_favorites_id_seq OWNED BY public.report_filter_favorites.id;


--
-- TOC entry 264 (class 1259 OID 17351)
-- Name: v_cmdb_discovery_results; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_discovery_results AS
 SELECT r.id,
    r.task_id,
    t.task_name,
    r.ip_address,
    r.hostname,
    r.mac_address,
    r.device_type,
    r.open_ports,
    r.os_info,
    r.status,
    r.discovery_time,
    r.created_at,
    r.created_by,
    r.updated_at,
    r.updated_by,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_server_management s
              WHERE (((s.management_ip)::text = (r.ip_address)::text) AND ((s.del_flag)::text = '0'::text)))) THEN '已登记'::text
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_device_management d
              WHERE (((d.management_ip)::text = (r.ip_address)::text) AND ((d.del_flag)::text = '0'::text)))) THEN '已登记'::text
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_vm_registry v
              WHERE (((v.management_ip)::text = (r.ip_address)::text) AND ((v.del_flag)::text = '0'::text)))) THEN '已登记'::text
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_application_system_info a
              WHERE (((a.management_ip)::text = (r.ip_address)::text) AND ((a.del_flag)::text = '0'::text)))) THEN '已登记'::text
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_host_scan_results h
              WHERE (((h.management_ip)::text = (r.ip_address)::text) AND ((h.del_flag)::text = '0'::text)))) THEN '已登记'::text
            ELSE '未登记'::text
        END AS cmdb_status
   FROM (public.cmdb_discovery_results r
     JOIN public.cmdb_discovery_tasks t ON ((r.task_id = t.id)))
  WHERE ((r.del_flag = '0'::bpchar) AND (t.del_flag = '0'::bpchar));


ALTER VIEW public.v_cmdb_discovery_results OWNER TO postgres;

--
-- TOC entry 5345 (class 0 OID 0)
-- Dependencies: 264
-- Name: VIEW v_cmdb_discovery_results; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_discovery_results IS '自动发现结果视图';


--
-- TOC entry 286 (class 1259 OID 17965)
-- Name: v_cmdb_server_management; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_server_management AS
 SELECT t.id,
    t.management_ip,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    COALESCE(t2.dict_name, t.server_type) AS server_type,
    COALESCE(t3.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t4.dict_name, t.data_center) AS data_center,
    t.out_of_band_management_ilo,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    t.is_innovative_tech,
    t.is_single_point,
    t.managed_addresses,
    t.asset_number,
    to_char((t.purchase_date)::timestamp with time zone, 'yyyy-mm-dd'::text) AS purchase_date,
    t.maintenance_years,
    to_char((t.purchase_date + ('1 year'::interval * (t.maintenance_years)::double precision)), 'yyyy-mm-dd'::text) AS maintenance_end_date,
    t.serial_number,
    t.server_model,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_monitored_ip_list t6
              WHERE (((t6.del_flag)::text = '0'::text) AND (((t6.ip_address)::text = (t.management_ip)::text) OR ((t6.ip_address)::text = (t.out_of_band_management_ilo)::text))))) THEN '是'::text
            ELSE '否'::text
        END AS is_monitored,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.cpu_model,
    t.memory,
    t.disk,
    t.network_card,
    COALESCE(t7.dict_name, t.operating_system) AS operating_system,
    t.os_category,
    t.remarks,
    ((COALESCE(t4.dict_name, t.data_center))::text ||
        CASE
            WHEN (to_char((t.purchase_date)::timestamp with time zone, 'YYYY'::text) <= '2016'::text) THEN '2016'::text
            WHEN (to_char((t.purchase_date)::timestamp with time zone, 'YYYY'::text) <= '2019'::text) THEN '2019'::text
            ELSE '2020'::text
        END) AS year_category,
    COALESCE(t.weak_password_exists, '否'::character varying) AS weak_password_exists,
    to_char((t.weak_password_correction_date)::timestamp with time zone, 'yyyy-mm-dd'::text) AS weak_password_correction_date,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((public.cmdb_server_management t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.server_type)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.production_attributes)::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.dict_code)::text = (t.data_center)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.operation_status)::text))))
     LEFT JOIN public.cmdb_data_dictionary t7 ON ((((t7.del_flag)::text = '0'::text) AND ((t7.dict_code)::text = (t.operating_system)::text))))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_server_management OWNER TO postgres;

--
-- TOC entry 5347 (class 0 OID 0)
-- Dependencies: 286
-- Name: VIEW v_cmdb_server_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_server_management IS '服务器设备视图 - 包含监控需求字段';


--
-- TOC entry 305 (class 1259 OID 18446)
-- Name: v_cmdb_vm_registry; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_vm_registry AS
 SELECT t.id,
    t.management_ip,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    t.host_ip,
    COALESCE(t2.dict_name, t.operating_system) AS operating_system,
    COALESCE(t3.dict_name, t.data_center1) AS data_center1,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    t.admin,
    t.app_system_id,
    t.virtual_host_ip,
    t.data_center2,
    t.is_monitored,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            WHEN (t.monitoring_requirement = false) THEN '否'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.weak_password_exists,
    to_char((t.weak_password_correction_date)::timestamp with time zone, 'yyyy-mm-dd'::text) AS weak_password_correction_date,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((public.cmdb_vm_registry t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.operating_system)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.data_center1)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.operation_status)::text))))
     LEFT JOIN public.cmdb_application_system_info t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.management_ip)::text = (t.management_ip)::text))))
     LEFT JOIN ( SELECT t9_1.ip_address
           FROM public.cmdb_monitored_ip_list t9_1
          WHERE ((t9_1.del_flag)::text = '0'::text)
          GROUP BY t9_1.ip_address) t9 ON (((t9.ip_address)::text = (t.management_ip)::text)))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_vm_registry OWNER TO postgres;

--
-- TOC entry 5349 (class 0 OID 0)
-- Dependencies: 305
-- Name: VIEW v_cmdb_vm_registry; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_vm_registry IS '虚拟机视图：包含监控需求字段和生命周期字段，布尔值转换为中文显示';


--
-- TOC entry 306 (class 1259 OID 18451)
-- Name: v_cmdb_application_system_info; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_application_system_info AS
 SELECT t.id,
    t.management_ip,
    COALESCE(t6.hostname, t5.hostname, ''::text) AS hostname,
    COALESCE(t6.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    COALESCE(t6.admin1, t5.admin1, ''::text) AS server_admin1,
    COALESCE(t6.admin2, t5.admin2, ''::text) AS server_admin2,
    COALESCE(t6.data_center, t5.data_center, ''::text) AS data_center,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS machine_usage_status,
    t.remarks,
    t.business_system_name,
    COALESCE(( SELECT u1.real_name
           FROM public.cmdb_users u1
          WHERE (((u1.username)::text = t8.main_admin) AND ((u1.del_flag)::text = '0'::text) AND (u1.real_name IS NOT NULL) AND ((u1.real_name)::text <> ''::text))), ( SELECT u2.real_name
           FROM public.cmdb_users u2
          WHERE (((u2.real_name)::text = t8.main_admin) AND ((u2.del_flag)::text = '0'::text) AND (u2.real_name IS NOT NULL) AND ((u2.real_name)::text <> ''::text))), (t8.main_admin)::character varying) AS system_administrator,
    t8.system_level AS system_classification,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            WHEN (t.monitoring_requirement = false) THEN '否'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (t9.ip_address IS NOT NULL) THEN '是'::text
            ELSE '否'::text
        END AS is_monitored,
    t.deployed_applications,
    COALESCE(t2.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t3.dict_name, t.master_slave_role) AS master_slave_role,
    COALESCE(t4.dict_name, t.backup_mode) AS backup_mode,
    t.internet_ip,
    t.internet_port,
    t.related_master_slave_ips,
    COALESCE(t6.operating_system, t5.operating_system, ''::text) AS operating_system,
    COALESCE(t.has_antivirus_software, '是'::character varying) AS has_antivirus_software,
    COALESCE(t.patch_update_configured, '是'::character varying) AS patch_update_configured,
        CASE
            WHEN (t8.system_level = '一级'::text) THEN '有一级系统管理员'::text
            WHEN (t8.system_level = '二级'::text) THEN '有二级系统管理员'::text
            ELSE ''::text
        END AS has_system_administrator,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((((public.cmdb_application_system_info t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.production_attributes)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.master_slave_role)::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.dict_code)::text = (t.backup_mode)::text))))
     LEFT JOIN ( SELECT t5_1.management_ip,
            max((t5_1.hostname)::text) AS hostname,
            max((t5_1.function_purpose)::text) AS function_purpose,
            max((t5_1.admin1)::text) AS admin1,
            max((t5_1.admin2)::text) AS admin2,
            max((t5_1.data_center)::text) AS data_center,
            max((t5_1.operating_system)::text) AS operating_system
           FROM public.v_cmdb_server_management t5_1
          GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t6_1.management_ip,
            max((t6_1.hostname)::text) AS hostname,
            max((t6_1.function_purpose)::text) AS function_purpose,
            max((t6_1.admin1)::text) AS admin1,
            max((t6_1.admin2)::text) AS admin2,
            max((t6_1.data_center1)::text) AS data_center,
            max((t6_1.operating_system)::text) AS operating_system
           FROM public.v_cmdb_vm_registry t6_1
          GROUP BY t6_1.management_ip) t6 ON (((t6.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t8_1.system_abbreviation,
            max((t8_1.main_admin)::text) AS main_admin,
            max((COALESCE(t82.dict_name, t8_1.system_level))::text) AS system_level
           FROM (public.cmdb_system_admin_responsibility_company t8_1
             LEFT JOIN public.cmdb_data_dictionary t82 ON ((((t82.del_flag)::text = '0'::text) AND ((t82.dict_code)::text = (t8_1.system_level)::text))))
          WHERE ((t8_1.del_flag)::text = '0'::text)
          GROUP BY t8_1.system_abbreviation) t8 ON (((t8.system_abbreviation)::text = (t.business_system_name)::text)))
     LEFT JOIN ( SELECT t9_1.ip_address
           FROM public.cmdb_monitored_ip_list t9_1
          WHERE ((t9_1.del_flag)::text = '0'::text)
          GROUP BY t9_1.ip_address) t9 ON (((t9.ip_address)::text = (t.management_ip)::text)))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_application_system_info OWNER TO postgres;

--
-- TOC entry 245 (class 1259 OID 16843)
-- Name: v_cmdb_dashboard; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_dashboard AS
 WITH tmp AS (
         SELECT t.id AS my_id,
            t.created_at
           FROM public.cmdb_device_management t
          WHERE ((t.del_flag)::text = '0'::text)
        UNION ALL
         SELECT t.id AS my_id,
            t.created_at
           FROM public.cmdb_server_management t
          WHERE ((t.del_flag)::text = '0'::text)
        UNION ALL
         SELECT t.id AS my_id,
            t.created_at
           FROM public.cmdb_vm_registry t
          WHERE ((t.del_flag)::text = '0'::text)
        )
 SELECT t1.asset_count,
    t1.asset_growth,
    t2.config_count,
    t2.config_growth
   FROM (( SELECT count(*) AS asset_count,
            round((
                CASE
                    WHEN (sum(
                    CASE
                        WHEN (tmp.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END) = 0) THEN (0)::numeric
                    ELSE (((count(*) - sum(
                    CASE
                        WHEN (tmp.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END)))::numeric / (sum(
                    CASE
                        WHEN (tmp.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END))::numeric)
                END * (100)::numeric), 2) AS asset_growth
           FROM tmp) t1
     LEFT JOIN ( SELECT count(1) AS config_count,
            round((
                CASE
                    WHEN (sum(
                    CASE
                        WHEN (t2_1.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END) = 0) THEN (0)::numeric
                    ELSE (((count(*) - sum(
                    CASE
                        WHEN (t2_1.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END)))::numeric / (sum(
                    CASE
                        WHEN (t2_1.created_at <= date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone)) THEN 1
                        ELSE 0
                    END))::numeric)
                END * (100)::numeric), 2) AS config_growth
           FROM public.cmdb_data_dictionary t2_1
          WHERE ((t2_1.del_flag)::text = '0'::text)) t2 ON ((1 = 1)));


ALTER VIEW public.v_cmdb_dashboard OWNER TO postgres;

--
-- TOC entry 5352 (class 0 OID 0)
-- Dependencies: 245
-- Name: VIEW v_cmdb_dashboard; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_dashboard IS '仪表盘视图';


--
-- TOC entry 287 (class 1259 OID 17993)
-- Name: v_cmdb_device_management; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_device_management AS
 SELECT t.id,
    t.management_ip,
    t.out_of_band_management,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    COALESCE(t2.dict_name, t.device_type) AS device_type,
    COALESCE(t3.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t4.dict_name, t.data_center) AS data_center,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    t.asset_number,
    to_char((t.purchase_date)::timestamp with time zone, 'yyyy-mm-dd'::text) AS purchase_date,
    t.maintenance_years,
    to_char((t.purchase_date + ('1 year'::interval * (t.maintenance_years)::double precision)), 'yyyy-mm-dd'::text) AS maintenance_end_date,
    t.serial_number,
    t.model,
    t.version,
    t.cpu_model,
    t.is_innovative_tech,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_monitored_ip_list t6
              WHERE (((t6.del_flag)::text = '0'::text) AND (((t6.ip_address)::text = (t.management_ip)::text) OR ((t6.ip_address)::text = (t.out_of_band_management)::text) OR ((t6.ip_address)::text = (t.monitoring_ip)::text))))) THEN '是'::text
            ELSE '否'::text
        END AS is_monitored,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.monitoring_ip,
    t.architecture_mode,
    t.is_single_point,
    t.managed_addresses,
    t.remarks,
    ((COALESCE(t4.dict_name, t.data_center))::text ||
        CASE
            WHEN (to_char((t.purchase_date)::timestamp with time zone, 'YYYY'::text) <= '2016'::text) THEN '2016'::text
            WHEN (to_char((t.purchase_date)::timestamp with time zone, 'YYYY'::text) <= '2019'::text) THEN '2019'::text
            ELSE '2020'::text
        END) AS year_category,
    t.in_monitoring_list,
    t.pre_monitoring_verified,
    t.inspection,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM ((((public.cmdb_device_management t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.device_type)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.production_attributes)::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.dict_code)::text = (t.data_center)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.operation_status)::text))))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_device_management OWNER TO postgres;

--
-- TOC entry 5354 (class 0 OID 0)
-- Dependencies: 287
-- Name: VIEW v_cmdb_device_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_device_management IS '网络设备视图';


--
-- TOC entry 267 (class 1259 OID 17446)
-- Name: v_cmdb_discovery_tasks; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_discovery_tasks AS
 SELECT t.id,
    t.task_name,
    t.task_type,
    t.description,
    t.ip_range_start,
    t.ip_range_end,
    t.ip_cidr,
    t.ip_range_type,
    t.scan_ports,
    t.schedule_type,
    t.schedule_value,
    t.last_run_time,
    t.status,
    t.created_at,
    t.created_by,
    t.updated_at,
    t.updated_by,
    t.run_duration_seconds,
    t.del_flag,
    ( SELECT count(*) AS count
           FROM public.cmdb_discovery_results r
          WHERE ((r.task_id = t.id) AND (r.del_flag = '0'::bpchar))) AS result_count
   FROM public.cmdb_discovery_tasks t
  WHERE (t.del_flag = '0'::bpchar);


ALTER VIEW public.v_cmdb_discovery_tasks OWNER TO postgres;

--
-- TOC entry 5356 (class 0 OID 0)
-- Dependencies: 267
-- Name: VIEW v_cmdb_discovery_tasks; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_discovery_tasks IS '自动发现任务视图';


--
-- TOC entry 288 (class 1259 OID 17998)
-- Name: v_cmdb_global_search; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_global_search AS
 WITH all_resources AS (
         SELECT cmdb_device_management.management_ip,
            '网络设备登记'::text AS menu_name,
            '/cmdb_device_management/'::text AS goto_router,
            cmdb_device_management.function_purpose
           FROM public.cmdb_device_management
          WHERE ((cmdb_device_management.del_flag)::text = '0'::text)
        UNION ALL
         SELECT cmdb_server_management.management_ip,
            '实体服务器设备登记'::text AS menu_name,
            '/cmdb_server_management/'::text AS goto_router,
            cmdb_server_management.function_purpose
           FROM public.cmdb_server_management
          WHERE ((cmdb_server_management.del_flag)::text = '0'::text)
        UNION ALL
         SELECT cmdb_application_system_info.management_ip,
            '应用系统信息登记表'::text AS menu_name,
            '/cmdb_application_system_info/'::text AS goto_router,
            cmdb_application_system_info.function_purpose
           FROM public.cmdb_application_system_info
          WHERE ((cmdb_application_system_info.del_flag)::text = '0'::text)
        UNION ALL
         SELECT cmdb_vm_registry.management_ip,
            '虚拟机登记表'::text AS menu_name,
            '/cmdb_vm_registry/'::text AS goto_router,
            cmdb_vm_registry.function_purpose
           FROM public.cmdb_vm_registry
          WHERE ((cmdb_vm_registry.del_flag)::text = '0'::text)
        UNION ALL
         SELECT ops_change_management.change_id AS management_ip,
            '变更管理'::text AS menu_name,
            '/ops_change_management/'::text AS goto_router,
            ops_change_management.title AS function_purpose
           FROM public.ops_change_management
          WHERE (ops_change_management.del_flag = '0'::bpchar)
        )
 SELECT all_resources.management_ip,
    all_resources.menu_name,
    all_resources.goto_router,
    all_resources.function_purpose
   FROM all_resources;


ALTER VIEW public.v_cmdb_global_search OWNER TO postgres;

--
-- TOC entry 5358 (class 0 OID 0)
-- Dependencies: 288
-- Name: VIEW v_cmdb_global_search; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_global_search IS '全局搜索视图';


--
-- TOC entry 303 (class 1259 OID 18413)
-- Name: v_cmdb_host_ip_update_logs; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_host_ip_update_logs AS
 SELECT l.id,
    l.vm_id,
    l.management_ip,
    v.hostname,
    v.function_purpose,
    v.admin1,
    l.old_host_ip,
    l.new_host_ip,
        CASE
            WHEN ((l.old_host_ip IS NULL) OR ((l.old_host_ip)::text = ''::text)) THEN '新增'::text
            WHEN ((l.new_host_ip IS NULL) OR ((l.new_host_ip)::text = ''::text)) THEN '清空'::text
            WHEN ((l.old_host_ip)::text <> (l.new_host_ip)::text) THEN '更新'::text
            ELSE '无变化'::text
        END AS change_type,
    l.update_source,
        CASE l.update_source
            WHEN 'zabbix_api'::text THEN 'Zabbix API'::character varying
            WHEN 'manual'::text THEN '手动更新'::character varying
            WHEN 'batch_update'::text THEN '批量更新'::character varying
            ELSE l.update_source
        END AS update_source_display,
    l.update_method,
        CASE l.update_method
            WHEN 'realtime'::text THEN '实时查询'::character varying
            WHEN 'scheduled'::text THEN '定时任务'::character varying
            WHEN 'manual'::text THEN '手动触发'::character varying
            ELSE l.update_method
        END AS update_method_display,
    l.success,
        CASE l.success
            WHEN true THEN '成功'::text
            WHEN false THEN '失败'::text
            ELSE '未知'::text
        END AS success_display,
    l.error_message,
    l.response_data,
    l.execution_time_ms,
    to_char(l.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    l.created_by
   FROM (public.cmdb_host_ip_update_logs l
     LEFT JOIN public.cmdb_vm_registry v ON ((l.vm_id = v.id)))
  WHERE (((v.del_flag)::text = '0'::text) OR (v.del_flag IS NULL))
  ORDER BY l.created_at DESC;


ALTER VIEW public.v_cmdb_host_ip_update_logs OWNER TO postgres;

--
-- TOC entry 5360 (class 0 OID 0)
-- Dependencies: 303
-- Name: VIEW v_cmdb_host_ip_update_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_host_ip_update_logs IS '虚拟机宿主机IP更新日志视图：包含虚拟机基本信息和更新状态的中文显示';


--
-- TOC entry 308 (class 1259 OID 18461)
-- Name: v_cmdb_host_scan_results; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_host_scan_results AS
 SELECT t.management_ip,
        CASE
            WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
            ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
        END AS admin1,
    COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) AS admin2,
    COALESCE(t.designated_admin, ''::character varying) AS designated_admin,
    COALESCE(t2.data_center, t3.data_center, t4.data_center) AS datacenter,
        CASE
            WHEN ((
            CASE
                WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
                ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
            END <> ''::text) OR (COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) <> ''::text) OR ((t.designated_admin)::text <> ''::text)) THEN '有人管'::text
            ELSE '无人管'::text
        END AS management_status,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.cmdb_vm_registry vm
              WHERE (((vm.management_ip)::text = (t.management_ip)::text) AND ((vm.del_flag)::text = '0'::text)))) THEN '是'::character varying
            ELSE '否'::character varying
        END AS is_virtual_machine,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS online_status,
    t.cmdb_registration_status,
    t.remarks,
    t.scan_date,
    t.id,
    COALESCE(t2.function_purpose, t3.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM ((((public.cmdb_host_scan_results t
     LEFT JOIN ( SELECT t2_1.management_ip,
            max((t2_1.admin1)::text) AS admin1,
            max((t2_1.admin2)::text) AS admin2,
            max((t2_1.data_center)::text) AS data_center,
            max((t2_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_device_management t2_1
          GROUP BY t2_1.management_ip) t2 ON (((t2.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t3_1.management_ip,
            max((t3_1.admin1)::text) AS admin1,
            max((t3_1.admin2)::text) AS admin2,
            max((t3_1.data_center)::text) AS data_center,
            max((t3_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_server_management t3_1
          GROUP BY t3_1.management_ip) t3 ON (((t3.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t4_1.management_ip,
            max(t4_1.server_admin1) AS admin1,
            max(t4_1.server_admin2) AS admin2,
            max(t4_1.data_center) AS data_center
           FROM public.v_cmdb_application_system_info t4_1
          GROUP BY t4_1.management_ip) t4 ON (((t4.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
     LEFT JOIN ( SELECT t5_1.management_ip,
            max((t5_1.function_purpose)::text) AS function_purpose
           FROM public.v_cmdb_vm_registry t5_1
          GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text))))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_host_scan_results OWNER TO postgres;

--
-- TOC entry 243 (class 1259 OID 16814)
-- Name: v_cmdb_monitored_ip_list; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_monitored_ip_list AS
 SELECT t.id,
    t.ip_address,
    to_char(t.last_checked, 'yyyy-mm-dd hh24:mi:ss'::text) AS last_checked,
    t.status,
    t.description,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM public.cmdb_monitored_ip_list t
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_monitored_ip_list OWNER TO postgres;

--
-- TOC entry 5363 (class 0 OID 0)
-- Dependencies: 243
-- Name: VIEW v_cmdb_monitored_ip_list; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_monitored_ip_list IS '监控ip视图';


--
-- TOC entry 244 (class 1259 OID 16818)
-- Name: v_cmdb_system_admin_responsibility; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_system_admin_responsibility AS
 SELECT t.id,
    t.external_system_id,
    t.system_abbreviation,
    t.main_admin,
    t.backup_admin,
    COALESCE(t2.dict_name, t.production_attribute) AS production_attribute,
    t.system_provider,
    t.system_function_summary,
    t.business_department,
    t.system_form,
    t.cs_client_name,
    t.bs_url,
    t.ip_port,
    t.monitoring_system_name,
    t.major_milestones,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (public.cmdb_system_admin_responsibility t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.production_attribute)::text))))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_system_admin_responsibility OWNER TO postgres;

--
-- TOC entry 5365 (class 0 OID 0)
-- Dependencies: 244
-- Name: VIEW v_cmdb_system_admin_responsibility; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_system_admin_responsibility IS '系统管理员责任表（外部）视图';


--
-- TOC entry 307 (class 1259 OID 18456)
-- Name: v_cmdb_system_admin_responsibility_company; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_system_admin_responsibility_company AS
 SELECT t.id,
    t.self_build_system_id,
    t.system_abbreviation,
    t.main_admin,
    t.backup_admin,
    t.business_department,
    COALESCE(t7.dict_name, t.system_attribute) AS system_attribute,
    t.go_live_date,
    t.decommission_date,
    t.major_milestones,
    t.industry_name,
    t.monitoring_system_name,
    t.system_function_summary,
    t.system_form,
    t.cs_client_name,
    t.bs_url,
    t.ip_port,
    t.business_line,
    t.system_category,
    COALESCE(t2.dict_name, t.system_level) AS system_level,
    t.has_backup_strategy,
    t4.server_count,
    t.remarks,
        CASE
            WHEN (t4.server_count = 0) THEN '0.00%'::text
            ELSE (round((((t4.is_monitored_qty)::numeric / (t4.server_count)::numeric) * (100)::numeric), 2) || '%'::text)
        END AS monitoring_coverage,
    t.digital_classification,
    t.jrt_0059_backup_standard,
    COALESCE(t5.dict_name, t.xinchuang_category_major) AS xinchuang_category_major,
    COALESCE(t3.dict_name, t.xinchuang_category_minor) AS xinchuang_category_minor,
    t.software_copyright_name,
    t.is_reported_to_external,
    COALESCE(t6.dict_name, t.construction_method) AS construction_method,
    COALESCE(t8.dict_name, t.technical_route) AS technical_route,
    t4.centos7_count,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by,
    t.operation_status,
    t.xinchuang_status,
    t.security_level,
    t.general_function_domains,
    t.futures_function_domains
   FROM (((((((public.cmdb_system_admin_responsibility_company t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.system_level)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.xinchuang_category_minor)::text))))
     LEFT JOIN public.cmdb_data_dictionary t5 ON ((((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.xinchuang_category_major)::text))))
     LEFT JOIN public.cmdb_data_dictionary t6 ON ((((t6.del_flag)::text = '0'::text) AND ((t6.dict_code)::text = (t.construction_method)::text))))
     LEFT JOIN public.cmdb_data_dictionary t7 ON ((((t7.del_flag)::text = '0'::text) AND ((t7.dict_code)::text = (t.system_attribute)::text))))
     LEFT JOIN public.cmdb_data_dictionary t8 ON ((((t8.del_flag)::text = '0'::text) AND ((t8.dict_code)::text = (t.technical_route)::text))))
     LEFT JOIN ( SELECT t4_1.business_system_name,
            count(1) AS server_count,
            sum(
                CASE
                    WHEN (t4_1.is_monitored = '是'::text) THEN 1
                    ELSE 0
                END) AS is_monitored_qty,
            sum(
                CASE
                    WHEN (t4_1.operating_system = 'CentOS7'::text) THEN 1
                    ELSE 0
                END) AS centos7_count
           FROM public.v_cmdb_application_system_info t4_1
          GROUP BY t4_1.business_system_name) t4 ON (((t4.business_system_name)::text = (t.system_abbreviation)::text)))
  WHERE ((t.del_flag)::text = '0'::text);


ALTER VIEW public.v_cmdb_system_admin_responsibility_company OWNER TO postgres;

--
-- TOC entry 298 (class 1259 OID 18300)
-- Name: v_cmdb_user_logs; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_cmdb_user_logs AS
 SELECT
        CASE
            WHEN ((t.url = '/api/update_ops_calendar_duty'::text) AND (t.body ? 'action'::text)) THEN (((((t.username)::text || '更新了'::text) || (t.body ->> 'date'::text)) || '的值班排班：'::text) || (t.body ->> 'duty_info'::text))
            WHEN ((t.url = ANY (ARRAY['/api/add_ops_change_management'::text, '/api/update_ops_change_management'::text, '/api/del_ops_change_management'::text])) AND (t.body ? 'action'::text)) THEN ((((((t.username)::text ||
            CASE
                WHEN ((t.body ->> 'action'::text) = '新增变更记录'::text) THEN '新增了变更记录'::text
                WHEN ((t.body ->> 'action'::text) = '更新变更记录'::text) THEN '更新了变更记录'::text
                WHEN ((t.body ->> 'action'::text) = '删除变更记录'::text) THEN '删除了变更记录'::text
                ELSE '操作了变更记录'::text
            END) || ' ['::text) || (t.body ->> 'change_id'::text)) || '] '::text) || COALESCE((t.body ->> 'title'::text), '未知标题'::text))
            WHEN ((t.url = ANY (ARRAY['/api/add_ops_event_management'::text, '/api/update_ops_event_management'::text, '/api/del_ops_event_management'::text])) AND (t.body ? 'action'::text)) THEN ((((((t.username)::text ||
            CASE
                WHEN ((t.body ->> 'action'::text) = '新增事件记录'::text) THEN '新增了事件记录'::text
                WHEN ((t.body ->> 'action'::text) = '更新事件记录'::text) THEN '更新了事件记录'::text
                WHEN ((t.body ->> 'action'::text) = '删除事件记录'::text) THEN '删除了事件记录'::text
                ELSE '操作了事件记录'::text
            END) || ' ['::text) || (t.body ->> 'event_id'::text)) || '] '::text) || COALESCE((t.body ->> 'title'::text), '未知标题'::text))
            WHEN ((t.url = ANY (ARRAY['/api/add_cmdb_device_management'::text, '/api/update_cmdb_device_management'::text, '/api/del_cmdb_device_management'::text, '/api/add_cmdb_server_management'::text, '/api/update_cmdb_server_management'::text, '/api/del_cmdb_server_management'::text, '/api/add_cmdb_vm_registry'::text, '/api/update_cmdb_vm_registry'::text, '/api/del_cmdb_vm_registry'::text, '/api/add_cmdb_application_system_info'::text, '/api/update_cmdb_application_system_info'::text, '/api/del_cmdb_application_system_info'::text])) AND (t.body ? 'action'::text)) THEN (((((((t.username)::text ||
            CASE
                WHEN ((t.body ->> 'action'::text) = '新增网络设备管理'::text) THEN '新增了网络设备'::text
                WHEN ((t.body ->> 'action'::text) = '更新网络设备管理'::text) THEN '更新了网络设备'::text
                WHEN ((t.body ->> 'action'::text) = '删除网络设备管理'::text) THEN '删除了网络设备'::text
                WHEN ((t.body ->> 'action'::text) = '新增实体服务器管理'::text) THEN '新增了实体服务器'::text
                WHEN ((t.body ->> 'action'::text) = '更新实体服务器管理'::text) THEN '更新了实体服务器'::text
                WHEN ((t.body ->> 'action'::text) = '删除实体服务器管理'::text) THEN '删除了实体服务器'::text
                WHEN ((t.body ->> 'action'::text) = '新增虚拟机登记'::text) THEN '新增了虚拟机'::text
                WHEN ((t.body ->> 'action'::text) = '更新虚拟机登记'::text) THEN '更新了虚拟机'::text
                WHEN ((t.body ->> 'action'::text) = '删除虚拟机登记'::text) THEN '删除了虚拟机'::text
                WHEN ((t.body ->> 'action'::text) = '新增应用系统信息'::text) THEN '新增了应用系统'::text
                WHEN ((t.body ->> 'action'::text) = '更新应用系统信息'::text) THEN '更新了应用系统'::text
                WHEN ((t.body ->> 'action'::text) = '删除应用系统信息'::text) THEN '删除了应用系统'::text
                ELSE '操作了IT资产'::text
            END) || ' ['::text) || (t.body ->> 'management_ip'::text)) || '] '::text) ||
            CASE
                WHEN ((t.body ? 'hostname'::text) AND ((t.body ->> 'hostname'::text) IS NOT NULL) AND ((t.body ->> 'hostname'::text) <> ''::text)) THEN (t.body ->> 'hostname'::text)
                WHEN ((t.body ? 'server_admin1'::text) AND ((t.body ->> 'server_admin1'::text) IS NOT NULL) AND ((t.body ->> 'server_admin1'::text) <> ''::text)) THEN (t.body ->> 'server_admin1'::text)
                ELSE '主机名未设置'::text
            END) ||
            CASE
                WHEN ((t.body ? 'function_purpose'::text) AND ((t.body ->> 'function_purpose'::text) IS NOT NULL) AND ((t.body ->> 'function_purpose'::text) <> ''::text)) THEN (' - '::text || (t.body ->> 'function_purpose'::text))
                WHEN ((t.body ? 'business_system_name'::text) AND ((t.body ->> 'business_system_name'::text) IS NOT NULL) AND ((t.body ->> 'business_system_name'::text) <> ''::text)) THEN (' - '::text || (t.body ->> 'business_system_name'::text))
                ELSE ''::text
            END)
            ELSE
            CASE
                WHEN (t2.table_comment IS NOT NULL) THEN ((((t.username)::text ||
                CASE
                    WHEN ((t.operation_type)::text = 'add'::text) THEN '创建了新的'::text
                    WHEN ((t.operation_type)::text = 'update'::text) THEN '更新了'::text
                    WHEN ((t.operation_type)::text = 'delete'::text) THEN '删除了'::text
                    ELSE '操作了'::text
                END) || t2.table_comment) || '数据'::text)
                WHEN (t.url ~~ '%login%'::text) THEN ((t.username)::text || '进行了登录操作'::text)
                WHEN (t.url ~~ '%logout%'::text) THEN ((t.username)::text || '进行了登出操作'::text)
                WHEN (t.url ~~ '%upload%'::text) THEN ((t.username)::text || '上传了文件'::text)
                WHEN (t.url ~~ '%download%'::text) THEN ((t.username)::text || '下载了文件'::text)
                WHEN (t.url ~~ '%remove%'::text) THEN ((t.username)::text || '删除了文件'::text)
                WHEN (t.url ~~ '%template%'::text) THEN ((t.username)::text || '操作了模板'::text)
                WHEN (t.url ~~ '%permission%'::text) THEN ((t.username)::text || '操作了权限'::text)
                WHEN (t.url ~~ '%discovery%'::text) THEN ((t.username)::text || '操作了自动发现'::text)
                ELSE NULL::text
            END
        END AS activity_info,
    to_char(t."timestamp", 'yyyy-mm-dd hh24:mi:ss'::text) AS activity_time,
        CASE
            WHEN ((t.operation_type)::text = 'add'::text) THEN 'primary'::text
            WHEN ((t.operation_type)::text = 'update'::text) THEN 'success'::text
            WHEN ((t.operation_type)::text = 'delete'::text) THEN 'warning'::text
            ELSE 'primary'::text
        END AS activity_type
   FROM (public.cmdb_user_logs t
     LEFT JOIN ( SELECT n.nspname,
            c.relname AS table_name,
            pgd.description AS table_comment
           FROM ((pg_class c
             JOIN pg_namespace n ON ((n.oid = c.relnamespace)))
             JOIN pg_description pgd ON (((pgd.objoid = c.oid) AND (pgd.objsubid = 0))))
          WHERE ((c.relkind = 'r'::"char") AND (n.nspname = 'public'::name))) t2 ON ((t2.table_name = replace(regexp_replace(t.url, '/api/(del_|update_|add_)'::text, ''::text, 'g'::text), '_password'::text, ''::text))))
  WHERE (((t.url = '/api/update_ops_calendar_duty'::text) AND (t.body ? 'action'::text)) OR ((t.url = ANY (ARRAY['/api/add_ops_change_management'::text, '/api/update_ops_change_management'::text, '/api/del_ops_change_management'::text])) AND (t.body ? 'action'::text)) OR ((t.url = ANY (ARRAY['/api/add_ops_event_management'::text, '/api/update_ops_event_management'::text, '/api/del_ops_event_management'::text])) AND (t.body ? 'action'::text)) OR ((t.url = ANY (ARRAY['/api/add_cmdb_device_management'::text, '/api/update_cmdb_device_management'::text, '/api/del_cmdb_device_management'::text, '/api/add_cmdb_server_management'::text, '/api/update_cmdb_server_management'::text, '/api/del_cmdb_server_management'::text, '/api/add_cmdb_vm_registry'::text, '/api/update_cmdb_vm_registry'::text, '/api/del_cmdb_vm_registry'::text, '/api/add_cmdb_application_system_info'::text, '/api/update_cmdb_application_system_info'::text, '/api/del_cmdb_application_system_info'::text])) AND (t.body ? 'action'::text)) OR ((t2.table_comment IS NOT NULL) AND (t.url <> ALL (ARRAY['/api/add_ops_change_management'::text, '/api/update_ops_change_management'::text, '/api/del_ops_change_management'::text])) AND (t.url <> ALL (ARRAY['/api/add_ops_event_management'::text, '/api/update_ops_event_management'::text, '/api/del_ops_event_management'::text])) AND (t.url <> ALL (ARRAY['/api/add_cmdb_device_management'::text, '/api/update_cmdb_device_management'::text, '/api/del_cmdb_device_management'::text, '/api/add_cmdb_server_management'::text, '/api/update_cmdb_server_management'::text, '/api/del_cmdb_server_management'::text, '/api/add_cmdb_vm_registry'::text, '/api/update_cmdb_vm_registry'::text, '/api/del_cmdb_vm_registry'::text, '/api/add_cmdb_application_system_info'::text, '/api/update_cmdb_application_system_info'::text, '/api/del_cmdb_application_system_info'::text])) AND (t.url <> '/api/update_ops_calendar_duty'::text)) OR (t.url ~~ '%login%'::text) OR (t.url ~~ '%logout%'::text) OR (t.url ~~ '%upload%'::text) OR (t.url ~~ '%download%'::text) OR (t.url ~~ '%remove%'::text) OR (t.url ~~ '%template%'::text) OR (t.url ~~ '%permission%'::text) OR (t.url ~~ '%discovery%'::text))
  ORDER BY t."timestamp" DESC, t.id
 LIMIT 50;


ALTER VIEW public.v_cmdb_user_logs OWNER TO postgres;

--
-- TOC entry 5368 (class 0 OID 0)
-- Dependencies: 298
-- Name: VIEW v_cmdb_user_logs; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_cmdb_user_logs IS '用户操作日志视图，支持交易日历值班排班操作、变更管理操作、事件管理操作和IT资产管理增删改操作的特殊显示';


--
-- TOC entry 284 (class 1259 OID 17858)
-- Name: v_ops_calendar; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_ops_calendar AS
 SELECT c.day,
    c.dat,
    c.wrk,
    c.tra,
    c.sun,
    c.mon,
    c.tue,
    c.wed,
    c.thu,
    c.fri,
    c.sat,
    c.str,
    c.tal,
    c.spr,
    c.main_duty_user,
    c.deputy_duty_user,
    c.duty_manager,
    c.simulation_user,
    c.inspection_user,
        CASE
            WHEN ((c.main_duty_user IS NOT NULL) AND ((c.main_duty_user)::text <> ''::text)) THEN ( SELECT string_agg((COALESCE(u.real_name, (usernames.username)::character varying))::text, ','::text) AS string_agg
               FROM (( SELECT unnest(string_to_array((c.main_duty_user)::text, ','::text)) AS username) usernames
                 LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM usernames.username)) AND ((u.del_flag)::text = '0'::text)))))
            ELSE NULL::text
        END AS main_duty_name,
        CASE
            WHEN ((c.deputy_duty_user IS NOT NULL) AND ((c.deputy_duty_user)::text <> ''::text)) THEN ( SELECT string_agg((COALESCE(u.real_name, (usernames.username)::character varying))::text, ','::text) AS string_agg
               FROM (( SELECT unnest(string_to_array((c.deputy_duty_user)::text, ','::text)) AS username) usernames
                 LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM usernames.username)) AND ((u.del_flag)::text = '0'::text)))))
            ELSE NULL::text
        END AS deputy_duty_name,
        CASE
            WHEN ((c.duty_manager IS NOT NULL) AND ((c.duty_manager)::text <> ''::text)) THEN ( SELECT string_agg((COALESCE(u.real_name, (usernames.username)::character varying))::text, ','::text) AS string_agg
               FROM (( SELECT unnest(string_to_array((c.duty_manager)::text, ','::text)) AS username) usernames
                 LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM usernames.username)) AND ((u.del_flag)::text = '0'::text)))))
            ELSE NULL::text
        END AS duty_manager_name,
        CASE
            WHEN ((c.simulation_user IS NOT NULL) AND ((c.simulation_user)::text <> ''::text)) THEN ( SELECT string_agg((COALESCE(u.real_name, (usernames.username)::character varying))::text, ','::text) AS string_agg
               FROM (( SELECT unnest(string_to_array((c.simulation_user)::text, ','::text)) AS username) usernames
                 LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM usernames.username)) AND ((u.del_flag)::text = '0'::text)))))
            ELSE NULL::text
        END AS simulation_name,
        CASE
            WHEN ((c.inspection_user IS NOT NULL) AND ((c.inspection_user)::text <> ''::text)) THEN ( SELECT string_agg((COALESCE(u.real_name, (usernames.username)::character varying))::text, ','::text) AS string_agg
               FROM (( SELECT unnest(string_to_array((c.inspection_user)::text, ','::text)) AS username) usernames
                 LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM usernames.username)) AND ((u.del_flag)::text = '0'::text)))))
            ELSE NULL::text
        END AS inspection_name,
    COALESCE(( SELECT string_agg((
                CASE
                    WHEN (length((cm.title)::text) > 10) THEN (("left"((cm.title)::text, 10) || '...'::text))::character varying
                    ELSE cm.title
                END)::text, '; '::text) AS string_agg
           FROM public.ops_change_management cm
          WHERE ((to_char((cm.planned_change_time)::timestamp with time zone, 'YYYYMMDD'::text) = (c.day)::text) AND (cm.del_flag = '0'::bpchar))), ''::text) AS change_summary,
    COALESCE(( SELECT count(*) AS count
           FROM public.ops_change_management cm
          WHERE ((to_char((cm.planned_change_time)::timestamp with time zone, 'YYYYMMDD'::text) = (c.day)::text) AND (cm.del_flag = '0'::bpchar))), (0)::bigint) AS change_count,
    c.created_at,
    c.updated_at,
    c.created_by,
    c.updated_by
   FROM public.ops_calendar c;


ALTER VIEW public.v_ops_calendar OWNER TO postgres;

--
-- TOC entry 5370 (class 0 OID 0)
-- Dependencies: 284
-- Name: VIEW v_ops_calendar; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_ops_calendar IS '交易日历视图，包含变更管理信息，支持多人值班';


--
-- TOC entry 280 (class 1259 OID 17745)
-- Name: v_ops_calendar_duty_permissions; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_ops_calendar_duty_permissions AS
 SELECT p.id,
    p.user_id,
    u.username,
    u.real_name,
    p.permission_type,
    p.created_at,
    p.created_by,
    p.updated_at,
    p.updated_by
   FROM (public.ops_calendar_duty_permissions p
     LEFT JOIN public.cmdb_users u ON (((p.user_id = u.id) AND ((u.del_flag)::text = '0'::text))))
  WHERE ((p.del_flag)::text = '0'::text);


ALTER VIEW public.v_ops_calendar_duty_permissions OWNER TO postgres;

--
-- TOC entry 5372 (class 0 OID 0)
-- Dependencies: 280
-- Name: VIEW v_ops_calendar_duty_permissions; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_ops_calendar_duty_permissions IS '值班排班权限视图';


--
-- TOC entry 276 (class 1259 OID 17700)
-- Name: v_ops_change_management; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_ops_change_management AS
 SELECT t.id,
    t.change_id,
    t.title,
    t.system,
    t.change_level,
    COALESCE(t2.dict_name, t.change_level) AS change_level_name_display,
    t.planned_change_time,
    ((to_char((t.planned_change_time)::timestamp with time zone, 'YYYY-MM-DD'::text) || ' '::text) ||
        CASE EXTRACT(dow FROM t.planned_change_time)
            WHEN 0 THEN '星期日'::text
            WHEN 1 THEN '星期一'::text
            WHEN 2 THEN '星期二'::text
            WHEN 3 THEN '星期三'::text
            WHEN 4 THEN '星期四'::text
            WHEN 5 THEN '星期五'::text
            WHEN 6 THEN '星期六'::text
            ELSE NULL::text
        END) AS formatted_change_time,
    t.requester,
    u1.real_name AS requester_name,
    t.implementers,
    ( SELECT string_agg((u.real_name)::text, ', '::text) AS string_agg
           FROM (( SELECT unnest(string_to_array(t.implementers, ','::text)) AS username) usernames
             LEFT JOIN public.cmdb_users u ON ((((u.username)::text = usernames.username) AND ((u.del_flag)::text = '0'::text))))) AS implementers_name,
    t.description,
    t.oa_process,
    t.oa_process_file,
    t.signed_archive,
    t.signed_archive_file,
    t.operation_sheet,
    t.supplementary_material,
    t.created_at,
    t.created_by,
    t.updated_at,
    t.updated_by
   FROM ((public.ops_change_management t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.dict_type)::text = 'P'::text) AND ((t2.dict_code)::text = (t.change_level)::text) AND ((t2.del_flag)::text = '0'::text))))
     LEFT JOIN public.cmdb_users u1 ON ((((u1.username)::text = (t.requester)::text) AND ((u1.del_flag)::text = '0'::text))))
  WHERE (t.del_flag = '0'::bpchar);


ALTER VIEW public.v_ops_change_management OWNER TO postgres;

--
-- TOC entry 5374 (class 0 OID 0)
-- Dependencies: 276
-- Name: VIEW v_ops_change_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_ops_change_management IS '变更管理视图，包含补充资料字段';


--
-- TOC entry 285 (class 1259 OID 17890)
-- Name: v_ops_event_management; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_ops_event_management AS
 SELECT t.id,
    t.event_id,
    t.title,
    t.event_type,
    COALESCE(t2.dict_name, t.event_type) AS event_type_name_display,
    t.status,
    COALESCE(t3.dict_name, t.status) AS status_name_display,
    t.priority,
    COALESCE(t4.dict_name, t.priority) AS priority_name_display,
    t.system,
    t.reporter,
    u1.real_name AS reporter_name,
    t.assignee,
    u2.real_name AS assignee_name,
        CASE
            WHEN ((t.assignee IS NOT NULL) AND ((t.assignee)::text <> ''::text)) THEN
            CASE
                WHEN (POSITION((','::text) IN (t.assignee)) > 0) THEN (( SELECT string_agg((u.real_name)::text, ', '::text) AS string_agg
                   FROM (unnest(string_to_array((t.assignee)::text, ','::text)) assignee_username(assignee_username)
                     LEFT JOIN public.cmdb_users u ON ((((u.username)::text = TRIM(BOTH FROM assignee_username.assignee_username)) AND ((u.del_flag)::text = '0'::text))))
                  WHERE (u.real_name IS NOT NULL)))::character varying
                ELSE u2.real_name
            END
            ELSE NULL::character varying
        END AS assignees_name_display,
    t.report_time,
    to_char(t.report_time, 'YYYY-MM-DD HH24:MI:SS'::text) AS formatted_report_time,
    t.close_time,
    to_char(t.close_time, 'YYYY-MM-DD HH24:MI:SS'::text) AS formatted_close_time,
    t.description,
    t.process,
    t.solution,
    t.improvement_plan,
    t.consequence_cause_analysis,
    t.screenshot,
    t.screenshot_file,
    t.process_doc,
    t.process_doc_file,
    t.supplementary_material,
    t.created_at,
    t.created_by,
    t.updated_at,
    t.updated_by
   FROM (((((public.ops_event_management t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.dict_type)::text = 'T'::text) AND ((t2.dict_code)::text = (t.event_type)::text) AND ((t2.del_flag)::text = '0'::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.dict_type)::text = 'V'::text) AND ((t3.dict_code)::text = (t.status)::text) AND ((t3.del_flag)::text = '0'::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.dict_type)::text = 'U'::text) AND ((t4.dict_code)::text = (t.priority)::text) AND ((t4.del_flag)::text = '0'::text))))
     LEFT JOIN public.cmdb_users u1 ON ((((u1.username)::text = (t.reporter)::text) AND ((u1.del_flag)::text = '0'::text))))
     LEFT JOIN public.cmdb_users u2 ON ((((u2.username)::text = (t.assignee)::text) AND ((u2.del_flag)::text = '0'::text))))
  WHERE (t.del_flag = '0'::bpchar);


ALTER VIEW public.v_ops_event_management OWNER TO postgres;

--
-- TOC entry 5376 (class 0 OID 0)
-- Dependencies: 285
-- Name: VIEW v_ops_event_management; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON VIEW public.v_ops_event_management IS '事件管理视图，关联显示字典名称和用户真实姓名，包含事件导致的后果、发生原因和事件性质判断字段';


--
-- TOC entry 268 (class 1259 OID 17458)
-- Name: v_report_filter_favorites; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_report_filter_favorites AS
 SELECT report_filter_favorites.id,
    report_filter_favorites.name,
    report_filter_favorites.data_centers,
    report_filter_favorites.operation_statuses,
    report_filter_favorites.time_periods,
    report_filter_favorites.innovative_tech_flags,
    report_filter_favorites.username,
    report_filter_favorites.created_at,
    report_filter_favorites.updated_at,
    report_filter_favorites.del_flag
   FROM public.report_filter_favorites
  WHERE (report_filter_favorites.del_flag = '0'::bpchar);


ALTER VIEW public.v_report_filter_favorites OWNER TO postgres;

--
-- TOC entry 4380 (class 2604 OID 18337)
-- Name: cmdb_discovery_cleanup id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_cleanup ALTER COLUMN id SET DEFAULT nextval('public.cmdb_discovery_cleanup_id_seq'::regclass);


--
-- TOC entry 4310 (class 2604 OID 17557)
-- Name: cmdb_function_domain_types id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_function_domain_types ALTER COLUMN id SET DEFAULT nextval('public.cmdb_function_domain_types_id_seq'::regclass);


--
-- TOC entry 4200 (class 2604 OID 16491)
-- Name: cmdb_host_scan_results id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_host_scan_results ALTER COLUMN id SET DEFAULT nextval('public.cmdb_host_scan_results_id_seq'::regclass);


--
-- TOC entry 4244 (class 2604 OID 16776)
-- Name: cmdb_issue_collection id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_issue_collection ALTER COLUMN id SET DEFAULT nextval('public.cmdb_issue_collection_id_seq'::regclass);


--
-- TOC entry 4294 (class 2604 OID 17311)
-- Name: cmdb_pages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_pages ALTER COLUMN id SET DEFAULT nextval('public.cmdb_pages_id_seq'::regclass);


--
-- TOC entry 4290 (class 2604 OID 17280)
-- Name: cmdb_schedule_task_history id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_history ALTER COLUMN id SET DEFAULT nextval('public.cmdb_schedule_task_history_id_seq'::regclass);


--
-- TOC entry 4285 (class 2604 OID 17257)
-- Name: cmdb_schedule_task_items id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_items ALTER COLUMN id SET DEFAULT nextval('public.cmdb_schedule_task_items_id_seq'::regclass);


--
-- TOC entry 4279 (class 2604 OID 17243)
-- Name: cmdb_schedule_tasks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_tasks ALTER COLUMN id SET DEFAULT nextval('public.cmdb_schedule_tasks_id_seq'::regclass);


--
-- TOC entry 4307 (class 2604 OID 17372)
-- Name: cmdb_system_config id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_system_config ALTER COLUMN id SET DEFAULT nextval('public.cmdb_system_config_id_seq'::regclass);


--
-- TOC entry 4263 (class 2604 OID 17192)
-- Name: cmdb_user_login_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_login_logs ALTER COLUMN id SET DEFAULT nextval('public.cmdb_user_login_logs_id_seq'::regclass);


--
-- TOC entry 4233 (class 2604 OID 16552)
-- Name: cmdb_user_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_logs ALTER COLUMN id SET DEFAULT nextval('public.cmdb_user_logs_id_seq'::regclass);


--
-- TOC entry 4301 (class 2604 OID 17326)
-- Name: cmdb_user_page_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_page_permissions ALTER COLUMN id SET DEFAULT nextval('public.cmdb_user_page_permissions_id_seq'::regclass);


--
-- TOC entry 4249 (class 2604 OID 16786)
-- Name: cmdb_users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_users ALTER COLUMN id SET DEFAULT nextval('public.cmdb_users_id_seq'::regclass);


--
-- TOC entry 4347 (class 2604 OID 18036)
-- Name: msg_push_config id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_config ALTER COLUMN id SET DEFAULT nextval('public.msg_push_config_id_seq'::regclass);


--
-- TOC entry 4374 (class 2604 OID 18147)
-- Name: msg_push_history id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_history ALTER COLUMN id SET DEFAULT nextval('public.msg_push_history_id_seq'::regclass);


--
-- TOC entry 4364 (class 2604 OID 18115)
-- Name: msg_push_tasks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_tasks ALTER COLUMN id SET DEFAULT nextval('public.msg_push_tasks_id_seq'::regclass);


--
-- TOC entry 4356 (class 2604 OID 18097)
-- Name: msg_push_templates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_templates ALTER COLUMN id SET DEFAULT nextval('public.msg_push_templates_id_seq'::regclass);


--
-- TOC entry 4332 (class 2604 OID 17732)
-- Name: ops_calendar_duty_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_calendar_duty_permissions ALTER COLUMN id SET DEFAULT nextval('public.ops_calendar_duty_permissions_id_seq'::regclass);


--
-- TOC entry 4317 (class 2604 OID 17612)
-- Name: ops_change_management id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_management ALTER COLUMN id SET DEFAULT nextval('public.ops_change_management_id_seq'::regclass);


--
-- TOC entry 4323 (class 2604 OID 17653)
-- Name: ops_change_templates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_templates ALTER COLUMN id SET DEFAULT nextval('public.ops_change_templates_id_seq'::regclass);


--
-- TOC entry 4338 (class 2604 OID 17794)
-- Name: ops_event_management id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_event_management ALTER COLUMN id SET DEFAULT nextval('public.ops_event_management_id_seq'::regclass);


--
-- TOC entry 4255 (class 2604 OID 17160)
-- Name: report_filter_favorites id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.report_filter_favorites ALTER COLUMN id SET DEFAULT nextval('public.report_filter_favorites_id_seq'::regclass);


--
-- TOC entry 4401 (class 2606 OID 16735)
-- Name: cmdb_application_system_info application_system_info_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_application_system_info
    ADD CONSTRAINT application_system_info_pkey PRIMARY KEY (id);


--
-- TOC entry 4406 (class 2606 OID 16739)
-- Name: cmdb_data_dictionary cmdb_data_dictionary_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_data_dictionary
    ADD CONSTRAINT cmdb_data_dictionary_pkey PRIMARY KEY (id);


--
-- TOC entry 4533 (class 2606 OID 18350)
-- Name: cmdb_discovery_cleanup cmdb_discovery_cleanup_config_key_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_cleanup
    ADD CONSTRAINT cmdb_discovery_cleanup_config_key_key UNIQUE (config_key);


--
-- TOC entry 4535 (class 2606 OID 18348)
-- Name: cmdb_discovery_cleanup cmdb_discovery_cleanup_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_cleanup
    ADD CONSTRAINT cmdb_discovery_cleanup_pkey PRIMARY KEY (id);


--
-- TOC entry 4452 (class 2606 OID 17230)
-- Name: cmdb_discovery_results cmdb_discovery_results_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_results
    ADD CONSTRAINT cmdb_discovery_results_pkey PRIMARY KEY (id);


--
-- TOC entry 4454 (class 2606 OID 17232)
-- Name: cmdb_discovery_results cmdb_discovery_results_task_ip_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_results
    ADD CONSTRAINT cmdb_discovery_results_task_ip_unique UNIQUE (task_id, ip_address);


--
-- TOC entry 4450 (class 2606 OID 17217)
-- Name: cmdb_discovery_tasks cmdb_discovery_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_discovery_tasks
    ADD CONSTRAINT cmdb_discovery_tasks_pkey PRIMARY KEY (id);


--
-- TOC entry 4482 (class 2606 OID 17565)
-- Name: cmdb_function_domain_types cmdb_function_domain_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_function_domain_types
    ADD CONSTRAINT cmdb_function_domain_types_pkey PRIMARY KEY (id);


--
-- TOC entry 4540 (class 2606 OID 18402)
-- Name: cmdb_host_ip_update_logs cmdb_host_ip_update_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_host_ip_update_logs
    ADD CONSTRAINT cmdb_host_ip_update_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 4412 (class 2606 OID 16744)
-- Name: cmdb_host_scan_results cmdb_host_scan_results_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_host_scan_results
    ADD CONSTRAINT cmdb_host_scan_results_pkey PRIMARY KEY (id);


--
-- TOC entry 4435 (class 2606 OID 16856)
-- Name: cmdb_issue_collection cmdb_issue_collection_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_issue_collection
    ADD CONSTRAINT cmdb_issue_collection_pkey PRIMARY KEY (id);


--
-- TOC entry 4414 (class 2606 OID 16747)
-- Name: cmdb_monitored_ip_list cmdb_monitored_ip_list_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_monitored_ip_list
    ADD CONSTRAINT cmdb_monitored_ip_list_pkey PRIMARY KEY (id);


--
-- TOC entry 4469 (class 2606 OID 17321)
-- Name: cmdb_pages cmdb_pages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_pages
    ADD CONSTRAINT cmdb_pages_pkey PRIMARY KEY (id);


--
-- TOC entry 4466 (class 2606 OID 17287)
-- Name: cmdb_schedule_task_history cmdb_schedule_task_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_history
    ADD CONSTRAINT cmdb_schedule_task_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4460 (class 2606 OID 17263)
-- Name: cmdb_schedule_task_items cmdb_schedule_task_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_items
    ADD CONSTRAINT cmdb_schedule_task_items_pkey PRIMARY KEY (id);


--
-- TOC entry 4456 (class 2606 OID 17252)
-- Name: cmdb_schedule_tasks cmdb_schedule_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_tasks
    ADD CONSTRAINT cmdb_schedule_tasks_pkey PRIMARY KEY (id);


--
-- TOC entry 4477 (class 2606 OID 17378)
-- Name: cmdb_system_config cmdb_system_config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_system_config
    ADD CONSTRAINT cmdb_system_config_pkey PRIMARY KEY (id);


--
-- TOC entry 4445 (class 2606 OID 17197)
-- Name: cmdb_user_login_logs cmdb_user_login_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_login_logs
    ADD CONSTRAINT cmdb_user_login_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 4426 (class 2606 OID 16755)
-- Name: cmdb_user_logs cmdb_user_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_logs
    ADD CONSTRAINT cmdb_user_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 4471 (class 2606 OID 17333)
-- Name: cmdb_user_page_permissions cmdb_user_page_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_page_permissions
    ADD CONSTRAINT cmdb_user_page_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 4437 (class 2606 OID 16860)
-- Name: cmdb_users cmdb_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_users
    ADD CONSTRAINT cmdb_users_pkey PRIMARY KEY (id);


--
-- TOC entry 4410 (class 2606 OID 16742)
-- Name: cmdb_device_management device_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_device_management
    ADD CONSTRAINT device_management_pkey PRIMARY KEY (id);


--
-- TOC entry 4516 (class 2606 OID 18048)
-- Name: msg_push_config msg_push_config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_config
    ADD CONSTRAINT msg_push_config_pkey PRIMARY KEY (id);


--
-- TOC entry 4531 (class 2606 OID 18156)
-- Name: msg_push_history msg_push_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_history
    ADD CONSTRAINT msg_push_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4526 (class 2606 OID 18128)
-- Name: msg_push_tasks msg_push_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_tasks
    ADD CONSTRAINT msg_push_tasks_pkey PRIMARY KEY (id);


--
-- TOC entry 4520 (class 2606 OID 18108)
-- Name: msg_push_templates msg_push_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_templates
    ADD CONSTRAINT msg_push_templates_pkey PRIMARY KEY (id);


--
-- TOC entry 4501 (class 2606 OID 17739)
-- Name: ops_calendar_duty_permissions ops_calendar_duty_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_calendar_duty_permissions
    ADD CONSTRAINT ops_calendar_duty_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 4491 (class 2606 OID 17621)
-- Name: ops_change_management ops_change_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_management
    ADD CONSTRAINT ops_change_management_pkey PRIMARY KEY (id);


--
-- TOC entry 4493 (class 2606 OID 17779)
-- Name: ops_change_management ops_change_management_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_management
    ADD CONSTRAINT ops_change_management_unique UNIQUE (change_id);


--
-- TOC entry 4497 (class 2606 OID 17660)
-- Name: ops_change_templates ops_change_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_templates
    ADD CONSTRAINT ops_change_templates_pkey PRIMARY KEY (id);


--
-- TOC entry 4512 (class 2606 OID 17806)
-- Name: ops_event_management ops_event_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_event_management
    ADD CONSTRAINT ops_event_management_pkey PRIMARY KEY (id);


--
-- TOC entry 4499 (class 2606 OID 17722)
-- Name: ops_calendar pk_calendar; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_calendar
    ADD CONSTRAINT pk_calendar PRIMARY KEY (day);


--
-- TOC entry 4443 (class 2606 OID 17170)
-- Name: report_filter_favorites report_filter_favorites_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.report_filter_favorites
    ADD CONSTRAINT report_filter_favorites_pkey PRIMARY KEY (id);


--
-- TOC entry 4422 (class 2606 OID 16750)
-- Name: cmdb_server_management server_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_server_management
    ADD CONSTRAINT server_management_pkey PRIMARY KEY (id);


--
-- TOC entry 4418 (class 2606 OID 18030)
-- Name: cmdb_monitored_ip_list uk_cmdb_monitored_ip_list_ip_address; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_monitored_ip_list
    ADD CONSTRAINT uk_cmdb_monitored_ip_list_ip_address UNIQUE (ip_address, del_flag);


--
-- TOC entry 4475 (class 2606 OID 17340)
-- Name: cmdb_user_page_permissions uq_cmdb_user_page_permissions_user_page; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_user_page_permissions
    ADD CONSTRAINT uq_cmdb_user_page_permissions_user_page UNIQUE (user_id, page_id);


--
-- TOC entry 4480 (class 2606 OID 17380)
-- Name: cmdb_system_config uq_config_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_system_config
    ADD CONSTRAINT uq_config_key UNIQUE (config_key);


--
-- TOC entry 4464 (class 2606 OID 17265)
-- Name: cmdb_schedule_task_items uq_schedule_discovery_task; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_items
    ADD CONSTRAINT uq_schedule_discovery_task UNIQUE (schedule_task_id, discovery_task_id);


--
-- TOC entry 4433 (class 2606 OID 16758)
-- Name: cmdb_vm_registry vm_registry_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_vm_registry
    ADD CONSTRAINT vm_registry_pkey PRIMARY KEY (id);


--
-- TOC entry 4402 (class 1259 OID 16733)
-- Name: cmdb_application_system_info_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_application_system_info_u ON public.cmdb_application_system_info USING btree (id);


--
-- TOC entry 4404 (class 1259 OID 16736)
-- Name: cmdb_data_dictionary_dict_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_data_dictionary_dict_code ON public.cmdb_data_dictionary USING btree (dict_code, del_flag);


--
-- TOC entry 4407 (class 1259 OID 16737)
-- Name: cmdb_data_dictionary_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_data_dictionary_u ON public.cmdb_data_dictionary USING btree (id);


--
-- TOC entry 4408 (class 1259 OID 16740)
-- Name: cmdb_device_management_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_device_management_u ON public.cmdb_device_management USING btree (id);


--
-- TOC entry 4415 (class 1259 OID 16745)
-- Name: cmdb_monitored_ip_list_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_monitored_ip_list_u ON public.cmdb_monitored_ip_list USING btree (id);


--
-- TOC entry 4419 (class 1259 OID 16748)
-- Name: cmdb_server_management_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_server_management_u ON public.cmdb_server_management USING btree (id);


--
-- TOC entry 4424 (class 1259 OID 16752)
-- Name: cmdb_system_admin_responsibility_company_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_system_admin_responsibility_company_u ON public.cmdb_system_admin_responsibility_company USING btree (id);


--
-- TOC entry 4546 (class 1259 OID 18440)
-- Name: cmdb_system_admin_responsibility_company_u_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_system_admin_responsibility_company_u_1 ON public.cmdb_system_admin_responsibility_company_20250731 USING btree (id);


--
-- TOC entry 4423 (class 1259 OID 16751)
-- Name: cmdb_system_admin_responsibility_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_system_admin_responsibility_u ON public.cmdb_system_admin_responsibility USING btree (id);


--
-- TOC entry 4427 (class 1259 OID 16753)
-- Name: cmdb_user_logs_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_user_logs_u ON public.cmdb_user_logs USING btree (id);


--
-- TOC entry 4438 (class 1259 OID 16857)
-- Name: cmdb_users_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_users_u ON public.cmdb_users USING btree (id);


--
-- TOC entry 4439 (class 1259 OID 16858)
-- Name: cmdb_users_username_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_users_username_key ON public.cmdb_users USING btree (username, del_flag);


--
-- TOC entry 4428 (class 1259 OID 16756)
-- Name: cmdb_vm_registry_u; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cmdb_vm_registry_u ON public.cmdb_vm_registry USING btree (id);


--
-- TOC entry 4403 (class 1259 OID 18014)
-- Name: idx_cmdb_application_system_info_monitoring_requirement; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_application_system_info_monitoring_requirement ON public.cmdb_application_system_info USING btree (monitoring_requirement);


--
-- TOC entry 4536 (class 1259 OID 18353)
-- Name: idx_cmdb_discovery_cleanup_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_discovery_cleanup_del_flag ON public.cmdb_discovery_cleanup USING btree (del_flag);


--
-- TOC entry 4537 (class 1259 OID 18352)
-- Name: idx_cmdb_discovery_cleanup_enabled; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_discovery_cleanup_enabled ON public.cmdb_discovery_cleanup USING btree (is_enabled);


--
-- TOC entry 4538 (class 1259 OID 18351)
-- Name: idx_cmdb_discovery_cleanup_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_discovery_cleanup_key ON public.cmdb_discovery_cleanup USING btree (config_key);


--
-- TOC entry 4541 (class 1259 OID 18410)
-- Name: idx_cmdb_host_ip_update_logs_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_host_ip_update_logs_created_at ON public.cmdb_host_ip_update_logs USING btree (created_at);


--
-- TOC entry 4542 (class 1259 OID 18409)
-- Name: idx_cmdb_host_ip_update_logs_management_ip; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_host_ip_update_logs_management_ip ON public.cmdb_host_ip_update_logs USING btree (management_ip);


--
-- TOC entry 4543 (class 1259 OID 18412)
-- Name: idx_cmdb_host_ip_update_logs_success; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_host_ip_update_logs_success ON public.cmdb_host_ip_update_logs USING btree (success);


--
-- TOC entry 4544 (class 1259 OID 18411)
-- Name: idx_cmdb_host_ip_update_logs_update_source; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_host_ip_update_logs_update_source ON public.cmdb_host_ip_update_logs USING btree (update_source);


--
-- TOC entry 4545 (class 1259 OID 18408)
-- Name: idx_cmdb_host_ip_update_logs_vm_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_host_ip_update_logs_vm_id ON public.cmdb_host_ip_update_logs USING btree (vm_id);


--
-- TOC entry 4416 (class 1259 OID 18031)
-- Name: idx_cmdb_monitored_ip_list_ip_address_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_cmdb_monitored_ip_list_ip_address_unique ON public.cmdb_monitored_ip_list USING btree (ip_address) WHERE ((del_flag)::text = '0'::text);


--
-- TOC entry 4420 (class 1259 OID 17980)
-- Name: idx_cmdb_server_management_monitoring_requirement; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_server_management_monitoring_requirement ON public.cmdb_server_management USING btree (monitoring_requirement) WHERE ((del_flag)::text = '0'::text);


--
-- TOC entry 4446 (class 1259 OID 17200)
-- Name: idx_cmdb_user_login_logs_login_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_user_login_logs_login_status ON public.cmdb_user_login_logs USING btree (login_status);


--
-- TOC entry 4447 (class 1259 OID 17199)
-- Name: idx_cmdb_user_login_logs_login_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_user_login_logs_login_time ON public.cmdb_user_login_logs USING btree (login_time);


--
-- TOC entry 4448 (class 1259 OID 17198)
-- Name: idx_cmdb_user_login_logs_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_user_login_logs_username ON public.cmdb_user_login_logs USING btree (username);


--
-- TOC entry 4472 (class 1259 OID 17338)
-- Name: idx_cmdb_user_page_permissions_page_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_user_page_permissions_page_id ON public.cmdb_user_page_permissions USING btree (page_id);


--
-- TOC entry 4473 (class 1259 OID 17337)
-- Name: idx_cmdb_user_page_permissions_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_user_page_permissions_user_id ON public.cmdb_user_page_permissions USING btree (user_id);


--
-- TOC entry 4429 (class 1259 OID 18388)
-- Name: idx_cmdb_vm_registry_host_ip_last_updated; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_vm_registry_host_ip_last_updated ON public.cmdb_vm_registry USING btree (host_ip_last_updated);


--
-- TOC entry 4430 (class 1259 OID 18389)
-- Name: idx_cmdb_vm_registry_host_ip_update_source; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_vm_registry_host_ip_update_source ON public.cmdb_vm_registry USING btree (host_ip_update_source);


--
-- TOC entry 4431 (class 1259 OID 17963)
-- Name: idx_cmdb_vm_registry_monitoring_requirement; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cmdb_vm_registry_monitoring_requirement ON public.cmdb_vm_registry USING btree (monitoring_requirement);


--
-- TOC entry 4483 (class 1259 OID 17566)
-- Name: idx_function_domain_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_function_domain_unique ON public.cmdb_function_domain_types USING btree (primary_category_code, function_domain_code, function_domain_type, del_flag);


--
-- TOC entry 4513 (class 1259 OID 18049)
-- Name: idx_msg_push_config_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_config_active ON public.msg_push_config USING btree (is_active) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4514 (class 1259 OID 18050)
-- Name: idx_msg_push_config_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_config_type ON public.msg_push_config USING btree (config_type) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4527 (class 1259 OID 18167)
-- Name: idx_msg_push_history_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_history_status ON public.msg_push_history USING btree (push_status);


--
-- TOC entry 4528 (class 1259 OID 18168)
-- Name: idx_msg_push_history_task; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_history_task ON public.msg_push_history USING btree (task_id);


--
-- TOC entry 4529 (class 1259 OID 18169)
-- Name: idx_msg_push_history_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_history_time ON public.msg_push_history USING btree (push_time);


--
-- TOC entry 4521 (class 1259 OID 18139)
-- Name: idx_msg_push_tasks_config; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_tasks_config ON public.msg_push_tasks USING btree (config_id) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4522 (class 1259 OID 18140)
-- Name: idx_msg_push_tasks_next_run; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_tasks_next_run ON public.msg_push_tasks USING btree (next_run_time) WHERE ((is_active = true) AND (del_flag = '0'::bpchar));


--
-- TOC entry 4523 (class 1259 OID 18141)
-- Name: idx_msg_push_tasks_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_tasks_status ON public.msg_push_tasks USING btree (status) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4524 (class 1259 OID 18142)
-- Name: idx_msg_push_tasks_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_tasks_type ON public.msg_push_tasks USING btree (task_type) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4517 (class 1259 OID 18109)
-- Name: idx_msg_push_templates_default; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_templates_default ON public.msg_push_templates USING btree (is_default) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4518 (class 1259 OID 18110)
-- Name: idx_msg_push_templates_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_msg_push_templates_type ON public.msg_push_templates USING btree (template_type) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4484 (class 1259 OID 17622)
-- Name: idx_ops_change_management_change_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_change_id ON public.ops_change_management USING btree (change_id);


--
-- TOC entry 4485 (class 1259 OID 17624)
-- Name: idx_ops_change_management_change_level; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_change_level ON public.ops_change_management USING btree (change_level);


--
-- TOC entry 4486 (class 1259 OID 17627)
-- Name: idx_ops_change_management_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_del_flag ON public.ops_change_management USING btree (del_flag);


--
-- TOC entry 4487 (class 1259 OID 17626)
-- Name: idx_ops_change_management_implementers; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_implementers ON public.ops_change_management USING gin (to_tsvector('simple'::regconfig, implementers));


--
-- TOC entry 4488 (class 1259 OID 17625)
-- Name: idx_ops_change_management_requester; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_requester ON public.ops_change_management USING btree (requester);


--
-- TOC entry 4489 (class 1259 OID 17623)
-- Name: idx_ops_change_management_system; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_system ON public.ops_change_management USING btree (system);


--
-- TOC entry 4494 (class 1259 OID 17661)
-- Name: idx_ops_change_templates_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_templates_del_flag ON public.ops_change_templates USING btree (del_flag);


--
-- TOC entry 4495 (class 1259 OID 17935)
-- Name: idx_ops_change_templates_is_default; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_templates_is_default ON public.ops_change_templates USING btree (is_default);


--
-- TOC entry 4502 (class 1259 OID 17813)
-- Name: idx_ops_event_management_assignee; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_assignee ON public.ops_event_management USING btree (assignee);


--
-- TOC entry 4503 (class 1259 OID 17876)
-- Name: idx_ops_event_management_close_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_close_time ON public.ops_event_management USING btree (close_time);


--
-- TOC entry 4504 (class 1259 OID 17814)
-- Name: idx_ops_event_management_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_del_flag ON public.ops_event_management USING btree (del_flag);


--
-- TOC entry 4505 (class 1259 OID 17807)
-- Name: idx_ops_event_management_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_event_id ON public.ops_event_management USING btree (event_id);


--
-- TOC entry 4506 (class 1259 OID 17808)
-- Name: idx_ops_event_management_event_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_event_type ON public.ops_event_management USING btree (event_type);


--
-- TOC entry 4507 (class 1259 OID 17810)
-- Name: idx_ops_event_management_priority; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_priority ON public.ops_event_management USING btree (priority);


--
-- TOC entry 4508 (class 1259 OID 17812)
-- Name: idx_ops_event_management_reporter; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_reporter ON public.ops_event_management USING btree (reporter);


--
-- TOC entry 4509 (class 1259 OID 17809)
-- Name: idx_ops_event_management_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_status ON public.ops_event_management USING btree (status);


--
-- TOC entry 4510 (class 1259 OID 17811)
-- Name: idx_ops_event_management_system; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_system ON public.ops_event_management USING btree (system);


--
-- TOC entry 4440 (class 1259 OID 17172)
-- Name: idx_report_filter_favorites_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_report_filter_favorites_del_flag ON public.report_filter_favorites USING btree (del_flag);


--
-- TOC entry 4441 (class 1259 OID 17171)
-- Name: idx_report_filter_favorites_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_report_filter_favorites_username ON public.report_filter_favorites USING btree (username);


--
-- TOC entry 4467 (class 1259 OID 17297)
-- Name: idx_schedule_task_history_schedule_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schedule_task_history_schedule_id ON public.cmdb_schedule_task_history USING btree (schedule_task_id);


--
-- TOC entry 4461 (class 1259 OID 17296)
-- Name: idx_schedule_task_items_discovery_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schedule_task_items_discovery_id ON public.cmdb_schedule_task_items USING btree (discovery_task_id) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4462 (class 1259 OID 17295)
-- Name: idx_schedule_task_items_schedule_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schedule_task_items_schedule_id ON public.cmdb_schedule_task_items USING btree (schedule_task_id) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4457 (class 1259 OID 17294)
-- Name: idx_schedule_tasks_next_run_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schedule_tasks_next_run_time ON public.cmdb_schedule_tasks USING btree (next_run_time) WHERE (((status)::text = 'active'::text) AND (del_flag = '0'::bpchar));


--
-- TOC entry 4458 (class 1259 OID 17293)
-- Name: idx_schedule_tasks_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schedule_tasks_status ON public.cmdb_schedule_tasks USING btree (status) WHERE (del_flag = '0'::bpchar);


--
-- TOC entry 4478 (class 1259 OID 17381)
-- Name: idx_system_config_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_system_config_key ON public.cmdb_system_config USING btree (config_key);


--
-- TOC entry 4555 (class 2606 OID 18403)
-- Name: cmdb_host_ip_update_logs fk_cmdb_host_ip_update_logs_vm_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_host_ip_update_logs
    ADD CONSTRAINT fk_cmdb_host_ip_update_logs_vm_id FOREIGN KEY (vm_id) REFERENCES public.cmdb_vm_registry(id) ON DELETE CASCADE;


--
-- TOC entry 4547 (class 2606 OID 17271)
-- Name: cmdb_schedule_task_items fk_discovery_task; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_items
    ADD CONSTRAINT fk_discovery_task FOREIGN KEY (discovery_task_id) REFERENCES public.cmdb_discovery_tasks(id);


--
-- TOC entry 4550 (class 2606 OID 17740)
-- Name: ops_calendar_duty_permissions fk_duty_perm_user; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_calendar_duty_permissions
    ADD CONSTRAINT fk_duty_perm_user FOREIGN KEY (user_id) REFERENCES public.cmdb_users(id);


--
-- TOC entry 4553 (class 2606 OID 18157)
-- Name: msg_push_history fk_push_history_config; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_history
    ADD CONSTRAINT fk_push_history_config FOREIGN KEY (config_id) REFERENCES public.msg_push_config(id);


--
-- TOC entry 4554 (class 2606 OID 18162)
-- Name: msg_push_history fk_push_history_task; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_history
    ADD CONSTRAINT fk_push_history_task FOREIGN KEY (task_id) REFERENCES public.msg_push_tasks(id);


--
-- TOC entry 4551 (class 2606 OID 18129)
-- Name: msg_push_tasks fk_push_tasks_config; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_tasks
    ADD CONSTRAINT fk_push_tasks_config FOREIGN KEY (config_id) REFERENCES public.msg_push_config(id);


--
-- TOC entry 4552 (class 2606 OID 18134)
-- Name: msg_push_tasks fk_push_tasks_template; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.msg_push_tasks
    ADD CONSTRAINT fk_push_tasks_template FOREIGN KEY (template_id) REFERENCES public.msg_push_templates(id);


--
-- TOC entry 4548 (class 2606 OID 17266)
-- Name: cmdb_schedule_task_items fk_schedule_task; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_items
    ADD CONSTRAINT fk_schedule_task FOREIGN KEY (schedule_task_id) REFERENCES public.cmdb_schedule_tasks(id);


--
-- TOC entry 4549 (class 2606 OID 17288)
-- Name: cmdb_schedule_task_history fk_schedule_task_history; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cmdb_schedule_task_history
    ADD CONSTRAINT fk_schedule_task_history FOREIGN KEY (schedule_task_id) REFERENCES public.cmdb_schedule_tasks(id);


--
-- TOC entry 4723 (class 0 OID 0)
-- Dependencies: 4722
-- Name: DATABASE cmdb; Type: ACL; Schema: -; Owner: postgres
--

GRANT CONNECT ON DATABASE cmdb TO cjmonitor;


--
-- TOC entry 4724 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT USAGE ON SCHEMA public TO cjmonitor;


--
-- TOC entry 4821 (class 0 OID 0)
-- Dependencies: 300
-- Name: TABLE cmdb_discovery_cleanup; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.cmdb_discovery_cleanup TO cjmonitor;


--
-- TOC entry 4883 (class 0 OID 0)
-- Dependencies: 302
-- Name: TABLE cmdb_host_ip_update_logs; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.cmdb_host_ip_update_logs TO cjmonitor;


--
-- TOC entry 5121 (class 0 OID 0)
-- Dependencies: 304
-- Name: TABLE cmdb_system_admin_responsibility_company_20250731; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.cmdb_system_admin_responsibility_company_20250731 TO cjmonitor;


--
-- TOC entry 5193 (class 0 OID 0)
-- Dependencies: 289
-- Name: TABLE duplicate_count; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.duplicate_count TO cjmonitor;


--
-- TOC entry 5212 (class 0 OID 0)
-- Dependencies: 291
-- Name: TABLE msg_push_config; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.msg_push_config TO cjmonitor;


--
-- TOC entry 5228 (class 0 OID 0)
-- Dependencies: 297
-- Name: TABLE msg_push_history; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.msg_push_history TO cjmonitor;


--
-- TOC entry 5243 (class 0 OID 0)
-- Dependencies: 295
-- Name: TABLE msg_push_tasks; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.msg_push_tasks TO cjmonitor;


--
-- TOC entry 5255 (class 0 OID 0)
-- Dependencies: 293
-- Name: TABLE msg_push_templates; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.msg_push_templates TO cjmonitor;


--
-- TOC entry 5281 (class 0 OID 0)
-- Dependencies: 277
-- Name: TABLE ops_calendar; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.ops_calendar TO cjmonitor;


--
-- TOC entry 5290 (class 0 OID 0)
-- Dependencies: 279
-- Name: TABLE ops_calendar_duty_permissions; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.ops_calendar_duty_permissions TO cjmonitor;


--
-- TOC entry 5342 (class 0 OID 0)
-- Dependencies: 282
-- Name: TABLE ops_event_management; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.ops_event_management TO cjmonitor;


--
-- TOC entry 5346 (class 0 OID 0)
-- Dependencies: 264
-- Name: TABLE v_cmdb_discovery_results; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_discovery_results TO cjmonitor;


--
-- TOC entry 5348 (class 0 OID 0)
-- Dependencies: 286
-- Name: TABLE v_cmdb_server_management; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_server_management TO cjmonitor;


--
-- TOC entry 5350 (class 0 OID 0)
-- Dependencies: 305
-- Name: TABLE v_cmdb_vm_registry; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_vm_registry TO cjmonitor;


--
-- TOC entry 5351 (class 0 OID 0)
-- Dependencies: 306
-- Name: TABLE v_cmdb_application_system_info; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_application_system_info TO cjmonitor;


--
-- TOC entry 5353 (class 0 OID 0)
-- Dependencies: 245
-- Name: TABLE v_cmdb_dashboard; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_dashboard TO cjmonitor;


--
-- TOC entry 5355 (class 0 OID 0)
-- Dependencies: 287
-- Name: TABLE v_cmdb_device_management; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_device_management TO cjmonitor;


--
-- TOC entry 5357 (class 0 OID 0)
-- Dependencies: 267
-- Name: TABLE v_cmdb_discovery_tasks; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_discovery_tasks TO cjmonitor;


--
-- TOC entry 5359 (class 0 OID 0)
-- Dependencies: 288
-- Name: TABLE v_cmdb_global_search; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_global_search TO cjmonitor;


--
-- TOC entry 5361 (class 0 OID 0)
-- Dependencies: 303
-- Name: TABLE v_cmdb_host_ip_update_logs; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_host_ip_update_logs TO cjmonitor;


--
-- TOC entry 5362 (class 0 OID 0)
-- Dependencies: 308
-- Name: TABLE v_cmdb_host_scan_results; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_host_scan_results TO cjmonitor;


--
-- TOC entry 5364 (class 0 OID 0)
-- Dependencies: 243
-- Name: TABLE v_cmdb_monitored_ip_list; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_monitored_ip_list TO cjmonitor;


--
-- TOC entry 5366 (class 0 OID 0)
-- Dependencies: 244
-- Name: TABLE v_cmdb_system_admin_responsibility; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_system_admin_responsibility TO cjmonitor;


--
-- TOC entry 5367 (class 0 OID 0)
-- Dependencies: 307
-- Name: TABLE v_cmdb_system_admin_responsibility_company; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_system_admin_responsibility_company TO cjmonitor;


--
-- TOC entry 5369 (class 0 OID 0)
-- Dependencies: 298
-- Name: TABLE v_cmdb_user_logs; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_cmdb_user_logs TO cjmonitor;


--
-- TOC entry 5371 (class 0 OID 0)
-- Dependencies: 284
-- Name: TABLE v_ops_calendar; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_ops_calendar TO cjmonitor;


--
-- TOC entry 5373 (class 0 OID 0)
-- Dependencies: 280
-- Name: TABLE v_ops_calendar_duty_permissions; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_ops_calendar_duty_permissions TO cjmonitor;


--
-- TOC entry 5375 (class 0 OID 0)
-- Dependencies: 276
-- Name: TABLE v_ops_change_management; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_ops_change_management TO cjmonitor;


--
-- TOC entry 5377 (class 0 OID 0)
-- Dependencies: 285
-- Name: TABLE v_ops_event_management; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_ops_event_management TO cjmonitor;


--
-- TOC entry 5378 (class 0 OID 0)
-- Dependencies: 268
-- Name: TABLE v_report_filter_favorites; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.v_report_filter_favorites TO cjmonitor;


--
-- TOC entry 2307 (class 826 OID 17711)
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT SELECT ON TABLES TO cjmonitor;


-- Completed on 2025-08-05 08:48:48

--
-- PostgreSQL database dump complete
--

--
-- Database "postgres" dump
--

\connect postgres

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12
-- Dumped by pg_dump version 17.0

-- Started on 2025-08-05 08:48:49

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 2 (class 3079 OID 16388)
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;


--
-- TOC entry 4143 (class 0 OID 0)
-- Dependencies: 2
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- TOC entry 221 (class 1259 OID 18298)
-- Name: ops_change_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 9999
    CACHE 1
    CYCLE;


ALTER SEQUENCE public.ops_change_id_seq OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 220 (class 1259 OID 18258)
-- Name: ops_change_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_change_management (
    id integer NOT NULL,
    change_id character varying(20) NOT NULL,
    title character varying(255) NOT NULL,
    system character varying(100) NOT NULL,
    change_level character varying(10) NOT NULL,
    planned_change_time date NOT NULL,
    requester character varying(50) NOT NULL,
    implementers text NOT NULL,
    description text,
    oa_process boolean DEFAULT false,
    oa_process_file character varying(255),
    signed_archive boolean DEFAULT false,
    signed_archive_file character varying(255),
    operation_sheet character varying(255),
    supplementary_material character varying(255),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50),
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.ops_change_management OWNER TO postgres;

--
-- TOC entry 4144 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.change_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.change_id IS '变更编号，格式：BG-YYYYMMDD-序号';


--
-- TOC entry 4145 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.title IS '变更名称';


--
-- TOC entry 4146 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.system IS '所属系统，来自系统管理员责任表（公司）的业务系统简称';


--
-- TOC entry 4147 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.change_level; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.change_level IS '变更级别，使用数据字典P';


--
-- TOC entry 4148 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.planned_change_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.planned_change_time IS '计划变更时间';


--
-- TOC entry 4149 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.requester; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.requester IS '变更负责人，存储username';


--
-- TOC entry 4150 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.implementers; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.implementers IS '变更实施人，存储多个username，以逗号分隔';


--
-- TOC entry 4151 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.description IS '变更描述';


--
-- TOC entry 4152 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.oa_process; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.oa_process IS 'OA流程是否上传';


--
-- TOC entry 4153 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.oa_process_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.oa_process_file IS 'OA流程文件路径';


--
-- TOC entry 4154 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.signed_archive; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.signed_archive IS '签字存档是否上传';


--
-- TOC entry 4155 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.signed_archive_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.signed_archive_file IS '签字存档文件路径';


--
-- TOC entry 4156 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN ops_change_management.operation_sheet; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_change_management.operation_sheet IS '变更操作表文件路径';


--
-- TOC entry 218 (class 1259 OID 18251)
-- Name: ops_change_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_management_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_change_management_id_seq OWNER TO postgres;

--
-- TOC entry 4157 (class 0 OID 0)
-- Dependencies: 218
-- Name: ops_change_management_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_change_management_id_seq OWNED BY public.ops_change_management.id;


--
-- TOC entry 219 (class 1259 OID 18255)
-- Name: ops_change_templates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_change_templates (
    id integer NOT NULL,
    template_name character varying(255) NOT NULL,
    template_description text,
    file_path character varying(255) NOT NULL,
    file_type character varying(200) NOT NULL,
    file_size integer NOT NULL,
    original_filename character varying(255) NOT NULL,
    is_default boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50) NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50) NOT NULL,
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.ops_change_templates OWNER TO postgres;

--
-- TOC entry 217 (class 1259 OID 18244)
-- Name: ops_change_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_change_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_change_templates_id_seq OWNER TO postgres;

--
-- TOC entry 4158 (class 0 OID 0)
-- Dependencies: 217
-- Name: ops_change_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_change_templates_id_seq OWNED BY public.ops_change_templates.id;


--
-- TOC entry 222 (class 1259 OID 18299)
-- Name: ops_event_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_event_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 9999
    CACHE 1
    CYCLE;


ALTER SEQUENCE public.ops_event_id_seq OWNER TO postgres;

--
-- TOC entry 216 (class 1259 OID 18239)
-- Name: ops_event_management; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ops_event_management (
    id integer NOT NULL,
    event_id character varying(20) NOT NULL,
    title character varying(255) NOT NULL,
    event_type character varying(20) DEFAULT 'T00002'::character varying NOT NULL,
    status character varying(20) DEFAULT '待处理'::character varying NOT NULL,
    priority character varying(10) DEFAULT 'U00002'::character varying NOT NULL,
    system character varying(100),
    reporter character varying(50) NOT NULL,
    assignee character varying(50),
    report_time timestamp(6) without time zone NOT NULL,
    close_time timestamp(6) without time zone,
    description text,
    process text,
    solution text,
    improvement_plan text,
    consequence_cause_analysis text,
    screenshot boolean DEFAULT false,
    screenshot_file character varying(255),
    process_doc boolean DEFAULT false,
    process_doc_file character varying(255),
    supplementary_material character varying(255),
    created_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(50),
    updated_at timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_by character varying(50),
    del_flag character(1) DEFAULT '0'::bpchar
);


ALTER TABLE public.ops_event_management OWNER TO postgres;

--
-- TOC entry 4159 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.event_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.event_id IS '事件编号，格式：SJ-YYYYMMDD-序号';


--
-- TOC entry 4160 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.title IS '事件标题';


--
-- TOC entry 4161 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.event_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.event_type IS '事件类型，使用数据字典T';


--
-- TOC entry 4162 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.status IS '事件状态，使用数据字典EVENT_STATUS';


--
-- TOC entry 4163 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.priority; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.priority IS '事件级别，使用数据字典U';


--
-- TOC entry 4164 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.system; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.system IS '影响系统，来自系统管理员责任表（公司）的业务系统简称';


--
-- TOC entry 4165 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.reporter; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.reporter IS '报告人，存储username';


--
-- TOC entry 4166 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.assignee; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.assignee IS '处理人，存储username';


--
-- TOC entry 4167 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.report_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.report_time IS '报告时间';


--
-- TOC entry 4168 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.close_time; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.close_time IS '关闭时间';


--
-- TOC entry 4169 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.description; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.description IS '事件描述';


--
-- TOC entry 4170 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.process; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process IS '处理过程';


--
-- TOC entry 4171 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.solution; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.solution IS '解决方案';


--
-- TOC entry 4172 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.improvement_plan; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.improvement_plan IS '改进计划';


--
-- TOC entry 4173 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.consequence_cause_analysis; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.consequence_cause_analysis IS '事件导致的后果、发生原因和事件性质判断';


--
-- TOC entry 4174 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.screenshot; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.screenshot IS '事件截图是否上传';


--
-- TOC entry 4175 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.screenshot_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.screenshot_file IS '事件截图文件路径';


--
-- TOC entry 4176 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.process_doc; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process_doc IS '处理过程文档是否上传';


--
-- TOC entry 4177 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.process_doc_file; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.process_doc_file IS '处理过程文档文件路径';


--
-- TOC entry 4178 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN ops_event_management.supplementary_material; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ops_event_management.supplementary_material IS '补充材料文件路径';


--
-- TOC entry 215 (class 1259 OID 18238)
-- Name: ops_event_management_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ops_event_management_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ops_event_management_id_seq OWNER TO postgres;

--
-- TOC entry 4179 (class 0 OID 0)
-- Dependencies: 215
-- Name: ops_event_management_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ops_event_management_id_seq OWNED BY public.ops_event_management.id;


--
-- TOC entry 3968 (class 2604 OID 18267)
-- Name: ops_change_management id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_management ALTER COLUMN id SET DEFAULT nextval('public.ops_change_management_id_seq'::regclass);


--
-- TOC entry 3963 (class 2604 OID 18261)
-- Name: ops_change_templates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_templates ALTER COLUMN id SET DEFAULT nextval('public.ops_change_templates_id_seq'::regclass);


--
-- TOC entry 3954 (class 2604 OID 18242)
-- Name: ops_event_management id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_event_management ALTER COLUMN id SET DEFAULT nextval('public.ops_event_management_id_seq'::regclass);


--
-- TOC entry 3995 (class 2606 OID 18281)
-- Name: ops_change_management ops_change_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_management
    ADD CONSTRAINT ops_change_management_pkey PRIMARY KEY (id);


--
-- TOC entry 3987 (class 2606 OID 18279)
-- Name: ops_change_templates ops_change_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_change_templates
    ADD CONSTRAINT ops_change_templates_pkey PRIMARY KEY (id);


--
-- TOC entry 3983 (class 2606 OID 18277)
-- Name: ops_event_management ops_event_management_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ops_event_management
    ADD CONSTRAINT ops_event_management_pkey PRIMARY KEY (id);


--
-- TOC entry 3988 (class 1259 OID 18285)
-- Name: idx_ops_change_management_change_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_change_id ON public.ops_change_management USING btree (change_id);


--
-- TOC entry 3989 (class 1259 OID 18289)
-- Name: idx_ops_change_management_change_level; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_change_level ON public.ops_change_management USING btree (change_level);


--
-- TOC entry 3990 (class 1259 OID 18294)
-- Name: idx_ops_change_management_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_del_flag ON public.ops_change_management USING btree (del_flag);


--
-- TOC entry 3991 (class 1259 OID 18293)
-- Name: idx_ops_change_management_implementers; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_implementers ON public.ops_change_management USING gin (to_tsvector('simple'::regconfig, implementers));


--
-- TOC entry 3992 (class 1259 OID 18291)
-- Name: idx_ops_change_management_requester; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_requester ON public.ops_change_management USING btree (requester);


--
-- TOC entry 3993 (class 1259 OID 18287)
-- Name: idx_ops_change_management_system; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_management_system ON public.ops_change_management USING btree (system);


--
-- TOC entry 3984 (class 1259 OID 18282)
-- Name: idx_ops_change_templates_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_templates_del_flag ON public.ops_change_templates USING btree (del_flag);


--
-- TOC entry 3985 (class 1259 OID 18283)
-- Name: idx_ops_change_templates_is_default; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_change_templates_is_default ON public.ops_change_templates USING btree (is_default);


--
-- TOC entry 3974 (class 1259 OID 18296)
-- Name: idx_ops_event_management_assignee; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_assignee ON public.ops_event_management USING btree (assignee);


--
-- TOC entry 3975 (class 1259 OID 18297)
-- Name: idx_ops_event_management_del_flag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_del_flag ON public.ops_event_management USING btree (del_flag);


--
-- TOC entry 3976 (class 1259 OID 18284)
-- Name: idx_ops_event_management_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_event_id ON public.ops_event_management USING btree (event_id);


--
-- TOC entry 3977 (class 1259 OID 18286)
-- Name: idx_ops_event_management_event_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_event_type ON public.ops_event_management USING btree (event_type);


--
-- TOC entry 3978 (class 1259 OID 18290)
-- Name: idx_ops_event_management_priority; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_priority ON public.ops_event_management USING btree (priority);


--
-- TOC entry 3979 (class 1259 OID 18295)
-- Name: idx_ops_event_management_reporter; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_reporter ON public.ops_event_management USING btree (reporter);


--
-- TOC entry 3980 (class 1259 OID 18288)
-- Name: idx_ops_event_management_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_status ON public.ops_event_management USING btree (status);


--
-- TOC entry 3981 (class 1259 OID 18292)
-- Name: idx_ops_event_management_system; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ops_event_management_system ON public.ops_event_management USING btree (system);


-- Completed on 2025-08-05 08:48:49

--
-- PostgreSQL database dump complete
--

-- Completed on 2025-08-05 08:48:49

--
-- PostgreSQL database cluster dump complete
--

