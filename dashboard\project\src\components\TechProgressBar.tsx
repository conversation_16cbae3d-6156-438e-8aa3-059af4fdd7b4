import React, { useEffect, useState } from 'react';

interface TechProgressBarProps {
  value: number;
  max: number;
  label: string;
  variant?: 'circular' | 'linear' | 'arc';
  glowColor?: string;
  animationDuration?: number;
  size?: number;
}

const TechProgressBar: React.FC<TechProgressBarProps> = ({
  value,
  max,
  label,
  variant = 'circular',
  glowColor = '#00d4ff',
  animationDuration = 1000,
  size = 120
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);
  const percentage = Math.min((value / max) * 100, 100);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValue(percentage);
    }, 100);
    return () => clearTimeout(timer);
  }, [percentage]);

  if (variant === 'circular') {
    const radius = (size - 20) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (animatedValue / 100) * circumference;

    return (
      <div className="flex flex-col items-center">
        <div className="relative" style={{ width: size, height: size }}>
          <svg width={size} height={size} className="transform -rotate-90">
            <defs>
              <linearGradient id={`gradient-${label}`} x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={glowColor} />
                <stop offset="100%" stopColor="#0066FF" />
              </linearGradient>
              <filter id={`glow-${label}`}>
                <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>

            {/* 背景圆圈 */}
            <circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke="rgba(59, 130, 246, 0.2)"
              strokeWidth="8"
              fill="transparent"
            />

            {/* 进度圆圈 */}
            <circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke={`url(#gradient-${label})`}
              strokeWidth="8"
              fill="transparent"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              filter={`url(#glow-${label})`}
              style={{
                transition: `stroke-dashoffset ${animationDuration}ms ease-out`
              }}
            />
          </svg>

          {/* 中心文字 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-white count-animation">
                {Math.round(animatedValue)}
              </div>
              <div className="text-xs text-gray-400">%</div>
            </div>
          </div>
        </div>

        {/* 标签 */}
        <div className="mt-2 text-sm text-gray-300 text-center">
          {label}
        </div>
      </div>
    );
  }

  if (variant === 'linear') {
    return (
      <div className="w-full">
        {label && (
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-300">{label}</span>
            <span className="text-sm font-bold text-white">{Math.round(animatedValue)}%</span>
          </div>
        )}
        <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
          <div
            className="h-full rounded-full neon-glow"
            style={{
              width: `${animatedValue}%`,
              background: `linear-gradient(90deg, ${glowColor}, #0066FF)`,
              transition: `width ${animationDuration}ms ease-out`
            }}
          />
        </div>
      </div>
    );
  }

  return null;
};

export default TechProgressBar;