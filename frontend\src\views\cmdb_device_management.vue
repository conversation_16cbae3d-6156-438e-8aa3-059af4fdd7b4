<style lang="scss" scoped>
.user-manage {
  .search-card {
    margin-bottom: 10px;
    height: auto;

    // label-width:130px;
  }

  :deep(.el-table) {

    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap;
      /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all;
      /* 尽量保持单词完整，不强制断开 */
      max-width: 400px;
      /* 保持最大宽度不变 */
      width: 100%;
      /* 确保宽度一致 */
      word-wrap: break-word;
      /* 当单词超过容器宽度时允许换行 */
      display: inline-block;
      /* 确保元素可以正确处理宽度 */
    }

    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }

  .pagination {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
  }
}

.form-control {
  width: auto;
  /* 占满父容器宽度 */
  min-width: 190px;
}
</style>

<template>
  <div class="user-manage">
    <el-dialog v-model="dialogVisible.add" title="新增网络设备信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="addFormRef" label-position="right" status-icon>
          <!-- IP地址 -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 带外管理IP -->
          <el-form-item prop="out_of_band_management" label="带外管理IP:">
            <el-input v-model="formData.out_of_band_management" style="width: 240px" clearable
              placeholder="请输入带外管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <el-form-item prop="hostname" label="主机名:">
            <el-input v-model="formData.hostname" style="width: 240px" clearable placeholder="请输入主机名" />
          </el-form-item>

          <!-- 功能用途 -->
          <el-form-item prop="function_purpose" label="功能用途:">
            <el-input v-model="formData.function_purpose" style="width: 240px" clearable placeholder="请输入功能用途" />
          </el-form-item>

          <!-- 管理员1 -->
          <el-form-item prop="admin1" label="管理员1:">
            <el-select v-model="formData.admin1" style="width: 240px" placeholder="请选择管理员1" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 管理员2 -->
          <el-form-item prop="admin2" label="管理员2:">
            <el-select v-model="formData.admin2" style="width: 240px" placeholder="请选择管理员2" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 设备类型 -->
          <el-form-item prop="device_type" label="设备类型:">
            <el-select v-model="formData.device_type" style="width: 240px" clearable filterable placeholder="请选择设备类型">
              <el-option v-for="item in devicetypes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生产属性 -->
          <el-form-item prop="production_attributes" label="生产属性:">
            <el-select v-model="formData.production_attributes" style="width: 240px" clearable filterable placeholder="请选择生产属性">
              <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 数据中心 -->
          <el-form-item prop="data_center" label="数据中心:">
            <el-select v-model="formData.data_center" style="width: 240px" clearable filterable placeholder="请选择数据中心">
              <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生命周期 -->
          <el-form-item prop="operation_status" label="生命周期:">
            <el-select v-model="formData.operation_status" style="width: 240px" clearable filterable placeholder="请选择生命周期">
              <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>
          <!-- 资产编号 -->
          <el-form-item prop="asset_number" label="资产编号:">
            <el-input v-model="formData.asset_number" style="width: 240px" clearable placeholder="请输入资产编号" />
          </el-form-item>

          <!-- 购买日期 -->
          <el-form-item prop="purchase_date" label="购买日期:">
            <el-date-picker v-model="formData.purchase_date" type="date" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
              style="width: 240px" clearable placeholder="选择日期" />
          </el-form-item>

          <!-- 维保年限 -->
          <el-form-item prop="maintenance_years" label="维保年限:">
            <el-input v-model="formData.maintenance_years" style="width: 240px" clearable placeholder="请输入维保年限" />
          </el-form-item>

          <!-- 序列号 -->
          <el-form-item prop="serial_number" label="序列号:">
            <el-input v-model="formData.serial_number" style="width: 240px" clearable placeholder="请输入序列号" />
          </el-form-item>

          <!-- 设备型号 -->
          <el-form-item prop="model" label="设备型号:">
            <el-input v-model="formData.model" style="width: 240px" clearable placeholder="请输入设备型号" />
          </el-form-item>

          <!-- 版本 -->
          <el-form-item prop="version" label="版本:">
            <el-input v-model="formData.version" style="width: 240px" clearable placeholder="请输入版本" />
          </el-form-item>

          <!-- CPU型号 -->
          <el-form-item prop="cpu_model" label="CPU型号:">
            <el-input v-model="formData.cpu_model" style="width: 240px" clearable placeholder="请输入CPU型号" />
          </el-form-item>

          <!-- 是否信创 -->
          <el-form-item prop="is_innovative_tech" label="是否信创:">
            <el-select v-model="formData.is_innovative_tech" style="width: 240px" clearable filterable placeholder="请选择是否信创">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:" :required="true">
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable filterable placeholder="请选择需要监控"
              @change="handleMonitoringRequirementChange">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === false">
            <el-input v-model="formData.monitoring_requirement_description" type="textarea" :rows="3"
              style="width: 240px" clearable placeholder="当需要监控为否时，请说明原因"
              :disabled="formData.monitoring_requirement === true" />
          </el-form-item>

          <!-- 监控IP -->
          <el-form-item prop="monitoring_ip" label="监控IP:">
            <el-input v-model="formData.monitoring_ip" style="width: 240px" clearable placeholder="请输入监控IP" />
          </el-form-item>

          <!-- 架构模式 - 已注释，保留数据库字段作为后期扩展 -->
          <!-- <el-form-item prop="architecture_mode" label="架构模式:">
            <el-input
              v-model="formData.architecture_mode"
              style="width: 240px"
              clearable
              placeholder="请输入架构模式"
            />
          </el-form-item> -->

          <!-- 是否单点 -->
          <el-form-item prop="is_single_point" label="是否单点:">
            <el-select v-model="formData.is_single_point" style="width: 240px" clearable filterable placeholder="请选择是否单点"
              @change="validateManagedAddresses">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 互备主机IP -->
          <el-form-item prop="managed_addresses" label="互备主机IP:" :required="formData.is_single_point === '否'">
            <el-input v-model="formData.managed_addresses" style="width: 240px" clearable
              placeholder="请输入互备主机IP，多个IP用英文逗号分隔" />
          </el-form-item>

          <!-- 是否在监控列表中
          <el-form-item prop="in_monitoring_list" label="是否在监控列表中:">
            <el-select
              v-model="formData.in_monitoring_list"
              style="width: 240px"
              placeholder="请选择是否在监控列表中"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->

          <!-- 预监控验证 -->
          <!-- <el-form-item prop="pre_monitoring_verified" label="预监控验证:">
            <el-input
              v-model="formData.pre_monitoring_verified"
              style="width: 240px"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入预监控验证"
            />
          </el-form-item> -->

          <!-- 检查 -->
          <!-- <el-form-item prop="inspection" label="检查:">
            <el-select
              v-model="formData.inspection"
              style="width: 240px"
              clearable
              placeholder="请选择是否检查"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->

          <!-- 备注 -->
          <el-form-item prop="remarks" label="备注:">
            <el-input v-model="formData.remarks" style="width: 240px" clearable placeholder="请输入备注" />
          </el-form-item>

        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">返回</el-button>
          <el-button type="primary" @click="validateAndSubmitAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.edit" title="编辑网络设备信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="editFormRef" label-position="right" status-icon>
          <!-- IP地址 -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 带外管理IP -->
          <el-form-item prop="out_of_band_management" label="带外管理IP:">
            <el-input v-model="formData.out_of_band_management" style="width: 240px" clearable
              placeholder="请输入带外管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <el-form-item prop="hostname" label="主机名:">
            <el-input v-model="formData.hostname" style="width: 240px" clearable placeholder="请输入主机名" />
          </el-form-item>

          <!-- 功能用途 -->
          <el-form-item prop="function_purpose" label="功能用途:">
            <el-input v-model="formData.function_purpose" style="width: 240px" clearable placeholder="请输入功能用途" />
          </el-form-item>

          <!-- 管理员1 -->
          <el-form-item prop="admin1" label="管理员1:">
            <el-select v-model="formData.admin1" style="width: 240px" placeholder="请选择管理员1" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 管理员2 -->
          <el-form-item prop="admin2" label="管理员2:">
            <el-select v-model="formData.admin2" style="width: 240px" placeholder="请选择管理员2" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 设备类型 -->
          <el-form-item prop="device_type" label="设备类型:">
            <el-select v-model="formData.device_type" style="width: 240px" clearable filterable placeholder="请选择设备类型">
              <el-option v-for="item in devicetypes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生产属性 -->
          <el-form-item prop="production_attributes" label="生产属性:">
            <el-select v-model="formData.production_attributes" style="width: 240px" clearable filterable placeholder="请选择生产属性">
              <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 数据中心 -->
          <el-form-item prop="data_center" label="数据中心:">
            <el-select v-model="formData.data_center" style="width: 240px" clearable filterable placeholder="请选择数据中心">
              <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生命周期 -->
          <el-form-item prop="operation_status" label="生命周期:">
            <el-select v-model="formData.operation_status" style="width: 240px" clearable filterable placeholder="请选择生命周期">
              <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>
          <!-- 资产编号 -->
          <el-form-item prop="asset_number" label="资产编号:">
            <el-input v-model="formData.asset_number" style="width: 240px" clearable placeholder="请输入资产编号" />
          </el-form-item>

          <!-- 购买日期 -->
          <el-form-item prop="purchase_date" label="购买日期:">
            <el-date-picker v-model="formData.purchase_date" type="date" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
              style="width: 240px" clearable placeholder="选择日期" />
          </el-form-item>

          <!-- 维保年限 -->
          <el-form-item prop="maintenance_years" label="维保年限:">
            <el-input v-model="formData.maintenance_years" style="width: 240px" clearable placeholder="请输入维保年限" />
          </el-form-item>

          <!-- 序列号 -->
          <el-form-item prop="serial_number" label="序列号:">
            <el-input v-model="formData.serial_number" style="width: 240px" clearable placeholder="请输入序列号" />
          </el-form-item>

          <!-- 设备型号 -->
          <el-form-item prop="model" label="设备型号:">
            <el-input v-model="formData.model" style="width: 240px" clearable placeholder="请输入设备型号" />
          </el-form-item>

          <!-- 版本 -->
          <el-form-item prop="version" label="版本:">
            <el-input v-model="formData.version" style="width: 240px" clearable placeholder="请输入版本" />
          </el-form-item>

          <!-- CPU型号 -->
          <el-form-item prop="cpu_model" label="CPU型号:">
            <el-input v-model="formData.cpu_model" style="width: 240px" clearable placeholder="请输入CPU型号" />
          </el-form-item>

          <!-- 是否信创 -->
          <el-form-item prop="is_innovative_tech" label="是否信创:">
            <el-select v-model="formData.is_innovative_tech" style="width: 240px" clearable filterable placeholder="请选择是否信创">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:" :required="true">
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable filterable placeholder="请选择需要监控"
              @change="handleMonitoringRequirementChange">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === false">
            <el-input v-model="formData.monitoring_requirement_description" type="textarea" :rows="3"
              style="width: 240px" clearable placeholder="当需要监控为否时，请说明原因"
              :disabled="formData.monitoring_requirement === true" />
          </el-form-item>

          <!-- 监控IP -->
          <el-form-item prop="monitoring_ip" label="监控IP:">
            <el-input v-model="formData.monitoring_ip" style="width: 240px" clearable placeholder="请输入监控IP" />
          </el-form-item>

          <!-- 架构模式 - 已注释，保留数据库字段作为后期扩展 -->
          <!-- <el-form-item prop="architecture_mode" label="架构模式:">
            <el-input
              v-model="formData.architecture_mode"
              style="width: 240px"
              clearable
              placeholder="请输入架构模式"
            />
          </el-form-item> -->

          <!-- 是否单点 -->
          <el-form-item prop="is_single_point" label="是否单点:">
            <el-select v-model="formData.is_single_point" style="width: 240px" clearable filterable placeholder="请选择是否单点"
              @change="validateManagedAddresses">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 互备主机IP -->
          <el-form-item prop="managed_addresses" label="互备主机IP:" :required="formData.is_single_point === '否'">
            <el-input v-model="formData.managed_addresses" style="width: 240px" clearable
              placeholder="请输入互备主机IP，多个IP用英文逗号分隔" />
          </el-form-item>

          <!-- 是否在监控列表中 -->
          <!-- <el-form-item prop="in_monitoring_list" label="是否在监控列表中:">
            <el-select
              v-model="formData.in_monitoring_list"
              style="width: 240px"
              placeholder="请选择是否在监控列表中"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->

          <!-- 预监控验证 -->
          <!-- <el-form-item prop="pre_monitoring_verified" label="预监控验证:">
            <el-input
              v-model="formData.pre_monitoring_verified"
              style="width: 240px"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入预监控验证"
            />
          </el-form-item> -->

          <!-- 检查 -->
          <!-- <el-form-item prop="inspection" label="检查:">
            <el-select
              v-model="formData.inspection"
              style="width: 240px"
              clearable
              placeholder="请选择是否检查"
            >
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item> -->
          <!-- 备注 -->
          <el-form-item prop="remarks" label="备注:">
            <el-input v-model="formData.remarks" style="width: 240px" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitEdit">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.delete" title="删除管理IP" width="500" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-alert type="warning" :title="`确定要删除 IP 为 ${formData.management_ip} 的记录吗？`" :closable="false" />
      <template #footer>
        <div>
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true">
        <el-row :gutter="10">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item label="管理IP">
              <el-input v-model="search.management_ip" placeholder="请输入管理IP" clearable class="form-control" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="带外管理IP">
              <el-input v-model="search.out_of_band_management" placeholder="请输入带外管理IP" clearable
                class="form-control" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主机名">
              <el-input v-model="search.hostname" placeholder="请输入主机名" clearable class="form-control" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="管理员1">
              <el-select v-model="search.admin1" placeholder="请选择管理员1" clearable filterable class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="管理员2">
              <el-select v-model="search.admin2" placeholder="请选择管理员2" clearable filterable class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="生命周期">
              <el-select v-model="search.operation_status" placeholder="请选择生命周期" clearable filterable class="form-control">
                <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>          

          <el-col :span="6">
            <el-form-item label="设备类型">
              <el-select v-model="search.device_type" placeholder="请选择设备类型" clearable filterable class="form-control">
                <el-option v-for="item in devicetypes" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="生产属性">
              <el-select v-model="search.production_attributes" placeholder="请选择生产属性" clearable filterable class="form-control">
                <el-option v-for="item in productionattributes" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属机房">
              <el-select v-model="search.data_center" placeholder="请选择所属机房" clearable filterable multiple collapse-tags collapse-tags-tooltip class="form-control">
                <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="序列号">
              <el-input v-model="search.serial_number" placeholder="请输入序列号" clearable class="form-control" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="是否信创">
              <el-select v-model="search.is_innovative_tech" placeholder="请选择是否信创" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="需要监控">
              <el-select v-model="search.monitoring_requirement" placeholder="请选择需要监控" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="监控状态">
              <el-select v-model="search.is_monitored" placeholder="请选择监控状态" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="PING状态">
              <el-select v-model="search.online_status" placeholder="请选择PING状态" clearable filterable class="form-control">
                <el-option label="在线" value="在线" />
                <el-option label="离线" value="离线" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="年份分类">
              <el-input
                v-model="search.year_category"
                placeholder="请输入年份分类"
                clearable
                class="form-control"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="24" class="search-buttons-col">
            <el-form-item label=" " class="form-item-with-label search-buttons">
              <div class="button-container">
                <el-button type="primary" @click="loadData">
                  <el-icon>
                    <Search />
                  </el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" :disabled="!hasInsertPermission" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>新增资产
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData">
          <el-icon>
            <Download />
          </el-icon> 导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="userArr" ref="table" border stripe v-loading="loading" table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="id" label="序号" v-if="false"></el-table-column>
        <el-table-column prop="management_ip" label="管理IP" sortable></el-table-column>
        <el-table-column prop="out_of_band_management" label="带外管理IP" sortable></el-table-column>
        <el-table-column prop="hostname" label="主机名" sortable>
        </el-table-column>
        <el-table-column prop="function_purpose" label="功能用途" sortable></el-table-column>
        <el-table-column prop="admin1" label="管理员1" sortable></el-table-column>
        <el-table-column prop="admin2" label="管理员2" sortable></el-table-column>

        <el-table-column prop="operation_status" label="生命周期" sortable>
          <template #default="scope">
            <el-tag :type="getLifecycleTagType(scope.row.operation_status)">
              {{ scope.row.operation_status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="device_type" label="设备类型" sortable></el-table-column>

        <el-table-column prop="production_attributes" label="生产属性" sortable></el-table-column>

        <el-table-column prop="data_center" label="所属机房" sortable></el-table-column>

        <el-table-column prop="asset_number" label="财务资产编号" sortable></el-table-column>

        <el-table-column prop="purchase_date" label="采购时间" sortable></el-table-column>

        <el-table-column prop="maintenance_years" label="维保年限" sortable></el-table-column>
        <el-table-column prop="maintenance_end_date" label="维保截止日期" sortable></el-table-column>
        <el-table-column prop="serial_number" label="序列号" sortable></el-table-column>
        <el-table-column prop="model" label="设备型号" sortable></el-table-column>
        <el-table-column prop="version" label="版本" sortable></el-table-column>
        <el-table-column prop="cpu_model" label="CPU型号" sortable></el-table-column>
        <el-table-column prop="is_innovative_tech" label="是否信创" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_innovative_tech === '是' ? 'success' : 'danger'">
              {{ scope.row.is_innovative_tech }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="monitoring_requirement" label="需要监控" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.monitoring_requirement === '是' ? 'success' : 'danger'">
              {{ scope.row.monitoring_requirement }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="monitoring_requirement_description" label="不监控原因" sortable min-width="150">
          <template #default="scope">
            <span style="color: #909399; font-size: 12px;" v-if="!scope.row.monitoring_requirement_description">
              -
            </span>
            <span v-else>
              {{ scope.row.monitoring_requirement_description }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="is_monitored" label="监控状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_monitored === '是' ? 'success' : 'danger'">
              {{ scope.row.is_monitored }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="online_status" label="PING状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.online_status === '在线' ? 'success' : 'danger'">
              {{ scope.row.online_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="monitoring_ip" label="配置的监控IP" sortable></el-table-column>
        <!-- 架构模式列已注释，保留数据库字段作为后期扩展 -->
        <!-- <el-table-column
          prop="architecture_mode"
          label="架构模式"
          sortable
        ></el-table-column> -->
        <el-table-column prop="is_single_point" label="是否单点" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_single_point === '是' ? 'success' : 'danger'">
              {{ scope.row.is_single_point }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="managed_addresses" label="互备主机IP" sortable></el-table-column>

        <!-- <el-table-column
          prop="year_category"
          label="年份分类"
          sortable
        ></el-table-column> -->
        <!-- <el-table-column
          prop="in_monitoring_list"
          label="是否在监控清单中"
          sortable
        ></el-table-column> -->
        <!-- <el-table-column
          prop="pre_monitoring_verified"
          label="监控前期是否验证"
          sortable
        ></el-table-column> -->
        <!-- <el-table-column
          prop="inspection"
          label="抽查"
          sortable
        ></el-table-column> -->
        <el-table-column prop="remarks" label="备注" sortable></el-table-column>
        <el-table-column prop="created_at" label="创建时间" sortable></el-table-column>
        <el-table-column prop="created_by" label="创建人" sortable></el-table-column>
        <el-table-column prop="updated_at" label="更新时间" sortable></el-table-column>
        <el-table-column prop="updated_by" label="更新人" sortable></el-table-column>
        <!-- 可以根据需要添加更多列 -->
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap">
              <el-button size="small" type="warning" :disabled="!hasUpdatePermission"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button size="small" type="danger" :disabled="!hasDeletePermission"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination background :current-page="search.currentPage" :page-size="search.pageSize" :total="search.total"
          :page-sizes="[10, 20, 50, 100, 1000, 10000]" :pager-count="5" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { Plus, Search, Download } from "@element-plus/icons-vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export default {
  components: {
    Plus,
    Search,
    Download,
  },

  data() {
    return {
      userArr: [], // 监控IP列表
      loading: false, // 加载状态
      devicetypes: [], // 数据字典值
      productionattributes: [], //数据字典值
      datacenters: [], //数据字典值
      operationstatus: [], //数据字典值
      usersList: [], // 用户列表
      hasDeletePermission: localStorage.getItem("role_code")?.includes("D"), // 是否有删除权限
      hasUpdatePermission: localStorage.getItem("role_code")?.includes("U"), // 是否有删除权限
      hasInsertPermission: localStorage.getItem("role_code")?.includes("I"), // 是否有删除权限

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false,
        delete: false,
      },

      // 查询数据
      search: {
        management_ip: "",
        out_of_band_management: null,
        hostname: null,
        admin1: null,
        admin2: null,
        device_type: null,
        production_attributes: null,
        data_center: [], // 改为数组支持多选
        operation_status: null,
        is_innovative_tech: null,
        is_monitored: null,
        online_status: null, // PING状态
        year_category: null,
        serial_number: null, // 序列号
        total: 0, // 总记录数
        pageSize: 10, // 每页显示条目数
        currentPage: 1, // 当前页码
        sortProp: "updated_at", // 排序字段
        sortOrder: "desc", // 排序顺序
      },

      // 增删改查表单数据
      formData: {
        id: null,
        management_ip: null,
        out_of_band_management: null,
        hostname: null,
        function_purpose: null,
        admin1: null,
        admin2: null,
        device_type: null,
        production_attributes: null,
        data_center: null,
        operation_status: null,
        asset_number: null,
        purchase_date: null,
        maintenance_years: null,
        maintenance_end_date: null,
        serial_number: null,
        model: null,
        version: null,
        cpu_model: null,
        is_innovative_tech: null,
        is_monitored: null,
        monitoring_ip: null,
        architecture_mode: null,
        is_single_point: null,
        managed_addresses: null,
        remarks: null,
        year_category: null,
        in_monitoring_list: null,
        pre_monitoring_verified: null,
        inspection: null,
        monitoring_requirement: true, // 新增字段：需要监控，默认为true（是）
        monitoring_requirement_description: null, // 新增字段：不监控原因
      },
      // 表单验证规则
      rules: {
        management_ip: [
          { required: true, message: '请输入管理IP', trigger: 'blur' },
          { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
        ],
        hostname: [
          { required: true, message: '请输入主机名', trigger: 'blur' },
          { min: 2, max: 50, message: '主机名长度应在2-50个字符之间', trigger: 'blur' }
        ],
        function_purpose: [
          { required: true, message: '请输入功能用途', trigger: 'blur' }
        ],
        admin1: [
          { required: true, message: '请选择管理员1', trigger: 'change' }
        ],
        device_type: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        production_attributes: [
          { required: true, message: '请选择生产属性', trigger: 'change' }
        ],
        data_center: [
          { required: true, message: '请选择所属机房', trigger: 'change' }
        ],
        operation_status: [
          { required: true, message: '请选择生命周期', trigger: 'change' }
        ],
        is_innovative_tech: [
          { required: true, message: '请选择是否信创', trigger: 'change' }
        ],
        monitoring_requirement: [
          { required: true, message: '请选择需要监控', trigger: 'change' }
        ],
        monitoring_requirement_description: [
          {
            required: false,
            validator: (_, value, callback) => {
              if (this.formData.monitoring_requirement === false) {
                if (!value || value.trim() === '') {
                  callback(new Error('当需要监控为"否"时，不监控原因为必填项'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        managed_addresses: [
          {
            required: false,
            validator: (_, value, callback) => {
              if (this.formData.is_single_point === '否') {
                if (!value) {
                  callback(new Error('当设备不是单点时，互备主机IP为必填项'));
                } else {
                  // 验证IP格式，支持多个IP用英文逗号分隔
                  const ips = value.split(',');
                  const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
                  const invalidIps = ips.filter(ip => !ipPattern.test(ip.trim()));

                  if (invalidIps.length > 0) {
                    callback(new Error('请输入正确的IP地址格式，多个IP请用英文逗号分隔'));
                  } else {
                    callback();
                  }
                }
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
    };
  },

  mounted() {
    // 检查URL参数中是否有search_ip
    if (this.$route.query.search_ip) {
      this.search.management_ip = this.$route.query.search_ip;
    }

    this.loadData();

    this.getDatadict("B", "devicetypes");
    this.getDatadict("C", "productionattributes");
    this.getDatadict("A", "datacenters");
    this.getDatadict("D", "operationstatus");
    this.loadUsersList(); // 加载用户列表

    // 检查是否来自发现结果页面
    if (this.$route.query.from_discovery === 'true') {
      this.$nextTick(() => {
        this.handleAddFromDiscovery();
      });
    }
  },

  methods: {
    // 获取生命周期标签类型（5个状态：正常、故障、闲置、报废、预报废）
    getLifecycleTagType(status) {
      switch (status) {
        case '正常':
          return 'success';    // 绿色 - 设备正常运行
        case '故障':
          return 'danger';     // 红色 - 设备出现故障
        case '闲置':
          return 'info';       // 蓝色 - 设备暂时不使用但可用
        case '报废':
          return 'info';       // 灰色 - 设备已报废，不再使用
        case '预报废':
          return 'warning';    // 橙色 - 设备即将报废
        default:
          // 兼容其他可能的状态表述
          if (status && status.includes('正常')) {
            return 'success';
          }
          if (status && status.includes('故障')) {
            return 'danger';
          }
          if (status && status.includes('闲置')) {
            return 'info';
          }
          if (status && (status.includes('报废') && !status.includes('预'))) {
            return 'info';
          }
          if (status && status.includes('预报废')) {
            return 'warning';
          }
          // 默认为信息色
          return 'info';
      }
    },

    // 页码选择
    handlePageChange(newPage) {
      this.search.currentPage = newPage;
      this.loadData();
    },

    // 每页显示条目数变化
    handlePageSizeChange(newSize) {
      this.search.pageSize = parseInt(newSize);
      this.search.currentPage = 1; // 重置当前页码为第一页
      this.loadData();
    },
    //增加排序
    handleSortChange({ prop, order }) {
      this.search.sortProp = prop;
      this.search.sortOrder = order === "ascending" ? "asc" : "desc";
      this.loadData();
    },

    // 获取数据字典值
    async loadData() {
      try {
        this.loading = true;
        // 获取数据
        const response = await this.$axios.post(
          `/api/get_cmdb_device_management`,
          this.search
        );
        this.userArr = response.data.msg;
        this.search.total = response.data.total;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading = false;
      }
    },

    // 验证并添加管理IP
    async validateAndSubmitAdd() {
      try {
        // 表单验证
        await this.$refs.addFormRef.validate();

        // 验证通过，提交表单
        await this.submitAdd();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 添加管理IP
    async submitAdd() {
      try {
        // 添加username参数，用于记录创建者
        const requestData = {
          ...this.formData,
          username: localStorage.getItem("loginUsername") || "unknown"
        };

        await this.$axios.post(
          `/api/add_cmdb_device_management`,
          requestData
        );
        this.$message.success("添加成功");
        this.dialogVisible.add = false;
        this.loadData();
      } catch (error) {
        console.error("添加失败:", error);
        this.$message.error(error.response?.data?.msg || "添加失败");
      }
    },

    // 验证并编辑管理IP
    async validateAndSubmitEdit() {
      try {
        // 表单验证
        await this.$refs.editFormRef.validate();

        // 验证通过，提交表单
        await this.submitEdit();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 编辑管理IP
    async submitEdit() {
      try {
        // 添加username参数，用于记录更新者
        const requestData = {
          ...this.formData,
          username: localStorage.getItem("loginUsername") || "unknown"
        };

        await this.$axios.post(
          `/api/update_cmdb_device_management`,
          requestData
        );
        this.$message.success("更新成功");
        this.dialogVisible.edit = false;
        this.loadData();
      } catch (error) {
        console.error("更新失败:", error);
        this.$message.error(error.response?.data?.msg || "更新失败");
      }
    },

    // 删除监控IP
    async submitDelete() {
      try {
        await this.$axios.post(
          `/api/del_cmdb_device_management`,
          this.formData
        );
        this.$message.success("删除成功");
        this.loadData();
        this.dialogVisible.delete = false;
      } catch (error) {
        console.error("删除失败:", error);
        this.$message.error("删除失败");
      }
    },

    // 重置搜索条件
    resetSearch() {
      this.search = {
        management_ip: "",
        out_of_band_management: null,
        hostname: null,
        admin1: null,
        admin2: null,
        device_type: null,
        production_attributes: null,
        data_center: [], // 改为数组支持多选
        operation_status: null,
        is_innovative_tech: null,
        is_monitored: null,
        online_status: null, // PING状态
        year_category: null,
        serial_number: null, // 序列号
        monitoring_requirement: null, // 需要监控
        total: 0,
        pageSize: 10,
        currentPage: 1,
        sortProp: "updated_at",
        sortOrder: "desc",
      };
      this.loadData();
    },

    // 得到数据字典
    async getDatadict(dictCode, targetArray) {
      try {
        console.log('=== 前端发送数据字典请求 ===');
        console.log('字典代码:', dictCode);
        console.log('目标数组:', targetArray);

        const requestConfig = {
          withCredentials: true, // 确保携带凭据（Cookie）
        };

        const requestData = {
          dict_code: dictCode,
        };

        console.log('请求配置:', requestConfig);
        console.log('请求数据:', requestData);

        const response = await this.$axios.post(
          `/api/get_cmdb_data_dictionary`,
          requestData,
          requestConfig
        );

        console.log('响应数据:', response.data);
        this[targetArray] = response.data.msg;
      } catch (error) {
        console.error("数据字典加载失败详细信息:", {
          error: error,
          message: error.message,
          response: error.response,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });
        this.$message.error(`数据字典加载失败: ${error.response?.data?.msg || error.message}`);
      }
    },

    // 加载用户列表
    async loadUsersList() {
      try {
        const response = await this.$axios.post('/api/get_all_users_real_name');
        this.usersList = response.data.msg;
        console.log('用户列表加载成功:', this.usersList);
      } catch (error) {
        console.error('用户列表加载失败:', error);
        this.$message.error('用户列表加载失败');
      }
    },

    // 需要监控变化处理
    handleMonitoringRequirementChange(value) {
      // 当需要监控为"是"时，清空不监控原因
      if (value === true) {
        this.formData.monitoring_requirement_description = null;
      }
      // 触发验证
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.validateField('monitoring_requirement_description');
        });
      }
      if (this.$refs.editFormRef) {
        this.$nextTick(() => {
          this.$refs.editFormRef.validateField('monitoring_requirement_description');
        });
      }
    },

    // 新增效果实现
    handleAdd() {
      this.dialogVisible.add = !this.dialogVisible.add;
      this.formData = {
        is_single_point: "否", // 默认值
        is_innovative_tech: "否", // 默认值
        is_monitored: "否", // 默认值
        in_monitoring_list: "否", // 默认值
        inspection: "否", // 默认值
        managed_addresses: "", // 初始化为空字符串
        monitoring_requirement: true, // 默认需要监控为"是"
        monitoring_requirement_description: null // 默认不监控原因为空
      };
      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
          // 由于默认值是"否"，需要立即验证互备主机IP字段
          this.validateManagedAddresses();
        });
      }
    },

    // 编辑按钮实现
    handleEdit(_, row) {
      this.dialogVisible.edit = true;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
      this.formData.out_of_band_management = row.out_of_band_management;
      this.formData.hostname = row.hostname;
      this.formData.function_purpose = row.function_purpose;
      this.formData.admin1 = row.admin1;
      this.formData.admin2 = row.admin2;
      // 设备类型：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.device_type = row.device_type_code || row.device_type;
      // 生产属性：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.production_attributes = row.production_attributes_code || row.production_attributes;
      // 所属机房：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.data_center = row.data_center_code || row.data_center;
      // 生命周期：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.operation_status = row.operation_status_code || row.operation_status;
      this.formData.asset_number = row.asset_number;
      this.formData.purchase_date = row.purchase_date;
      this.formData.maintenance_years = row.maintenance_years;
      this.formData.maintenance_end_date = row.maintenance_end_date;
      this.formData.serial_number = row.serial_number;
      this.formData.model = row.model;
      this.formData.version = row.version;
      this.formData.cpu_model = row.cpu_model;
      this.formData.is_innovative_tech = row.is_innovative_tech;
      // 需要监控字段处理：视图中的"是"/"否"转换为布尔值
      this.formData.monitoring_requirement = row.monitoring_requirement === '是' ? true : false;
      this.formData.monitoring_requirement_description = row.monitoring_requirement_description;
      this.formData.is_monitored = row.is_monitored;
      this.formData.monitoring_ip = row.monitoring_ip;
      this.formData.architecture_mode = row.architecture_mode;
      this.formData.is_single_point = row.is_single_point;
      this.formData.managed_addresses = row.managed_addresses;
      this.formData.remarks = row.remarks;
      this.formData.year_category = row.year_category;
      this.formData.in_monitoring_list = row.in_monitoring_list;
      this.formData.pre_monitoring_verified = row.pre_monitoring_verified;
      this.formData.inspection = row.inspection;

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.editFormRef) {
        this.$nextTick(() => {
          this.$refs.editFormRef.clearValidate();
        });
      }
    },

    // 删除效果实现
    handleDelete(_, row) {
      this.dialogVisible.delete = !this.dialogVisible.delete;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
    },
    //导出数据
    exportData() {
      const table = this.$refs.table; // 获取 el-table 实例
      const columns = table.columns; // 获取表头
      const headers = columns.map((col) => col.label); // 获取表头
      const data = this.userArr.map((row) =>
        columns.map((col) => row[col.property])
      ); // 获取表格数据

      const wsData = [headers, ...data]; // 将表头和数据合并
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const blob = new Blob([wbout], { type: "application/octet-stream" });
      saveAs(blob, "网络设备.xlsx");
    },

    // 处理从发现结果页面传递的参数
    handleAddFromDiscovery() {
      // 打开添加对话框
      this.dialogVisible.add = true;

      // 从 URL 参数中获取数据
      const { ip_address, hostname, open_ports } = this.$route.query;

      // 只填充管理IP
      this.formData = {
        management_ip: ip_address || '',
        hostname: hostname || '',
        function_purpose: '',
        admin1: '',
        admin2: '',
        device_type: '',
        production_attributes: '',
        data_center: '',
        operation_status: '',
        is_innovative_tech: '否', // 默认值为"否"
        is_monitored: '否', // 默认值
        remarks: open_ports ? `开放端口: ${open_ports}` : '',
        is_single_point: '否', // 默认值
        in_monitoring_list: '否', // 默认值
        inspection: '否' // 默认值
      };

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
          // 显示提示消息
          this.$message.info('请完善资产信息并提交');
        });
      }

      // 清除URL参数，避免刷新页面时重复打开对话框
      this.$router.replace({ path: this.$route.path });
    },

    // 验证互备主机IP字段
    validateManagedAddresses() {
      // 当"是否单点"值变化时，重新验证互备主机IP字段
      if (this.$refs.addFormRef) {
        this.$refs.addFormRef.validateField('managed_addresses');
      }
      if (this.$refs.editFormRef) {
        this.$refs.editFormRef.validateField('managed_addresses');
      }

      // 如果选择"是"，清空互备主机IP字段
      if (this.formData.is_single_point === '是') {
        this.formData.managed_addresses = '';
      }
    },
  },
};
</script>

<style scoped>
/* 统一操作按钮区样式 */
.unified-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: nowrap;
}

.action-bar-left,
.action-bar-right {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 按钮容器 */
.button-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

/* 搜索按钮对齐 */
.search-buttons-col {
  display: flex;
  align-items: center;
}

.search-buttons {
  margin-bottom: 0;
  text-align: right;
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-bar-left,
  .action-bar-right {
    margin-bottom: 8px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-bar-right {
    justify-content: flex-end;
  }
}
</style>
