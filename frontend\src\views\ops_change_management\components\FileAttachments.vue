<template>
  <div class="file-attachments-container">
    <div class="attachments-header">
      <el-divider content-position="left">
        <span class="divider-title">附件管理</span>
      </el-divider>
      <el-button type="primary" size="small" class="refresh-button" :loading="refreshLoading"
        @click="handleManualRefresh">
        <el-icon>
          <Refresh />
        </el-icon>
        刷新附件状态
      </el-button>

    </div>

    <!-- 变更模板下载 -->
    <div class="template-download-section">
      <el-card shadow="hover" class="template-card">
        <div class="template-content">
          <div class="template-selector">
            <span class="template-title">变更操作表模板下载</span>
            <el-select v-model="selectedTemplate" placeholder="请选择变更操作表模板" style="width: 50%" size="small" filterable
              clearable>
              <el-option v-for="template in templateOptions" :key="template.id"
                :label="template.is_default ? `${template.template_name} (默认)` : template.template_name"
                :value="template.id">
                <span>{{ template.template_name }}</span>
                <el-tag v-if="template.is_default" type="success" size="small" style="margin-left: 8px;">
                  默认
                </el-tag>
              </el-option>
            </el-select>
            <el-button type="primary" size="small" @click="downloadTemplate" :disabled="!selectedTemplate"
              :loading="templateDownloadLoading" style="margin-left: 10px">
              <el-icon>
                <Download />
              </el-icon>
              下载模板
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <el-row :gutter="20">
      <!-- OA流程文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card" :class="{
          'drag-over': dragStates.oa_process,
          'has-file': changeData.oa_process,
          'uploading': uploadLoading.oa_process
        }" @dragenter.prevent="handleDragEnter('oa_process')" @dragover.prevent="handleDragOver('oa_process')"
          @dragleave.prevent="handleDragLeave('oa_process')" @drop.prevent="handleDrop($event, 'oa_process')"
          @mouseleave="handleCardMouseLeave('oa_process')">
          <template #header>
            <div class="card-header">
              <span>OA流程</span>
              <el-tag v-if="changeData.oa_process" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <!-- 拖拽提示层 -->
            <div v-if="dragStates.oa_process" class="drag-overlay">
              <el-icon class="drag-icon">
                <Upload />
              </el-icon>
              <span class="drag-text">释放文件进行上传</span>
            </div>

            <div v-if="changeData.oa_process" class="file-info">
              <el-icon>
                <Document />
              </el-icon>
              <span class="file-name">{{ getFileName(changeData.oa_process_file) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon>
                <Upload />
              </el-icon>
              <span>请上传OA流程文件</span>
              <div class="drag-hint">
                <el-text type="info" size="small">支持拖拽文件到此区域</el-text>
              </div>
            </div>

            <div class="action-buttons">
              <!-- 一行显示所有按钮 -->
              <div class="button-row">
                <el-button v-if="changeData.oa_process" type="primary" size="small" @click="downloadFile('oa_process')"
                  :loading="downloadLoading.oa_process">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>

                <!-- OA流程预览按钮 -->
                <el-button v-if="changeData.oa_process" type="info" size="small" @click="previewFile('oa_process')"
                  :loading="previewLoading.oa_process">
                  <el-icon>
                    <View />
                  </el-icon>
                  预览
                </el-button>

                <el-upload class="upload-button" :action="uploadUrl" :headers="headers"
                  :data="getUploadData('oa_process')" :show-file-list="false" :before-upload="beforeUpload"
                  :http-request="(options) => customUpload(options, 'oa_process')">
                  <el-button type="success" size="small" :loading="uploadLoading.oa_process">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    {{ changeData.oa_process ? '上传' : '上传' }}
                  </el-button>
                </el-upload>

                <el-button v-if="changeData.oa_process" type="danger" size="small" @click="removeFile('oa_process')"
                  :loading="removeLoading.oa_process">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 签字存档文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card" :class="{
          'drag-over': dragStates.signed_archive,
          'has-file': changeData.signed_archive,
          'uploading': uploadLoading.signed_archive
        }" @dragenter.prevent="handleDragEnter('signed_archive')" @dragover.prevent="handleDragOver('signed_archive')"
          @dragleave.prevent="handleDragLeave('signed_archive')" @drop.prevent="handleDrop($event, 'signed_archive')"
          @mouseleave="handleCardMouseLeave('signed_archive')">
          <template #header>
            <div class="card-header">
              <span>签字存档</span>
              <el-tag v-if="changeData.signed_archive" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <!-- 拖拽提示层 -->
            <div v-if="dragStates.signed_archive" class="drag-overlay">
              <el-icon class="drag-icon">
                <Upload />
              </el-icon>
              <span class="drag-text">释放文件进行上传</span>
            </div>

            <div v-if="changeData.signed_archive" class="file-info">
              <el-icon>
                <Document />
              </el-icon>
              <span class="file-name">{{ getFileName(changeData.signed_archive_file) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon>
                <Upload />
              </el-icon>
              <span>请上传签字存档文件</span>
              <div class="drag-hint">
                <el-text type="info" size="small">支持拖拽文件到此区域</el-text>
              </div>
            </div>

            <div class="action-buttons">
              <!-- 一行显示所有按钮 -->
              <div class="button-row">
                <el-button v-if="changeData.signed_archive" type="primary" size="small"
                  @click="downloadFile('signed_archive')" :loading="downloadLoading.signed_archive">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>

                <!-- 签字存档预览按钮 -->
                <el-button v-if="changeData.signed_archive" type="info" size="small"
                  @click="previewFile('signed_archive')" :loading="previewLoading.signed_archive">
                  <el-icon>
                    <View />
                  </el-icon>
                  预览
                </el-button>

                <el-upload class="upload-button" :action="uploadUrl" :headers="headers"
                  :data="getUploadData('signed_archive')" :show-file-list="false" :before-upload="beforeUpload"
                  :http-request="(options) => customUpload(options, 'signed_archive')">
                  <el-button type="success" size="small" :loading="uploadLoading.signed_archive">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    {{ changeData.signed_archive ? '上传' : '上传' }}
                  </el-button>
                </el-upload>

                <el-button v-if="changeData.signed_archive" type="danger" size="small"
                  @click="removeFile('signed_archive')" :loading="removeLoading.signed_archive">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 变更操作表文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card" :class="{
          'drag-over': dragStates.operation_sheet,
          'has-file': changeData.operation_sheet,
          'uploading': uploadLoading.operation_sheet
        }" @dragenter.prevent="handleDragEnter('operation_sheet')"
          @dragover.prevent="handleDragOver('operation_sheet')" @dragleave.prevent="handleDragLeave('operation_sheet')"
          @drop.prevent="handleDrop($event, 'operation_sheet')" @mouseleave="handleCardMouseLeave('operation_sheet')">
          <template #header>
            <div class="card-header">
              <span>变更操作表</span>
              <el-tag v-if="changeData.operation_sheet" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <!-- 拖拽提示层 -->
            <div v-if="dragStates.operation_sheet" class="drag-overlay">
              <el-icon class="drag-icon">
                <Upload />
              </el-icon>
              <span class="drag-text">释放文件进行上传</span>
            </div>

            <div v-if="changeData.operation_sheet" class="file-info">
              <el-icon>
                <Document />
              </el-icon>
              <span class="file-name">{{ getFileName(changeData.operation_sheet) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon>
                <Upload />
              </el-icon>
              <span>请上传变更操作表文件</span>
              <div class="drag-hint">
                <el-text type="info" size="small">支持拖拽文件到此区域</el-text>
              </div>
            </div>

            <div class="action-buttons">
              <!-- 一行显示所有按钮 -->
              <div class="button-row">
                <el-button v-if="changeData.operation_sheet" type="primary" size="small"
                  @click="downloadFile('operation_sheet')" :loading="downloadLoading.operation_sheet">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>

                <!-- 变更操作表预览按钮 -->
                <el-button v-if="changeData.operation_sheet" type="info" size="small"
                  @click="previewFile('operation_sheet')" :loading="previewLoading.operation_sheet">
                  <el-icon>
                    <View />
                  </el-icon>
                  预览
                </el-button>

                <el-upload class="upload-button" :action="uploadUrl" :headers="headers"
                  :data="getUploadData('operation_sheet')" :show-file-list="false" :before-upload="beforeUpload"
                  :http-request="(options) => customUpload(options, 'operation_sheet')">
                  <el-button type="success" size="small" :loading="uploadLoading.operation_sheet">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    {{ changeData.operation_sheet ? '上传' : '上传' }}
                  </el-button>
                </el-upload>

                <el-button v-if="changeData.operation_sheet" type="danger" size="small"
                  @click="removeFile('operation_sheet')" :loading="removeLoading.operation_sheet">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 补充资料文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card" :class="{
          'drag-over': dragStates.supplementary_material,
          'has-file': changeData.supplementary_material,
          'uploading': uploadLoading.supplementary_material
        }" @dragenter.prevent="handleDragEnter('supplementary_material')"
          @dragover.prevent="handleDragOver('supplementary_material')"
          @dragleave.prevent="handleDragLeave('supplementary_material')"
          @drop.prevent="handleDrop($event, 'supplementary_material')"
          @mouseleave="handleCardMouseLeave('supplementary_material')">
          <template #header>
            <div class="card-header">
              <span>补充资料</span>
              <el-tag v-if="changeData.supplementary_material" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <!-- 拖拽提示层 -->
            <div v-if="dragStates.supplementary_material" class="drag-overlay">
              <el-icon class="drag-icon">
                <Upload />
              </el-icon>
              <span class="drag-text">释放文件进行上传</span>
            </div>

            <div v-if="changeData.supplementary_material" class="file-info">
              <el-icon>
                <Document />
              </el-icon>
              <span class="file-name">{{ getFileName(changeData.supplementary_material) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon>
                <Upload />
              </el-icon>
              <span>请上传补充资料文件</span>
              <div class="drag-hint">
                <el-text type="info" size="small">支持拖拽文件到此区域</el-text>
              </div>
            </div>

            <div class="action-buttons">
              <!-- 一行显示所有按钮 -->
              <div class="button-row">
                <el-button v-if="changeData.supplementary_material" type="primary" size="small"
                  @click="downloadFile('supplementary_material')" :loading="downloadLoading.supplementary_material">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>

                <!-- 补充资料预览按钮 -->
                <el-button v-if="changeData.supplementary_material" type="info" size="small"
                  @click="previewFile('supplementary_material')" :loading="previewLoading.supplementary_material">
                  <el-icon>
                    <View />
                  </el-icon>
                  预览
                </el-button>

                <el-upload class="upload-button" :action="uploadUrl" :headers="headers"
                  :data="getUploadData('supplementary_material')" :show-file-list="false" :before-upload="beforeUpload"
                  :http-request="(options) => customUpload(options, 'supplementary_material')">
                  <el-button type="success" size="small" :loading="uploadLoading.supplementary_material">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    {{ changeData.supplementary_material ? '上传' : '上传' }}
                  </el-button>
                </el-upload>

                <el-button v-if="changeData.supplementary_material" type="danger" size="small"
                  @click="removeFile('supplementary_material')" :loading="removeLoading.supplementary_material">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>

  <!-- 图片放大查看器 -->
  <div v-if="imageViewerVisible" class="image-viewer-overlay" @click="closeImageViewer">
    <div class="image-viewer-container">
      <!-- 工具栏 -->
      <div class="image-viewer-toolbar">
        <div class="toolbar-left">
          <span class="image-title">{{ imageViewerFileName }}</span>
        </div>
        <div class="toolbar-right">
          <div class="zoom-controls">
            <el-tooltip content="缩小" placement="bottom">
              <el-button class="toolbar-btn zoom-btn" @click="zoomOut" :disabled="imageZoom <= 0.5">
                <el-icon size="16">
                  <ZoomOut />
                </el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="重置缩放" placement="bottom">
              <el-button class="toolbar-btn zoom-display" @click="resetImageZoom">
                {{ Math.round(imageZoom * 100) }}%
              </el-button>
            </el-tooltip>
            <el-tooltip content="放大" placement="bottom">
              <el-button class="toolbar-btn zoom-btn" @click="zoomIn" :disabled="imageZoom >= 3">
                <el-icon size="16">
                  <ZoomIn />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
          <div class="action-controls">
            <el-tooltip content="下载图片" placement="bottom">
              <el-button class="toolbar-btn download-btn" @click="downloadCurrentImage">
                <el-icon size="16">
                  <Download />
                </el-icon>
                <span class="btn-text">下载</span>
              </el-button>
            </el-tooltip>
            <el-tooltip content="关闭查看器" placement="bottom">
              <el-button class="toolbar-btn close-btn" @click="closeImageViewer">
                <el-icon size="16">
                  <Close />
                </el-icon>
                <span class="btn-text">关闭</span>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 图片容器 -->
      <div class="image-viewer-content" @click.stop>
        <img :src="imageViewerUrl" :alt="imageViewerFileName" :class="{ 'dragging': isDragging }"
          :style="`transform: ${imageTransform}; transition: ${imageTransition}; max-width: none; max-height: none;`"
          @wheel="handleImageWheel" @mousedown="handleMouseDown" @touchstart="handleTouchStart"
          @touchmove="handleTouchMove" @touchend="handleTouchEnd" draggable="false" />
      </div>
    </div>
  </div>

  <!-- 文件预览对话框 -->
  <el-dialog v-model="previewDialog.visible" :title="''"
    :width="previewDialog.isFullscreen ? '100%' : '90%'" :fullscreen="previewDialog.isFullscreen"
    :before-close="closePreviewDialog" :show-close="false" append-to-body destroy-on-close class="file-preview-dialog"
    :class="{ 'fullscreen-preview': previewDialog.isFullscreen, 'no-header-preview': true }">
    <!-- 浮动工具栏 - 适用于所有预览类型 -->
    <div v-if="previewDialog.isSupported" class="floating-toolbar">
      <el-tooltip :content="previewDialog.isFullscreen ? '退出全屏' : '全屏预览'" placement="bottom">
        <el-button class="toolbar-btn fullscreen-btn" @click="toggleFullscreen" size="small">
          <el-icon size="16">
            <svg v-if="previewDialog.isFullscreen" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
            </svg>
            <svg v-else viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
            </svg>
          </el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="下载文件" placement="bottom">
        <el-button class="toolbar-btn download-btn" @click="downloadFile(previewDialog.currentFileType)" size="small">
          <el-icon size="16">
            <Download />
          </el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="关闭预览" placement="bottom">
        <el-button class="toolbar-btn close-btn" @click="closePreviewDialog" size="small">
          <el-icon size="16">
            <Close />
          </el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <div v-if="previewDialog.isSupported" class="preview-container">
      <!-- PDF预览 -->
      <iframe v-if="previewDialog.fileType === '.pdf'" :src="previewDialog.previewUrl"
        :style="`width: 100%; height: ${previewDialog.isFullscreen ? '100vh' : '70vh'}; border: none;`"
        title="PDF预览"></iframe>

      <!-- Office文档纯前端预览 -->
      <div v-else-if="['.xlsx', '.xls', '.docx'].includes(previewDialog.fileType)" class="office-preview">
        <!-- 预览内容区域 -->
        <div v-loading="officePreviewLoading" element-loading-text="正在加载预览..." class="preview-content-wrapper">
          <!-- Excel预览 -->
          <div v-if="['.xlsx', '.xls'].includes(previewDialog.fileType)" id="excel-preview-container"
            :style="`width: 100%; height: ${previewDialog.isFullscreen ? '100vh' : '70vh'}; border: none; border-radius: 0; overflow: auto; background: white;`">
          </div>
          <!-- Word预览 -->
          <div v-else-if="['.docx'].includes(previewDialog.fileType)" id="word-preview-container"
            :style="`width: 100%; height: ${previewDialog.isFullscreen ? '100vh' : '70vh'}; border: none; border-radius: 0; overflow: auto; padding: 20px; background: white;`">
          </div>
        </div>
      </div>

      <!-- 图片预览 -->
      <div v-else-if="['.jpg', '.jpeg', '.png', '.gif'].includes(previewDialog.fileType)" class="image-preview">
        <div class="image-preview-container" @click="openImageViewer">
          <img :src="previewDialog.previewUrl"
            :style="`max-width: 100%; max-height: ${previewDialog.isFullscreen ? '100vh' : '70vh'}; display: block; margin: 0 auto; cursor: pointer;`"
            alt="图片预览" />

          <div class="image-preview-overlay">
            <el-button type="primary" size="large">
              <el-icon>
                <ZoomIn />
              </el-icon>
              点击放大查看
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 不支持预览的文件类型 -->
    <div v-else class="unsupported-preview">
      <el-empty description=" ">
        <template #image>
          <el-icon size="60" color="#409eff">
            <Document />
          </el-icon>
        </template>
        <div style="margin-top: 20px;">
          <p style="font-size: 16px; color: #606266; margin-bottom: 15px;">
            {{ previewDialog.message }}
          </p>
          <el-button type="primary" @click="downloadFile(previewDialog.currentFileType)">
            <el-icon>
              <Download />
            </el-icon>
            立即下载
          </el-button>
        </div>
      </el-empty>
    </div>


  </el-dialog>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Download, Upload, Delete, Refresh, View, Warning, ZoomIn, ZoomOut, Close } from '@element-plus/icons-vue'
import SimpleImageViewer from '@/components/SimpleImageViewer.vue'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import { previewExcel, previewWord, getFileArrayBuffer, isSupportedClientPreview } from '@/utils/office-preview'
import { useImageViewer, imageViewerStyles } from '@/utils/ImageViewerMixin.js'

export default {
  name: 'FileAttachments',
  components: {
    Document,
    Download,
    Upload,
    Delete,
    Refresh,
    View,
    Warning,
    ZoomIn,
    ZoomOut,
    Close,
    SimpleImageViewer
  },
  props: {
    changeData: {
      type: Object,
      required: true
    },
    refreshChangeData: {
      type: Function,
      default: null
    }
  },
  emits: ['update:changeData'],
  setup(props, { emit }) {
    // 上传URL
    const uploadUrl = '/api/upload_ops_change_file_simple'

    // 请求头（包含token）
    const headers = {
      Authorization: `Bearer ${getToken()}`
    }

    // 加载状态
    const uploadLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    const downloadLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    const removeLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    // 预览加载状态
    const previewLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    // 预览对话框状态
    const previewDialog = reactive({
      visible: false,
      title: '',
      fileType: '',
      previewUrl: '',
      isSupported: false,
      message: '',
      currentFileType: '', // 添加当前预览的文件类型追踪
      previewType: null,
      previewOptions: null,
      isFullscreen: false, // 添加全屏状态
      excelInfo: null // 添加Excel文件信息
    })

    // 刷新加载状态
    const refreshLoading = ref(false)

    // 使用图片查看器工具函数
    const {
      imageViewerVisible,
      imageViewerUrl,
      imageViewerFileName,
      imageZoom,
      isDragging,
      imagePosition,
      openImageViewer: openImageViewerBase,
      closeImageViewer,
      zoomIn,
      zoomOut,
      resetImageZoom,
      handleImageWheel,
      handleMouseDown,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      downloadCurrentImage: downloadCurrentImageBase
    } = useImageViewer()

    // 打开图片查看器（适配当前组件）
    const openImageViewer = () => {
      openImageViewerBase(previewDialog)
    }

    // 下载当前图片（适配当前组件）
    const downloadCurrentImage = () => {
      downloadCurrentImageBase(() => {
        if (previewDialog.currentFileType) {
          downloadFile(previewDialog.currentFileType)
        }
      })
    }

    // 计算属性：安全访问图片位置
    const imageTransform = computed(() => {
      const position = imagePosition.value || { x: 0, y: 0 }
      const zoom = imageZoom.value || 1
      return `scale(${zoom}) translate(${position.x}px, ${position.y}px)`
    })

    // 计算属性：图片过渡效果
    const imageTransition = computed(() => {
      return isDragging.value ? 'none' : 'transform 0.3s ease'
    })



    // 拖拽状态管理
    const dragStates = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    // 拖拽计数器，用于解决事件冲突导致的闪烁问题
    const dragCounters = reactive({
      oa_process: 0,
      signed_archive: 0,
      operation_sheet: 0,
      supplementary_material: 0
    })

    // 防抖定时器
    const dragTimers = reactive({
      oa_process: null,
      signed_archive: null,
      operation_sheet: null,
      supplementary_material: null
    })

    // 模板相关数据
    const selectedTemplate = ref('')
    const templateOptions = ref([])
    const templateDownloadLoading = ref(false)

    // 获取上传数据
    const getUploadData = (fileType) => {
      return {
        changeId: props.changeData.change_id,
        fileType: fileType,
        username: localStorage.getItem('loginUsername') || 'unknown'
      }
    }

    // 从文件路径中提取文件名
    const getFileName = (filePath) => {
      if (!filePath) return ''
      return filePath.split('/').pop()
    }

    // 上传前验证
    const beforeUpload = (file) => {
      // 文件类型验证
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ]

      if (!allowedTypes.includes(file.type)) {
        ElMessage.error('文件类型不支持，请上传PDF、Word、Excel或图片文件')
        return false
      }

      // 文件大小验证（20MB）
      const maxSize = 20 * 1024 * 1024
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超过20MB')
        return false
      }

      return true
    }

    // 刷新变更详情数据
    const refreshChangeData = async (isRetry = false, forceUpdate = false) => {
      try {
        console.log(`开始${isRetry ? '重试' : ''}刷新变更详情数据...${forceUpdate ? '(强制更新模式)' : ''}`)
        console.log('当前变更ID:', props.changeData.id)

        if (!props.changeData.id) {
          console.log('变更ID为空，无法刷新数据')
          return false
        }

        console.log('发送请求获取最新变更数据...')

        // 添加时间戳参数，避免缓存
        const timestamp = new Date().getTime()

        // 使用request函数而不是fetch，确保与其他API请求保持一致
        const requestData = {
          id: props.changeData.id,
          _t: timestamp // 添加时间戳参数，避免缓存
        }
        console.log('请求参数:', requestData)

        // 导入request函数
        const { default: request } = await import('@/utils/request')

        const response = await request({
          url: '/api/get_ops_change_management',
          method: 'post',
          data: requestData,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })

        console.log('获取变更数据响应:', JSON.stringify(response, null, 2))

        if (response.code === 0 && response.msg && response.msg.length > 0) {
          const freshData = response.msg[0]
          console.log('获取到的最新数据:', JSON.stringify(freshData, null, 2))
          console.log('附件相关字段:',
            '附件1(OA流程):', freshData.oa_process, freshData.oa_process_file,
            '附件2(签字存档):', freshData.signed_archive, freshData.signed_archive_file,
            '附件3(操作表):', freshData.operation_sheet,
            '附件4(补充资料):', freshData.supplementary_material
          )

          // 创建新的变更数据对象，保留原有数据，更新附件相关字段
          const newChangeData = { ...props.changeData }

          // 更新附件相关字段
          newChangeData.oa_process = freshData.oa_process
          newChangeData.oa_process_file = freshData.oa_process_file
          newChangeData.signed_archive = freshData.signed_archive
          newChangeData.signed_archive_file = freshData.signed_archive_file
          newChangeData.operation_sheet = freshData.operation_sheet
          newChangeData.supplementary_material = freshData.supplementary_material

          console.log('更新前的数据:', {
            oa_process: props.changeData.oa_process,
            oa_process_file: props.changeData.oa_process_file,
            signed_archive: props.changeData.signed_archive,
            signed_archive_file: props.changeData.signed_archive_file,
            operation_sheet: props.changeData.operation_sheet,
            supplementary_material: props.changeData.supplementary_material
          })
          console.log('更新后的数据:', {
            oa_process: newChangeData.oa_process,
            oa_process_file: newChangeData.oa_process_file,
            signed_archive: newChangeData.signed_archive,
            signed_archive_file: newChangeData.signed_archive_file,
            operation_sheet: newChangeData.operation_sheet,
            supplementary_material: newChangeData.supplementary_material
          })

          // 检查数据是否有变化
          const hasChanged =
            props.changeData.oa_process !== newChangeData.oa_process ||
            props.changeData.oa_process_file !== newChangeData.oa_process_file ||
            props.changeData.signed_archive !== newChangeData.signed_archive ||
            props.changeData.signed_archive_file !== newChangeData.signed_archive_file ||
            props.changeData.operation_sheet !== newChangeData.operation_sheet ||
            props.changeData.supplementary_material !== newChangeData.supplementary_material

          console.log('数据是否有变化:', hasChanged)
          console.log('是否强制更新:', forceUpdate)

          // 如果数据有变化或者强制更新模式
          if (hasChanged || forceUpdate) {
            console.log(hasChanged ? '数据有变化，更新组件数据' : '强制更新模式，更新组件数据')

            // 创建一个全新的对象，确保Vue能够检测到变化
            const completelyNewData = JSON.parse(JSON.stringify(newChangeData))

            // 更新组件数据
            emit('update:changeData', completelyNewData)

            // 显示刷新成功消息
            if (!isRetry) {
              ElMessage.success(hasChanged ? '附件状态已更新' : '刷新成功')
            }

            return true
          } else {
            console.log('数据没有变化，无需更新')
            return false
          }
        } else {
          console.log('获取变更数据失败或数据为空')
          return false
        }
      } catch (error) {
        console.error('刷新变更详情数据失败:', error)
        return false
      }
    }

    // 上传成功处理
    const handleUploadSuccess = async (response, fileType) => {
      try {
        console.log('上传成功处理函数被调用')
        console.log('上传响应:', response)

        if (response.code === 0) {
          ElMessage.success('文件上传成功')

          // 更新组件数据
          const newChangeData = { ...props.changeData }

          if (fileType === 'oa_process') {
            newChangeData.oa_process = true
            newChangeData.oa_process_file = response.msg.path
          } else if (fileType === 'signed_archive') {
            newChangeData.signed_archive = true
            newChangeData.signed_archive_file = response.msg.path
          } else if (fileType === 'operation_sheet') {
            newChangeData.operation_sheet = response.msg.path
          } else if (fileType === 'supplementary_material') {
            newChangeData.supplementary_material = response.msg.path
          }

          console.log('更新前的数据:', props.changeData)
          console.log('更新后的数据:', newChangeData)

          emit('update:changeData', newChangeData)

          console.log('组件数据已更新，准备刷新变更详情数据...')

          // 延迟三秒后刷新，确保后端数据已完全更新
          ElMessage.info('正在更新附件状态，请稍候...')
          setTimeout(async () => {
            console.log('开始延迟刷新...')
            try {
              // 优先使用父组件的刷新方法
              if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
                console.log('使用父组件的刷新方法')
                const result = await props.refreshChangeData()

                if (result) {
                  console.log('父组件刷新成功')
                } else {
                  console.log('父组件刷新失败或无变化，尝试第二次刷新')

                  // 如果第一次刷新未获取到新数据，再延迟两秒进行第二次刷新
                  setTimeout(async () => {
                    console.log('开始第二次刷新...')
                    try {
                      const secondResult = await props.refreshChangeData()
                      console.log('第二次刷新完成，结果:', secondResult)

                      if (!secondResult) {
                        console.log('两次刷新均未获取到更新的数据，可能需要手动刷新')
                        ElMessage.info('附件状态可能需要手动刷新查看')
                      }
                    } catch (error) {
                      console.error('第二次刷新出错:', error)
                    }
                  }, 2000)
                }
              } else {
                console.log('使用组件内部的刷新方法')
                // 第一次刷新
                const firstRefreshResult = await refreshChangeData(false, false)
                console.log('第一次刷新完成，结果:', firstRefreshResult)

                if (!firstRefreshResult) {
                  // 如果第一次刷新没有获取到更新的数据，再延迟两秒进行第二次刷新
                  setTimeout(async () => {
                    console.log('开始第二次刷新...')
                    try {
                      const secondRefreshResult = await refreshChangeData(true, false)
                      console.log('第二次刷新完成，结果:', secondRefreshResult)

                      if (!secondRefreshResult) {
                        // 如果第二次刷新仍然没有获取到更新的数据，再延迟三秒进行第三次刷新，使用强制更新模式
                        setTimeout(async () => {
                          console.log('开始第三次刷新(强制更新模式)...')
                          try {
                            const thirdRefreshResult = await refreshChangeData(true, true)
                            console.log('第三次刷新完成，结果:', thirdRefreshResult)

                            if (!thirdRefreshResult) {
                              console.log('三次刷新均未获取到更新的数据，可能需要手动刷新页面')
                              ElMessage.info('附件状态可能需要手动刷新查看')

                              // 强制重新渲染组件
                              setTimeout(() => {
                                const newChangeData = JSON.parse(JSON.stringify(props.changeData))
                                emit('update:changeData', newChangeData)
                              }, 100)
                            }
                          } catch (refreshError3) {
                            console.error('第三次刷新出错:', refreshError3)
                          }
                        }, 3000)
                      }
                    } catch (refreshError2) {
                      console.error('第二次刷新出错:', refreshError2)
                    }
                  }, 2000)
                }
              }
            } catch (refreshError) {
              console.error('第一次刷新出错:', refreshError)
            }
          }, 3000)
        } else {
          ElMessage.error(`上传失败: ${response.msg}`)
        }
      } catch (error) {
        console.error('上传成功处理函数出错:', error)
      } finally {
        // 重置加载状态
        uploadLoading[fileType] = false
      }
    }

    // 自定义上传方法
    const customUpload = async (options, fileType) => {
      try {
        // 设置加载状态
        uploadLoading[fileType] = true

        console.log('开始上传文件:', fileType)
        console.log('文件信息:', options.file)
        console.log('变更ID:', props.changeData.change_id)

        if (!props.changeData.change_id) {
          throw new Error('变更ID不能为空，请先保存变更信息')
        }

        // 创建FormData对象，只包含文件
        const formData = new FormData()
        formData.append('file', options.file)

        // 构建URL查询参数
        const username = localStorage.getItem('loginUsername') || 'unknown'
        const queryParams = new URLSearchParams({
          changeId: props.changeData.change_id,
          fileType: fileType,
          username: username
        }).toString()

        console.log('表单数据:', {
          changeId: props.changeData.change_id,
          fileType: fileType,
          username: username
        })

        // 发送请求，使用URL查询参数传递变更ID和文件类型
        const response = await fetch(`${uploadUrl}?${queryParams}`, {
          method: 'POST',
          headers: {
            'Authorization': headers.Authorization
          },
          body: formData
        })

        // 解析响应
        const data = await response.json()

        // 处理响应
        if (response.ok) {
          handleUploadSuccess(data, fileType)
        } else {
          throw new Error(data.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传错误:', error)
        ElMessage.error(`上传失败: ${error.message}`)

        // 重置加载状态
        uploadLoading[fileType] = false
      }
    }

    // 上传错误处理
    const handleUploadError = (error) => {
      console.error('上传错误:', error)
      ElMessage.error('文件上传失败，请稍后重试')

      // 重置所有加载状态
      Object.keys(uploadLoading).forEach(key => {
        uploadLoading[key] = false
      })
    }

    // 拖拽处理方法 - 优化版本，解决闪烁问题
    const handleDragOver = (fileType) => {
      if (uploadLoading[fileType]) return

      // 清除之前的定时器
      if (dragTimers[fileType]) {
        clearTimeout(dragTimers[fileType])
        dragTimers[fileType] = null
      }

      // 增加计数器
      dragCounters[fileType]++

      // 设置拖拽状态
      if (!dragStates[fileType]) {
        dragStates[fileType] = true
      }
    }

    const handleDragLeave = (fileType) => {
      // 减少计数器
      dragCounters[fileType]--

      // 使用更短的防抖时间，更快地响应拖拽离开
      dragTimers[fileType] = setTimeout(() => {
        if (dragCounters[fileType] <= 0) {
          dragStates[fileType] = false
          dragCounters[fileType] = 0 // 重置计数器
        }
      }, 30) // 减少到30ms防抖时间
    }

    const handleDragEnter = (fileType) => {
      if (uploadLoading[fileType]) return

      // 清除可能存在的离开定时器
      if (dragTimers[fileType]) {
        clearTimeout(dragTimers[fileType])
        dragTimers[fileType] = null
      }

      dragCounters[fileType]++
      dragStates[fileType] = true
    }

    const handleDrop = async (event, fileType) => {
      // 立即清理拖拽状态
      dragStates[fileType] = false
      dragCounters[fileType] = 0

      if (dragTimers[fileType]) {
        clearTimeout(dragTimers[fileType])
        dragTimers[fileType] = null
      }

      if (uploadLoading[fileType]) {
        ElMessage.warning('正在上传中，请稍候...')
        return
      }

      const files = event.dataTransfer.files
      if (files.length === 0) {
        ElMessage.warning('未检测到文件')
        return
      }

      if (files.length > 1) {
        ElMessage.warning('一次只能上传一个文件')
        return
      }

      const file = files[0]
      console.log('拖拽上传文件:', file.name, '类型:', file.type, '大小:', file.size)

      // 文件验证
      if (!beforeUpload(file)) {
        return
      }

      // 创建模拟的upload选项对象
      const options = {
        file: file,
        filename: file.name
      }

      // 调用上传方法
      await customUpload(options, fileType)
    }

    // 下载文件
    const downloadFile = async (fileType) => {
      try {
        downloadLoading[fileType] = true

        // 使用直接下载方式，类似变更模板下载
        const downloadUrl = `/api/download_ops_change_file?changeId=${props.changeData.change_id}&fileType=${fileType}&direct=true`

        // 创建一个隐藏的a标签并模拟点击下载
        const link = document.createElement('a')
        link.href = downloadUrl
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('文件下载成功')
      } catch (error) {
        console.error('下载错误:', error)
        ElMessage.error(`文件下载失败: ${error.message}`)
      } finally {
        downloadLoading[fileType] = false
      }
    }

    // 预览文件
    const previewFile = async (fileType) => {
      try {
        previewLoading[fileType] = true

        const response = await fetch(`/api/preview_ops_change_file?changeId=${props.changeData.change_id}&fileType=${fileType}`)

        if (response.ok) {
          const contentType = response.headers.get('content-type')

          // 检查是否是JSON响应（表示不支持预览）
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            if (data.code === 0) {
              previewDialog.visible = true
              previewDialog.title = `文件预览 - ${data.msg.fileName}`
              previewDialog.fileType = data.msg.fileType
              previewDialog.isSupported = data.msg.previewSupported
              previewDialog.message = data.msg.message
              previewDialog.previewUrl = data.msg.previewUrl || ''
              previewDialog.currentFileType = fileType
              previewDialog.previewType = data.msg.previewType || null
              previewDialog.previewOptions = data.msg.previewOptions || null
              previewDialog.excelInfo = data.msg.excelInfo || null

              // 如果后端选择了纯前端预览方案，自动执行预览
              if (data.msg.previewType === 'client_side') {
                console.log('后端选择了纯前端预览方案，自动执行预览')
                // 延迟执行，确保DOM已经渲染
                setTimeout(() => {
                  switchPreviewMethod('client_side')
                }, 100)
              }
            } else {
              ElMessage.error(data.msg || '获取文件信息失败')
            }
          } else {
            // 直接预览的文件类型（PDF、图片等）
            const fileName = getFileName(getFileFieldByType(fileType))
            const blob = await response.blob()
            const url = URL.createObjectURL(blob)

            previewDialog.visible = true
            previewDialog.title = `文件预览 - ${fileName}`
            previewDialog.fileType = getFileExtension(fileName)
            previewDialog.isSupported = true
            previewDialog.previewUrl = url
            previewDialog.message = ''
            previewDialog.currentFileType = fileType
            previewDialog.previewType = null
          }
        } else {
          ElMessage.error('文件预览失败')
        }
      } catch (error) {
        console.error('预览错误:', error)
        ElMessage.error(`文件预览失败: ${error.message}`)
      } finally {
        previewLoading[fileType] = false
      }
    }

    // 关闭预览对话框
    const closePreviewDialog = () => {
      if (previewDialog.previewUrl) {
        URL.revokeObjectURL(previewDialog.previewUrl)
      }
      previewDialog.visible = false
      previewDialog.title = ''
      previewDialog.fileType = ''
      previewDialog.previewUrl = ''
      previewDialog.isSupported = false
      previewDialog.message = ''
      previewDialog.currentFileType = ''
      previewDialog.previewType = null
      previewDialog.isFullscreen = false // 重置全屏状态
      previewDialog.excelInfo = null // 重置Excel信息
    }

    // 切换全屏模式
    const toggleFullscreen = () => {
      previewDialog.isFullscreen = !previewDialog.isFullscreen
    }

    // 根据文件类型获取对应的文件字段
    const getFileFieldByType = (fileType) => {
      switch (fileType) {
        case 'oa_process':
          return props.changeData.oa_process_file
        case 'signed_archive':
          return props.changeData.signed_archive_file
        case 'operation_sheet':
          return props.changeData.operation_sheet
        case 'supplementary_material':
          return props.changeData.supplementary_material
        default:
          return ''
      }
    }

    // 获取文件扩展名
    const getFileExtension = (fileName) => {
      if (!fileName) return ''
      const lastDot = fileName.lastIndexOf('.')
      return lastDot !== -1 ? fileName.substring(lastDot).toLowerCase() : ''
    }

    // 删除文件
    const removeFile = (fileType) => {
      ElMessageBox.confirm(
        '确定要删除此文件吗？此操作不可恢复。',
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          removeLoading[fileType] = true

          const response = await fetch('/api/remove_ops_change_file', {
            method: 'POST',
            headers: {
              ...headers,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              changeId: props.changeData.change_id,
              fileType: fileType,
              username: localStorage.getItem('loginUsername') || 'unknown'
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.msg || '删除失败')
          }

          const data = await response.json()

          if (data.code === 0) {
            ElMessage.success('文件删除成功')

            console.log('文件删除成功，准备更新组件数据')

            // 更新组件数据
            const newChangeData = { ...props.changeData }

            if (fileType === 'oa_process') {
              newChangeData.oa_process = false
              newChangeData.oa_process_file = null
            } else if (fileType === 'signed_archive') {
              newChangeData.signed_archive = false
              newChangeData.signed_archive_file = null
            } else if (fileType === 'operation_sheet') {
              newChangeData.operation_sheet = null
            } else if (fileType === 'supplementary_material') {
              newChangeData.supplementary_material = null
            }

            console.log('更新前的数据:', props.changeData)
            console.log('更新后的数据:', newChangeData)

            emit('update:changeData', newChangeData)

            console.log('组件数据已更新，准备刷新变更详情数据...')

            // 延迟三秒后刷新，确保后端数据已完全更新
            ElMessage.info('正在更新附件状态，请稍候...')
            setTimeout(async () => {
              console.log('开始延迟刷新...')
              try {
                // 优先使用父组件的刷新方法
                if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
                  console.log('使用父组件的刷新方法')
                  const result = await props.refreshChangeData()

                  if (result) {
                    console.log('父组件刷新成功')
                  } else {
                    console.log('父组件刷新失败或无变化，尝试第二次刷新')

                    // 如果第一次刷新未获取到新数据，再延迟两秒进行第二次刷新
                    setTimeout(async () => {
                      console.log('开始第二次刷新...')
                      try {
                        const secondResult = await props.refreshChangeData()
                        console.log('第二次刷新完成，结果:', secondResult)

                        if (!secondResult) {
                          console.log('两次刷新均未获取到更新的数据，可能需要手动刷新')
                          ElMessage.info('附件状态可能需要手动刷新查看')
                        }
                      } catch (error) {
                        console.error('第二次刷新出错:', error)
                      }
                    }, 2000)
                  }
                } else {
                  console.log('使用组件内部的刷新方法')
                  // 第一次刷新
                  const firstRefreshResult = await refreshChangeData(false, false)
                  console.log('第一次刷新完成，结果:', firstRefreshResult)

                  if (!firstRefreshResult) {
                    // 如果第一次刷新没有获取到更新的数据，再延迟两秒进行第二次刷新
                    setTimeout(async () => {
                      console.log('开始第二次刷新...')
                      try {
                        const secondRefreshResult = await refreshChangeData(true, false)
                        console.log('第二次刷新完成，结果:', secondRefreshResult)

                        if (!secondRefreshResult) {
                          // 如果第二次刷新仍然没有获取到更新的数据，再延迟三秒进行第三次刷新，使用强制更新模式
                          setTimeout(async () => {
                            console.log('开始第三次刷新(强制更新模式)...')
                            try {
                              const thirdRefreshResult = await refreshChangeData(true, true)
                              console.log('第三次刷新完成，结果:', thirdRefreshResult)

                              if (!thirdRefreshResult) {
                                console.log('三次刷新均未获取到更新的数据，可能需要手动刷新页面')
                                ElMessage.info('附件状态可能需要手动刷新查看')

                                // 强制重新渲染组件
                                setTimeout(() => {
                                  const newChangeData = JSON.parse(JSON.stringify(props.changeData))
                                  emit('update:changeData', newChangeData)
                                }, 100)
                              }
                            } catch (refreshError3) {
                              console.error('第三次刷新出错:', refreshError3)
                            }
                          }, 3000)
                        }
                      } catch (refreshError2) {
                        console.error('第二次刷新出错:', refreshError2)
                      }
                    }, 2000)
                  }
                }
              } catch (refreshError) {
                console.error('第一次刷新出错:', refreshError)
              }
            }, 3000)
          } else {
            throw new Error(data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除错误:', error)
          ElMessage.error(`文件删除失败: ${error.message}`)
        } finally {
          removeLoading[fileType] = false
        }
      }).catch(() => {
        // 用户取消删除操作
      })
    }

    // 手动刷新方法
    const handleManualRefresh = async () => {
      try {
        refreshLoading.value = true
        ElMessage.info('正在刷新附件状态...')

        // 优先使用父组件的刷新方法
        if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
          console.log('使用父组件的刷新方法')
          const result = await props.refreshChangeData()

          if (result) {
            console.log('父组件刷新成功')
          } else {
            console.log('父组件刷新失败或无变化')
            ElMessage.info('刷新完成，未检测到附件状态变化')
          }
        } else {
          console.log('使用组件内部的刷新方法')
          // 使用强制更新模式，确保组件数据被更新
          const result = await refreshChangeData(false, true)

          if (result) {
            // 消息已在refreshChangeData中显示
          } else {
            ElMessage.info('刷新完成，未检测到附件状态变化')
          }

          // 强制重新渲染组件
          setTimeout(() => {
            const newChangeData = JSON.parse(JSON.stringify(props.changeData))
            emit('update:changeData', newChangeData)
          }, 100)
        }
      } catch (error) {
        console.error('手动刷新出错:', error)
        ElMessage.error('刷新失败，请稍后重试')
      } finally {
        refreshLoading.value = false
      }
    }



    // 获取模板列表
    const getTemplateList = async () => {
      try {
        const response = await request({
          url: '/api/get_ops_change_templates',
          method: 'post',
          data: {
            currentPage: 1,
            pageSize: 100 // 获取所有模板
          }
        })

        if (response.code === 0) {
          templateOptions.value = response.msg || []
          console.log('获取模板列表成功:', templateOptions.value)

          // 自动选择默认模板
          const defaultTemplate = templateOptions.value.find(template => template.is_default === true)
          if (defaultTemplate) {
            selectedTemplate.value = defaultTemplate.id
            console.log('自动选择默认模板:', defaultTemplate.template_name)
          }
        } else {
          console.error('获取模板列表失败:', response.msg)
        }
      } catch (error) {
        console.error('获取模板列表失败:', error)
      }
    }

    // 下载模板
    const downloadTemplate = async () => {
      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择要下载的模板')
        return
      }

      try {
        templateDownloadLoading.value = true
        console.log('开始下载模板，ID:', selectedTemplate.value)

        // 获取选中的模板信息
        const selectedTemplateInfo = templateOptions.value.find(t => t.id === selectedTemplate.value)
        if (!selectedTemplateInfo) {
          ElMessage.error('未找到选中的模板')
          return
        }

        // 直接下载文件
        const downloadUrl = `/api/download_ops_change_template?id=${selectedTemplate.value}&direct=true`

        // 创建一个隐藏的a标签并模拟点击下载
        const link = document.createElement('a')
        link.href = downloadUrl
        link.target = '_blank'
        link.download = selectedTemplateInfo.original_filename || selectedTemplateInfo.template_name

        // 设置请求头
        const token = getToken()
        if (token) {
          // 对于直接下载，我们需要在URL中包含token或使用其他方式
          // 这里我们使用window.open的方式
          window.open(downloadUrl, '_blank')
        } else {
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }

        ElMessage.success(`模板 "${selectedTemplateInfo.template_name}" 下载成功`)

        // 清空选择
        selectedTemplate.value = ''
      } catch (error) {
        console.error('下载模板失败:', error)
        ElMessage.error('模板下载失败，请稍后重试')
      } finally {
        templateDownloadLoading.value = false
      }
    }

    // 清理所有拖拽定时器
    const clearAllDragTimers = () => {
      Object.keys(dragTimers).forEach(fileType => {
        if (dragTimers[fileType]) {
          clearTimeout(dragTimers[fileType])
          dragTimers[fileType] = null
        }
      })
    }

    // 清理所有拖拽状态
    const clearAllDragStates = () => {
      Object.keys(dragStates).forEach(fileType => {
        dragStates[fileType] = false
        dragCounters[fileType] = 0
      })
      clearAllDragTimers()
    }

    // 处理卡片鼠标离开事件
    const handleCardMouseLeave = (fileType) => {
      setTimeout(() => {
        if (!uploadLoading[fileType]) {
          dragStates[fileType] = false
          dragCounters[fileType] = 0
        }
      }, 100)
    }

    // 全局拖拽结束事件处理
    const handleGlobalDragEnd = () => {
      // 延迟清理，确保拖拽真正结束
      setTimeout(() => {
        clearAllDragStates()
      }, 100)
    }

    // 鼠标离开窗口事件处理
    const handleMouseLeave = () => {
      // 当鼠标离开整个浏览器窗口时，清理所有拖拽状态
      setTimeout(() => {
        clearAllDragStates()
      }, 200)
    }

    // Office预览加载成功处理
    const handleOfficePreviewLoad = () => {
      console.log('Office文档预览加载成功')
    }

    // Office预览错误处理
    const handleOfficePreviewError = () => {
      console.log('Office文档预览加载失败，可能是网络问题或文档格式问题')
      ElMessage.warning('Office在线预览服务暂时无法访问，建议下载后查看')
    }

    // Office预览加载状态
    const officePreviewLoading = ref(false)

    // 切换预览方案
    const switchPreviewMethod = async (method) => {
      if (!previewDialog.previewOptions) return

      // 如果是fallback方案，显示下载提示
      if (method === 'fallback') {
        previewDialog.previewType = method
        previewDialog.previewUrl = ''
        previewDialog.isSupported = false

        // 根据是否为多sheet Excel显示不同的消息
        if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
          previewDialog.message = `该Excel文件包含${previewDialog.excelInfo.sheetCount}个工作表，需要下载后查看以完整浏览所有工作表`
        } else {
          previewDialog.message = 'Office文档需要下载后查看'
        }

        ElMessage.info('已切换到下载查看模式')
        return
      }

      // 如果是纯前端预览方案
      if (method === 'client_side') {
        officePreviewLoading.value = true
        previewDialog.previewType = method
        previewDialog.isSupported = true

        try {
          // 检查文件类型是否支持纯前端预览
          if (!isSupportedClientPreview(previewDialog.fileType)) {
            throw new Error('该文件格式暂不支持纯前端预览')
          }

          // 获取文件下载URL
          const downloadUrl = `/api/download_ops_change_file?changeId=${props.changeData.change_id}&fileType=${previewDialog.currentFileType}&direct=true`

          // 获取文件数据
          const arrayBuffer = await getFileArrayBuffer(downloadUrl)

          // 根据文件类型进行预览
          if (['.xlsx', '.xls'].includes(previewDialog.fileType)) {
            await previewExcel(arrayBuffer, 'excel-preview-container')
            ElMessage.success('Excel文件预览加载成功，支持多工作表切换')
          } else if (['.docx'].includes(previewDialog.fileType)) {
            await previewWord(arrayBuffer, 'word-preview-container')
            ElMessage.info('Word文档预览功能开发中，建议使用其他预览方案')
          }

          previewDialog.message = '正在使用纯前端预览，无需第三方服务支持'

        } catch (error) {
          console.error('纯前端预览失败:', error)
          ElMessage.error(`纯前端预览失败: ${error.message}`)

          // 预览失败时显示错误信息
          const containerId = ['.xlsx', '.xls'].includes(previewDialog.fileType) ? 'excel-preview-container' : 'word-preview-container'
          const container = document.getElementById(containerId)
          if (container) {
            container.innerHTML = `
              <div style="text-align: center; padding: 40px; color: #f56c6c;">
                <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                <h3 style="margin-bottom: 10px;">预览失败</h3>
                <p style="margin-bottom: 20px;">${error.message}</p>
                <p style="font-size: 14px; color: #909399;">
                  请尝试其他预览方案或下载文件查看
                </p>
              </div>
            `
          }
        } finally {
          officePreviewLoading.value = false
        }
        return
      }

      // 检查第三方服务方案是否可用
      const selectedUrl = previewDialog.previewOptions[method]
      if (!selectedUrl) {
        let warningMsg = '该预览方案暂不可用，请联系管理员配置相关服务'

        // 预留扩展空间

        ElMessage.warning(warningMsg)
        return
      }

      officePreviewLoading.value = true
      previewDialog.previewType = method
      previewDialog.previewUrl = selectedUrl
      previewDialog.isSupported = true

      // 更新提示信息
      const methodNames = {
        office_online: 'Microsoft Office Online',
        google_docs: 'Google Docs Viewer',
        onlyoffice: 'ONLYOFFICE Document Server',
        libreoffice_online: 'LibreOffice Online'
      }

      let message = `正在使用${methodNames[method]}预览...`

      // 为多sheet Excel添加特别说明
      if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
        message += `\n该Excel文件包含${previewDialog.excelInfo.sheetCount}个工作表：${previewDialog.excelInfo.sheetNames.join(', ')}`
      }

      previewDialog.message = message

      // 根据预览类型给出不同的提示
      if (method === 'office_online') {
        let successMsg = `已切换到 ${methodNames[method]} 预览方案（推荐）`
        if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
          successMsg += `，可完整查看所有${previewDialog.excelInfo.sheetCount}个工作表`
        }
        ElMessage.success(successMsg)
      } else if (method === 'google_docs') {
        let successMsg = `已切换到 ${methodNames[method]} 预览方案（备选）`
        if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
          successMsg += `，支持基本预览功能`
        }
        ElMessage.success(successMsg)
      } else if (method === 'onlyoffice') {
        let successMsg = `已切换到 ${methodNames[method]} 预览方案（本地部署）`
        if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
          successMsg += `，可完整查看所有${previewDialog.excelInfo.sheetCount}个工作表`
        }
        ElMessage.success(successMsg)
      } else if (method === 'libreoffice_online') {
        let successMsg = `已切换到 ${methodNames[method]} 预览方案（本地部署）`
        if (previewDialog.excelInfo && previewDialog.excelInfo.isMultiSheet) {
          successMsg += `，可完整查看所有${previewDialog.excelInfo.sheetCount}个工作表`
        }
        ElMessage.success(successMsg)
      } else {
        ElMessage.info(`已切换到 ${methodNames[method] || '下载查看'} 模式`)
      }

      // 模拟加载时间
      setTimeout(() => {
        officePreviewLoading.value = false
      }, 1500)
    }

    // 获取预览服务标题
    const getPreviewServiceTitle = () => {
      const titles = {
        libreoffice_online: 'LibreOffice Online 本地预览',
        onlyoffice: 'ONLYOFFICE 本地预览',
        client_side: '纯前端预览 - 无需第三方服务'
      }
      return titles[previewDialog.previewType] || 'Office文档预览'
    }

    // 获取预览服务类型（用于alert颜色）
    const getPreviewServiceType = () => {
      const types = {
        libreoffice_online: 'success',
        onlyoffice: 'success',
        client_side: 'success'
      }
      return types[previewDialog.previewType] || 'info'
    }

    // 获取预览服务描述
    const getPreviewServiceDescription = () => {
      const descriptions = {
        libreoffice_online: '正在使用LibreOffice Online服务预览文档，开源可靠的预览方案，完整支持Excel多工作表显示。',
        onlyoffice: '正在使用ONLYOFFICE Document Server预览文档，商业级的文档预览体验。',
        client_side: '正在使用纯前端预览技术，无需第三方服务支持，完整支持Excel多工作表切换和数据查看。'
      }
      return descriptions[previewDialog.previewType] || '正在预览Office文档...'
    }

    // 获取预览方案类型标签
    const getPreviewMethodType = (method) => {
      const types = {
        libreoffice_online: 'success', // 绿色 - 推荐方案
        onlyoffice: 'warning',         // 橙色 - 备选方案
        client_side: 'success',        // 绿色 - 无依赖方案
        fallback: 'info'              // 灰色 - 下载
      }
      return types[method] || 'info'
    }

    // 组件挂载时获取模板列表和添加全局事件监听
    onMounted(() => {
      getTemplateList()

      // 添加全局事件监听器
      document.addEventListener('dragend', handleGlobalDragEnd)
      document.addEventListener('mouseleave', handleMouseLeave)
      window.addEventListener('blur', clearAllDragStates) // 窗口失焦时清理

      // 添加键盘ESC键监听，按ESC取消拖拽状态
      const handleEscKey = (event) => {
        if (event.key === 'Escape') {
          clearAllDragStates()
        }
      }
      document.addEventListener('keydown', handleEscKey)

      // 保存事件处理器引用，用于组件卸载时移除
      window.fileAttachmentEventHandlers = {
        handleGlobalDragEnd,
        handleMouseLeave,
        clearAllDragStates,
        handleEscKey
      }
    })

    // 组件卸载时清理定时器和移除事件监听器
    onUnmounted(() => {
      clearAllDragTimers()
      clearAllDragStates()

      // 移除全局事件监听器
      if (window.fileAttachmentEventHandlers) {
        document.removeEventListener('dragend', window.fileAttachmentEventHandlers.handleGlobalDragEnd)
        document.removeEventListener('mouseleave', window.fileAttachmentEventHandlers.handleMouseLeave)
        window.removeEventListener('blur', window.fileAttachmentEventHandlers.clearAllDragStates)
        document.removeEventListener('keydown', window.fileAttachmentEventHandlers.handleEscKey)
        delete window.fileAttachmentEventHandlers
      }
    })

    return {
      uploadLoading,
      downloadLoading,
      removeLoading,
      previewLoading,
      previewDialog,
      refreshLoading,
      dragStates,
      selectedTemplate,
      templateOptions,
      templateDownloadLoading,
      officePreviewLoading,
      uploadUrl,
      headers,
      getUploadData,
      getFileName,
      beforeUpload,
      customUpload,
      downloadFile,
      previewFile,
      closePreviewDialog,
      toggleFullscreen,
      getFileFieldByType,
      getFileExtension,
      removeFile,
      handleDragEnter,
      handleDragOver,
      handleDragLeave,
      handleDrop,
      handleCardMouseLeave,
      handleManualRefresh,
      getTemplateList,
      downloadTemplate,
      switchPreviewMethod,
      getPreviewServiceTitle,
      getPreviewServiceType,
      getPreviewServiceDescription,
      getPreviewMethodType,
      // 图片查看器相关
      imageViewerVisible,
      imageViewerUrl,
      imageViewerFileName,
      imageZoom,
      isDragging,
      imagePosition,
      imageTransform,
      imageTransition,
      openImageViewer,
      closeImageViewer,
      zoomIn,
      zoomOut,
      resetImageZoom,
      handleImageWheel,
      handleMouseDown,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      downloadCurrentImage
    }
  }
}
</script>

<style scoped>
.file-attachments-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.attachments-header {
  position: relative;
  margin-bottom: 20px;
}

.refresh-button {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

/* 模板下载卡片 */
.template-download-section {
  margin-bottom: 20px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.template-content {
  padding: 16px;
}

.template-selector {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 10px;
}

.divider-title {
  font-size: 16px;
  font-weight: bold;
}

.attachment-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  position: relative;
  border: 2px dashed transparent;
  cursor: pointer;
}

.attachment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

/* 拖拽悬停状态 */
.attachment-card.drag-over {
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

/* 已有文件状态 */
.attachment-card.has-file {
  border-color: #67c23a;
}

/* 上传中状态 */
.attachment-card.uploading {
  border-color: #e6a23c;
  background-color: #fdf6ec;
  opacity: 0.8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.card-content {
  min-height: 130px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  padding-bottom: 8px;
}

/* 拖拽提示层 */
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  z-index: 10;
  pointer-events: none;
  /* 防止遮罩层干扰鼠标事件 */
}

.drag-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 8px;
  animation: bounce 1s infinite;
}

.drag-text {
  color: #409EFF;
  font-size: 16px;
  font-weight: 500;
}

@keyframes bounce {

  0%,
  20%,
  60%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  80% {
    transform: translateY(-5px);
  }
}

.file-info,
.file-placeholder {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.file-info {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.file-placeholder {
  background-color: #f5f7fa;
  color: #909399;
  flex-direction: column;
  text-align: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  min-height: 60px;
}

/* 拖拽提示文字 */
.drag-hint {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px dashed #409EFF;
  font-size: 11px;
  color: #666;
}

.file-info .el-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #67c23a;
}

.file-placeholder .el-icon {
  margin-bottom: 4px;
  font-size: 24px;
  color: #c0c4cc;
}

.file-name {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  align-items: center;
  padding: 0 8px;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  width: 100%;
  align-items: center;
}

.button-row .el-button {
  min-width: 52px;
  height: 28px;
  font-size: 12px;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin: 0;
}

.button-row .el-button .el-icon {
  margin-right: 3px;
  font-size: 12px;
}

.button-row .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.upload-button {
  margin: 0 !important;
  display: inline-flex;
}

.upload-button .el-button {
  min-width: 52px !important;
  height: 28px !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  margin: 0 !important;
}

.upload-button .el-button .el-icon {
  margin-right: 3px !important;
  font-size: 12px !important;
}

.upload-button .el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

@media (max-width: 768px) {
  .action-buttons {
    margin-top: 8px !important;
    padding: 0 4px !important;
  }

  .button-row {
    gap: 4px !important;
    justify-content: center !important;
  }

  .button-row .el-button {
    min-width: 44px !important;
    height: 26px !important;
    font-size: 10px !important;
    padding: 3px 5px !important;
    border-radius: 4px !important;
    margin: 0 !important;
  }

  .button-row .el-button .el-icon {
    margin-right: 2px !important;
    font-size: 10px !important;
  }

  .upload-button .el-button {
    min-width: 44px !important;
    height: 26px !important;
    font-size: 10px !important;
    padding: 3px 5px !important;
    border-radius: 4px !important;
    margin: 0 !important;
  }

  .upload-button .el-button .el-icon {
    margin-right: 2px !important;
    font-size: 10px !important;
  }

  .template-selector {
    flex-direction: column;
    align-items: stretch;
  }

  .template-selector .el-select {
    width: 100% !important;
  }

  .template-selector .el-button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 10px;
  }

  .card-content {
    min-height: 110px !important;
    padding-bottom: 4px !important;
  }

  .file-info,
  .file-placeholder {
    margin-bottom: 6px !important;
    padding: 6px !important;
    min-height: 50px !important;
  }

  .file-info .el-icon {
    font-size: 14px !important;
    margin-right: 4px !important;
  }

  .file-placeholder .el-icon {
    font-size: 20px !important;
    margin-bottom: 2px !important;
  }

  .drag-hint {
    margin-top: 4px !important;
    padding: 2px 6px !important;
    font-size: 10px !important;
  }

  .drag-icon {
    font-size: 36px;
  }

  .drag-text {
    font-size: 14px;
  }

  /* 移动端全屏预览优化 */
  .custom-header {
    font-size: 14px;
  }

  .header-actions .el-button {
    padding: 4px;
  }
}

/* 预览对话框样式 */
.file-preview-dialog .el-dialog__header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 头部操作按钮样式 */
.header-action-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  border: 1px solid transparent;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  color: #606266;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.header-action-btn:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.header-action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.header-action-btn.fullscreen-btn:hover {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
  border-color: rgba(103, 194, 58, 0.2);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.15);
}

.header-action-btn.close-btn:hover {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
  border-color: rgba(245, 108, 108, 0.2);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.15);
}

/* 操作图标样式 */
.action-icon {
  transition: all 0.25s ease;
}

.header-action-btn:hover .action-icon {
  transform: scale(1.1);
}

.header-action-btn .action-icon svg {
  stroke-width: 2px;
  stroke: currentColor;
}

/* 全屏模式下的样式调整 */
.file-preview-dialog.el-dialog--fullscreen {
  margin: 0;
  border-radius: 0;
}

.file-preview-dialog.el-dialog--fullscreen .el-dialog__body {
  padding: 10px;
  height: calc(100vh - 60px);
  overflow: auto;
}

/* 无header预览样式 - 适用于所有预览类型 */
.no-header-preview .el-dialog__header {
  display: none !important;
}

.no-header-preview .el-dialog__title {
  display: none !important;
}

.no-header-preview .el-dialog__headerbtn {
  display: none !important;
}

.no-header-preview .el-dialog__footer {
  display: none !important;
}

.no-header-preview .el-dialog__body {
  padding: 0 !important;
  margin: 0 !important;
}

/* 全屏预览样式 */
.fullscreen-preview.el-dialog--fullscreen .el-dialog__body {
  height: 100vh !important;
  overflow: hidden !important;
}

/* 普通预览样式 */
.no-header-preview:not(.fullscreen-preview) .el-dialog__body {
  height: 80vh !important;
  overflow: hidden !important;
}

/* 浮动工具栏样式 */
.floating-toolbar {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 6px;
  align-items: center;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.toolbar-btn.fullscreen-btn {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
}

.toolbar-btn.fullscreen-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.toolbar-btn.download-btn {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.toolbar-btn.download-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.toolbar-btn.close-btn {
  background: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.toolbar-btn.close-btn:hover {
  background: #f78989;
  border-color: #f78989;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.toolbar-btn:active {
  transform: translateY(0);
}

/* 预览内容包装器 */
.preview-content-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Excel预览容器优化 */
#excel-preview-container {
  position: relative;
}

/* 全屏模式下隐藏滚动条但保持功能 */
.fullscreen-excel-preview #excel-preview-container {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.fullscreen-excel-preview #excel-preview-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.preview-container {
  text-align: left;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.image-preview img {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.unsupported-preview {
  padding: 40px;
  text-align: center;
}



/* Office文档在线预览样式 */
.office-preview {
  padding: 0;
  background-color: #fff;
  border-radius: 0;
  box-shadow: none;
  width: 100%;
  height: 100%;
}

.preview-content-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.office-preview-header {
  margin-bottom: 20px;
}

.office-preview-header .el-alert {
  margin-bottom: 10px;
}

.office-preview-header .el-alert {
  margin-bottom: 10px;
}

.preview-method-switch {
  margin-top: 10px;
}

.preview-method-switch .el-text {
  margin-right: 10px;
}

.preview-method-switch .el-button-group {
  margin-top: 5px;
}

.preview-method-switch .el-button-group .el-button {
  margin-right: 5px;
}

.office-preview iframe {
  width: 100%;
  height: 600px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* Excel信息显示样式 */
.excel-info-alert {
  margin-bottom: 15px;
}

/* 图片查看器工具栏样式 */
.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.image-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  backdrop-filter: blur(10px);
}

.toolbar-left {
  flex: 1;
}

.image-title {
  font-size: 14px;
  color: #e6e6e6;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: 15px;
}

.action-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 图片查看器按钮样式 */
.image-viewer-toolbar .toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
  font-size: 12px;
  gap: 4px;
}

.image-viewer-toolbar .toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.image-viewer-toolbar .toolbar-btn:active {
  transform: translateY(0);
}

.image-viewer-toolbar .toolbar-btn.zoom-display {
  min-width: 50px;
  font-weight: 500;
}

.image-viewer-toolbar .toolbar-btn .btn-text {
  font-size: 12px;
  margin-left: 4px;
  white-space: nowrap;
}

.image-viewer-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.image-viewer-content img {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  user-select: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  cursor: grab;
  transition: transform 0.3s ease;
}

.image-viewer-content img:active {
  cursor: grabbing;
}

.image-viewer-content img.dragging {
  cursor: grabbing;
  transition: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-viewer-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;
  }
  
  .toolbar-left,
  .toolbar-right {
    flex: none;
  }
  
  .toolbar-right {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .image-viewer-toolbar .toolbar-btn .btn-text {
    display: none;
  }
  
  .action-controls {
    gap: 6px;
  }
}

.excel-info-alert .el-alert {
  border-radius: 8px;
}

.sheet-names {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.sheet-names .el-tag {
  font-weight: 500;
  border-radius: 12px;
}

/* 图片查看器样式 */
.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.image-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  backdrop-filter: blur(10px);
}

.toolbar-left {
  flex: 1;
}

.image-title {
  font-size: 14px;
  color: #e6e6e6;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 缩放控制组 */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 2px;
  backdrop-filter: blur(10px);
}

/* 操作控制组 */
.action-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 工具栏按钮基础样式 */
.toolbar-btn {
  min-width: 36px;
  height: 36px;
  padding: 0 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  white-space: nowrap;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toolbar-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toolbar-btn:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 缩放按钮特殊样式 */
.zoom-btn {
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
}

.zoom-display {
  min-width: 60px;
  height: 32px;
  font-size: 12px;
  font-weight: 600;
  background: rgba(64, 158, 255, 0.2);
  border-color: rgba(64, 158, 255, 0.3);
  color: #409eff;
}

.zoom-display:hover {
  background: rgba(64, 158, 255, 0.3);
  border-color: rgba(64, 158, 255, 0.4);
  color: #66b1ff;
}

/* 下载按钮样式 */
.download-btn {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.3);
  color: #67c23a;
}

.download-btn:hover {
  background: rgba(103, 194, 58, 0.3);
  border-color: rgba(103, 194, 58, 0.4);
  color: #85ce61;
}

/* 关闭按钮样式 */
.close-btn {
  background: rgba(245, 108, 108, 0.2);
  border-color: rgba(245, 108, 108, 0.3);
  color: #f56c6c;
}

.close-btn:hover {
  background: rgba(245, 108, 108, 0.3);
  border-color: rgba(245, 108, 108, 0.4);
  color: #f78989;
}

/* 按钮文字 */
.btn-text {
  margin-left: 4px;
  font-size: 12px;
}

.image-viewer-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.image-viewer-content img {
  max-width: 90vw;
  max-height: 80vh;
  object-fit: contain;
  user-select: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  cursor: grab;
}

.image-viewer-content img:active {
  cursor: grabbing;
}

/* 图片预览增强样式 */
.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  min-height: 400px;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-preview-container:hover {
  transform: scale(1.02);
}

.image-preview-container:hover .image-preview-overlay {
  opacity: 1;
}

.image-preview-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 8px;
}

.image-preview-container:hover .image-preview-overlay {
  pointer-events: auto;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.image-preview-container:hover img {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 深色主题下的按钮样式 */
.image-viewer-toolbar :deep(.el-button) {
  &.el-button--small {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

/* 图片查看器响应式设计 */
@media (max-width: 768px) {
  .image-viewer-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
  }

  .toolbar-left,
  .toolbar-right {
    flex: none;
  }

  .toolbar-right {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .zoom-controls,
  .action-controls {
    gap: 4px;
  }

  .toolbar-btn {
    min-width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .zoom-display {
    min-width: 50px;
    height: 28px;
  }

  .btn-text {
    display: none;
  }

  .image-viewer-content img {
    max-width: 95vw;
    max-height: 70vh;
  }

  /* 移动端头部按钮优化 */
  .header-action-btn {
    width: 32px;
    height: 32px;
  }

  .header-action-btn .action-icon {
    font-size: 16px;
  }

  .custom-header {
    font-size: 14px;
  }
}
</style>
