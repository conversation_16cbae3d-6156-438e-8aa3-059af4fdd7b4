/**
 * 文件上传工具模块
 */
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { format } = require('date-fns');
const {
  processChineseFilename,
  setContentDisposition
} = require('./chinese-filename');
const dotenv = require('dotenv');

// 加载环境变量 - 根据NODE_ENV选择配置文件
const envFile = process.env.NODE_ENV === 'production' ? '.env_pro' : '.env';
dotenv.config({ path: envFile });

// 文件存储路径配置
const baseUploadPath = process.env.FILE_UPLOAD_BASE_PATH || 'uploads';
const oaProcessPath = process.env.FILE_UPLOAD_OA_PATH || path.join(baseUploadPath, 'oa_process');
const signedArchivePath = process.env.FILE_UPLOAD_SIGNED_PATH || path.join(baseUploadPath, 'signed_archive');
const operationSheetPath = process.env.FILE_UPLOAD_OPERATION_PATH || path.join(baseUploadPath, 'operation_sheet');
const supplementaryPath = process.env.FILE_UPLOAD_SUPPLEMENTARY_PATH || path.join(baseUploadPath, 'supplementary_material');
const templatesPath = process.env.FILE_UPLOAD_TEMPLATES_PATH || path.join(baseUploadPath, 'change_templates');
const eventTemplatesPath = process.env.FILE_UPLOAD_EVENT_TEMPLATES_PATH || path.join(baseUploadPath, 'event_templates');
const fileUrlPrefix = process.env.FILE_URL_PREFIX || 'http://localhost:3000/files';

// 确保上传目录存在
const ensureDir = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// 初始化上传目录
const initUploadDirs = () => {
  ensureDir(baseUploadPath);
  ensureDir(oaProcessPath);
  ensureDir(signedArchivePath);
  ensureDir(operationSheetPath);
  ensureDir(supplementaryPath);
  ensureDir(templatesPath);
  ensureDir(eventTemplatesPath);
};

// 初始化目录
initUploadDirs();

// 允许的文件类型 - 包含常见的MIME类型变体
const allowedFileTypes = {
  // PDF文件
  'application/pdf': 'pdf',
  'application/x-pdf': 'pdf',
  'application/acrobat': 'pdf',
  'applications/vnd.pdf': 'pdf',
  'text/pdf': 'pdf',
  'text/x-pdf': 'pdf',
  
  // Word文档
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  
  // Excel文档
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/excel': 'xls',
  'application/x-excel': 'xls',
  'application/x-msexcel': 'xls',
  
  // 图片文件
  'image/jpeg': 'jpg',
  'image/jpg': 'jpg',
  'image/pjpeg': 'jpg',
  'image/png': 'png',
  'image/x-png': 'png',
  'image/gif': 'gif'
};

// 文件存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据文件类型确定存储路径
    // 优先从查询参数获取，其次从请求体获取
    const fileType = req.query.fileType || req.body.fileType || 'oa_process';

    let uploadPath;

    // 创建按年月组织的子目录
    const yearMonth = format(new Date(), 'yyyy-MM');

    switch (fileType) {
      case 'oa_process':
        uploadPath = path.join(oaProcessPath, yearMonth);
        break;
      case 'signed_archive':
        uploadPath = path.join(signedArchivePath, yearMonth);
        break;
      case 'operation_sheet':
        uploadPath = path.join(operationSheetPath, yearMonth);
        break;
      case 'supplementary_material':
        uploadPath = path.join(supplementaryPath, yearMonth);
        break;
      case 'change_template':
        uploadPath = path.join(templatesPath, yearMonth);
        break;
      default:
        uploadPath = path.join(baseUploadPath, yearMonth);
    }

    // 确保目录存在
    ensureDir(uploadPath);

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // 获取文件扩展名
    const fileExt = allowedFileTypes[file.mimetype] || 'unknown';

    // 优先从查询参数获取，其次从请求体获取
    const fileType = req.query.fileType || req.body.fileType || 'unknown';
    const timestamp = Date.now();

    // 处理文件名乱码问题
    let originalname = file.originalname;

    // 保存原始文件名到请求对象，以便后续处理
    if (!req.originalFilenames) {
      req.originalFilenames = {};
    }
    req.originalFilenames.raw = originalname;

    try {
      // 如果请求对象中已经有处理后的文件名，则使用它
      if (req.originalFilenames && req.originalFilenames.processed) {
        originalname = req.originalFilenames.processed;
      } else {
        // 使用专门的中文文件名处理工具处理
        originalname = processChineseFilename(originalname);

        // 保存处理后的文件名到请求对象
        req.originalFilenames.processed = originalname;
      }

      // 更新file对象的originalname
      file.originalname = originalname;

    } catch (error) {
      // 文件名处理失败
    }

    // 为不同类型的文件生成不同的文件名
    let fileName;

    if (fileType === 'change_template') {
      // 对于变更模板，使用模板名称和时间戳
      const templateName = req.body.templateName || 'template';
      // 移除文件名中的特殊字符，避免路径问题
      const safeTemplateName = templateName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_');
      fileName = `template_${safeTemplateName}_${timestamp}.${fileExt}`;
    } else {
      // 对于其他类型，使用原来的逻辑
      const changeId = req.query.changeId || req.body.changeId || 'unknown';
      fileName = `${changeId}_${fileType}_${timestamp}.${fileExt}`;
    }

    cb(null, fileName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型是否允许
  const fileType = req.query.fileType || req.body.fileType || '';

  // 对于变更模板，只允许Word和Excel文件
  if (fileType === 'change_template') {
    const allowedMimeTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error('不支持的文件类型，变更模板只允许Word和Excel文件'), false);
    }

    // 检查文件扩展名
    const fileName = file.originalname.toLowerCase();
    const isValidExtension = fileName.endsWith('.doc') ||
                            fileName.endsWith('.docx') ||
                            fileName.endsWith('.xls') ||
                            fileName.endsWith('.xlsx');

    if (!isValidExtension) {
      return cb(new Error('不支持的文件类型，变更模板只允许.doc、.docx、.xls、.xlsx格式'), false);
    }
  } else {
    // 其他类型的文件，检查是否在允许的类型列表中
    if (!allowedFileTypes[file.mimetype]) {
      return cb(new Error('不支持的文件类型。允许的类型：PDF、Word、Excel、图片'), false);
    }
  }

  // 处理文件名乱码问题
  try {
    let originalname = file.originalname;

    // 保存原始文件名到请求对象
    if (!req.originalFilenames) {
      req.originalFilenames = {};
    }
    req.originalFilenames.raw = originalname;

    // 使用专门的中文文件名处理工具处理
    const processedName = processChineseFilename(originalname);

    // 更新file对象的originalname
    file.originalname = processedName;

    // 保存处理后的文件名到请求对象
    req.originalFilenames.processed = processedName;

  } catch (error) {
    // 文件过滤器中处理文件名乱码失败
  }

  cb(null, true);
};

// 创建multer上传实例
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 增加到50MB
    fieldSize: 50 * 1024 * 1024, // 增加到50MB
    files: 1, // 限制为单文件上传
    parts: 10 // 限制表单字段数量
  }
});

/**
 * 获取文件URL
 * @param {string} filePath 文件相对路径
 * @returns {string} 文件完整URL
 */
const getFileUrl = (filePath) => {
  if (!filePath) return '';

  // 如果已经是完整URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }

  // 构建完整URL
  return `${fileUrlPrefix}/${filePath.replace(/\\/g, '/')}`;
};

/**
 * 删除文件
 * @param {string} filePath 文件相对路径
 * @returns {Promise<boolean>} 是否删除成功
 */
const deleteFile = async (filePath) => {
  if (!filePath) return false;

  try {
    // 构建完整路径
    const fullPath = path.join(baseUploadPath, filePath);

    // 检查文件是否存在
    if (fs.existsSync(fullPath)) {
      // 删除文件
      fs.unlinkSync(fullPath);
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
};

/**
 * 处理文件上传
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @returns {Promise<Object>} 上传文件信息
 */
const handleFileUpload = (req, res) => {
  return new Promise((resolve, reject) => {
    // 确保上传目录存在
    try {
      // 确保基础目录存在
      if (!fs.existsSync(baseUploadPath)) {
        fs.mkdirSync(baseUploadPath, { recursive: true });
      }

      // 根据文件类型确定子目录
      const fileType = req.body.fileType || 'oa_process';
      let uploadPath;

      switch (fileType) {
        case 'oa_process':
          uploadPath = oaProcessPath;
          break;
        case 'signed_archive':
          uploadPath = signedArchivePath;
          break;
        case 'operation_sheet':
          uploadPath = operationSheetPath;
          break;
        case 'supplementary_material':
          uploadPath = supplementaryPath;
          break;
        case 'change_template':
          uploadPath = templatesPath;
          break;
        default:
          uploadPath = baseUploadPath;
      }

      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }

      // 创建年月子目录
      const yearMonth = format(new Date(), 'yyyy-MM');
      const yearMonthPath = path.join(uploadPath, yearMonth);

      if (!fs.existsSync(yearMonthPath)) {
        fs.mkdirSync(yearMonthPath, { recursive: true });
      }

    } catch (dirError) {
      // 创建目录失败
    }

    upload.single('file')(req, res, (err) => {
      if (err) {
        return reject(err);
      }

      if (!req.file) {
        return reject(new Error('未找到上传的文件'));
      }

      try {
        // 获取文件相对路径（相对于上传根目录）
        const relativePath = path.relative(
          path.resolve(baseUploadPath),
          path.resolve(req.file.path)
        ).replace(/\\/g, '/');

        // 构建文件URL
        const fileUrl = getFileUrl(relativePath);

        const result = {
          originalname: req.file.originalname,
          filename: req.file.filename,
          path: relativePath,
          url: fileUrl
        };

        resolve(result);
      } catch (pathError) {
        reject(pathError);
      }
    });
  });
};

/**
 * 文件上传错误处理中间件
 * @param {Object} err 错误对象
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
const handleUploadError = (err, req, res, next) => {

  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      code: 1,
      msg: '文件大小超出限制，最大允许50MB'
    });
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      code: 1,
      msg: '意外的文件字段名称，请使用"file"作为文件字段名'
    });
  }

  if (err.message === 'Unexpected end of form') {
    return res.status(400).json({
      code: 1,
      msg: '文件上传中断，请重试'
    });
  }

  if (err.message.includes('不支持的文件类型')) {
    return res.status(415).json({
      code: 1,
      msg: err.message
    });
  }

  // 默认错误处理
  return res.status(500).json({
    code: 1,
    msg: `文件上传失败: ${err.message}`
  });
};

/**
 * 创建带错误处理的上传中间件
 * @param {string} fieldName 文件字段名
 * @returns {Function} 上传中间件
 */
const uploadWithErrorHandling = (fieldName = 'file') => {
  return (req, res, next) => {
    upload.single(fieldName)(req, res, (err) => {
      if (err) {
        return handleUploadError(err, req, res, next);
      }
      next();
    });
  };
};

module.exports = {
  upload,
  uploadWithErrorHandling,
  getFileUrl,
  deleteFile,
  handleFileUpload,
  baseUploadPath,
  oaProcessPath,
  signedArchivePath,
  operationSheetPath,
  supplementaryPath,
  templatesPath
};
