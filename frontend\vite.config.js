import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  // 获取后端API地址，如果环境变量中没有设置，则使用默认值
  const apiBaseUrl = env.VITE_API_BASE_URL || 'http://localhost:3000'

  // 从环境变量中获取构建配置
  const basePath = env.VITE_BUILD_BASE_PATH
  const serverHost = env.VITE_DEV_SERVER_HOST || '0.0.0.0'
  const serverPort = parseInt(env.VITE_DEV_SERVER_PORT || '9000')
  const useHttps = env.VITE_DEV_SERVER_HTTPS === 'true'

  // 构建相关配置
  const buildConfig = {
    sourcemap: env.VITE_BUILD_SOURCEMAP === 'true',
    minify: env.VITE_BUILD_COMPRESS === 'true' ? 'esbuild' : false,
    // 其他构建配置...
  }

  return {
    base: basePath,
    plugins: [vue()],

    // 使用环境变量配置构建选项
    build: {
      sourcemap: buildConfig.sourcemap,
      minify: buildConfig.minify,
      // 如果需要删除控制台输出
      terserOptions: env.VITE_BUILD_DROP_CONSOLE === 'true' ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      } : undefined,
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    // 使用环境变量配置开发服务器
    server: {
      host: serverHost, // 从环境变量中获取主机
      port: serverPort, // 从环境变量中获取端口
      https: useHttps,  // 从环境变量中获取是否使用HTTPS
      strictPort: true, // 如果端口被占用，直接退出而不是尝试其他端口
      proxy: {
        '/api': {
          target: apiBaseUrl, // 使用环境变量中的后端服务器地址
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path,
          // 增加超时设置
          timeout: 120000, // 120秒
          // 增加请求体大小限制
          proxyTimeout: 120000, // 120秒
          configure: (proxy, options) => {
            // 增加请求头
            proxy.on('proxyReq', (proxyReq, req, res) => {
              proxyReq.setHeader('Connection', 'keep-alive');
              proxyReq.setHeader('Keep-Alive', 'timeout=120');
            });
          }
      }
    }
  }
}});
