/**
 * Excel导入导出工具类
 */
const XLSX = require('xlsx');

/**
 * 解析Excel文件数据
 * @param {Buffer} fileBuffer - Excel文件缓冲区
 * @param {string} sheetName - 工作表名称（可选，默认使用第一个工作表）
 * @returns {Array} 解析后的数据数组
 */
const parseExcelFile = (fileBuffer, sheetName = null) => {
  try {
    // 读取工作簿
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    
    // 获取工作表名称
    const targetSheetName = sheetName || workbook.SheetNames[0];
    
    if (!workbook.Sheets[targetSheetName]) {
      throw new Error(`工作表 "${targetSheetName}" 不存在`);
    }
    
    // 获取工作表
    const worksheet = workbook.Sheets[targetSheetName];
    
    // 转换为JSON数组
    const data = XLSX.utils.sheet_to_json(worksheet, {
      header: 1, // 使用数组格式
      defval: '', // 空单元格默认值
      blankrows: false // 跳过空行
    });
    
    if (data.length === 0) {
      throw new Error('Excel文件中没有数据');
    }
    
    // 第一行作为表头
    const headers = data[0];
    const rows = data.slice(1);
    
    // 转换为对象数组
    const result = rows.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
    
    return result;
  } catch (error) {
    throw new Error(`解析Excel文件失败: ${error.message}`);
  }
};

/**
 * 创建Excel文件缓冲区
 * @param {Array} data - 数据数组
 * @param {string} sheetName - 工作表名称
 * @returns {Buffer} Excel文件缓冲区
 */
const createExcelBuffer = (data, sheetName = 'Sheet1') => {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // 生成Excel文件缓冲区
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    return buffer;
  } catch (error) {
    throw new Error(`创建Excel文件失败: ${error.message}`);
  }
};

/**
 * 创建多工作表Excel文件缓冲区
 * @param {Array} sheets - 工作表数组，格式：[{name: '工作表名', data: [数据数组]}]
 * @returns {Buffer} Excel文件缓冲区
 */
const createMultiSheetExcelBuffer = (sheets) => {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 添加每个工作表
    sheets.forEach(sheet => {
      const worksheet = XLSX.utils.json_to_sheet(sheet.data);
      XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
    });
    
    // 生成Excel文件缓冲区
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    return buffer;
  } catch (error) {
    throw new Error(`创建多工作表Excel文件失败: ${error.message}`);
  }
};

/**
 * 验证Excel文件格式
 * @param {Buffer} fileBuffer - Excel文件缓冲区
 * @param {Array} requiredHeaders - 必需的表头数组
 * @returns {Object} 验证结果 {valid: boolean, message: string, headers: Array}
 */
const validateExcelFormat = (fileBuffer, requiredHeaders = []) => {
  try {
    // 读取工作簿
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    
    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // 获取表头
    const data = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      range: 0 // 只读取第一行
    });
    
    if (data.length === 0) {
      return { valid: false, message: 'Excel文件为空', headers: [] };
    }
    
    const headers = data[0];
    
    // 检查必需的表头
    if (requiredHeaders.length > 0) {
      const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
      if (missingHeaders.length > 0) {
        return {
          valid: false,
          message: `缺少必需的表头: ${missingHeaders.join(', ')}`,
          headers: headers
        };
      }
    }
    
    return { valid: true, message: '格式验证通过', headers: headers };
  } catch (error) {
    return { valid: false, message: `文件格式错误: ${error.message}`, headers: [] };
  }
};

/**
 * 生成Excel导入模板
 * @param {Array} headers - 表头数组
 * @param {Array} sampleData - 示例数据数组（可选）
 * @param {string} sheetName - 工作表名称
 * @returns {Buffer} Excel模板文件缓冲区
 */
const generateImportTemplate = (headers, sampleData = [], sheetName = '导入模板') => {
  try {
    // 创建模板数据
    const templateData = [];
    
    // 添加表头说明行（可选）
    if (sampleData.length === 0) {
      // 如果没有示例数据，创建一个空行作为模板
      const emptyRow = {};
      headers.forEach(header => {
        emptyRow[header] = '';
      });
      templateData.push(emptyRow);
    } else {
      // 使用提供的示例数据
      templateData.push(...sampleData);
    }
    
    return createExcelBuffer(templateData, sheetName);
  } catch (error) {
    throw new Error(`生成导入模板失败: ${error.message}`);
  }
};

module.exports = {
  parseExcelFile,
  createExcelBuffer,
  createMultiSheetExcelBuffer,
  validateExcelFormat,
  generateImportTemplate
};