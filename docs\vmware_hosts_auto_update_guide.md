# VMware虚拟化信息自动更新功能指南

## 功能概述

VMware虚拟化信息自动更新功能是CMDB系统自动发现模块下的一个新增功能，用于定时从指定的VMware接口获取虚拟机信息，并自动更新到数据库中。该功能支持每小时自动同步一次，也支持手动触发同步。

## 主要特性

- **自动定时同步**: 每小时自动从VMware接口获取最新数据
- **手动同步**: 支持用户手动触发数据同步
- **数据管理**: 提供完整的CRUD操作界面
- **同步监控**: 实时监控同步状态和历史记录
- **统计分析**: 提供主机状态、性能等统计信息
- **数据导出**: 支持导出VMware主机数据

## 数据源配置

### 接口地址
```
http://**************:8081/config_files?file=vmware_hosts.json
```

### 环境变量配置

在 `backend/.env` 文件中添加以下配置：

```bash
# VMware虚拟化信息自动更新配置
VMWARE_DATA_SOURCE_URL=http://**************:8081/config_files?file=vmware_hosts.json
VMWARE_SYNC_INTERVAL=0 * * * *
VMWARE_SYNC_ENABLED=true
VMWARE_SYNC_TIMEOUT=30000
VMWARE_SYNC_LOG_RETENTION_DAYS=30
```

### 配置说明

- `VMWARE_DATA_SOURCE_URL`: VMware数据源接口地址
- `VMWARE_SYNC_INTERVAL`: 同步间隔，使用cron表达式格式（默认每小时执行一次）
- `VMWARE_SYNC_ENABLED`: 是否启用自动同步（true/false）
- `VMWARE_SYNC_TIMEOUT`: 接口请求超时时间（毫秒）
- `VMWARE_SYNC_LOG_RETENTION_DAYS`: 同步日志保留天数

## 数据库结构

### 主表：cmdb_vmware_info_auto_update

存储VMware虚拟机的基本信息：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | SERIAL | 主键ID |
| vm_name | VARCHAR(255) | 虚拟机名称 |
| vcenter_ip | INET | vCenter IP地址 |
| esxi_ip | INET | ESXi主机IP地址 |
| vm_ip | INET | 虚拟机IP地址 |
| data_source_time | TIMESTAMP | 数据源时间 |
| raw_data | JSONB | 原始JSON数据 |
| data_source_url | VARCHAR(500) | 数据源URL |
| last_sync_time | TIMESTAMP | 最后同步时间 |
| sync_status | VARCHAR(20) | 同步状态 |
| sync_error_message | TEXT | 同步错误信息 |
| created_at | TIMESTAMP | 创建时间 |
| created_by | VARCHAR(50) | 创建人 |
| updated_at | TIMESTAMP | 更新时间 |
| updated_by | VARCHAR(50) | 更新人 |
| is_deleted | BOOLEAN | 是否删除 |
| version_num | INTEGER | 版本号 |

### 日志表：cmdb_vmware_sync_logs

记录每次同步的详细信息：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | SERIAL | 主键ID |
| sync_start_time | TIMESTAMP | 同步开始时间 |
| sync_end_time | TIMESTAMP | 同步结束时间 |
| sync_duration_ms | INTEGER | 同步耗时(毫秒) |
| total_records | INTEGER | 总记录数 |
| success_records | INTEGER | 成功记录数 |
| failed_records | INTEGER | 失败记录数 |
| new_records | INTEGER | 新增记录数 |
| updated_records | INTEGER | 更新记录数 |
| sync_status | VARCHAR(20) | 同步状态 |
| error_message | TEXT | 错误信息 |

## 部署步骤

### 1. 创建数据库表

执行以下命令创建数据库表和视图：

```bash
cd backend
node scripts/deploy-vmware-tables.js
```

或者直接执行SQL脚本：

```bash
psql -d cmdb -f sql/*******/create_vmware_hosts_table.sql
```

### 2. 更新环境配置

确保 `backend/.env` 文件中包含VMware相关的配置项。

### 3. 重启后端服务

```bash
cd backend
npm run dev  # 开发环境
# 或
npm start    # 生产环境
```

### 4. 验证服务启动

查看控制台输出，确认看到以下信息：

```
VMware同步服务初始化完成
数据源URL: http://**************:8081/config_files?file=vmware_hosts.json
同步间隔: 0 * * * *
VMware数据同步定时任务已启动，执行间隔: 0 * * * *
VMware虚拟化信息同步服务启动成功
```

## API接口

### 数据查询接口

- `POST /api/get_vmware_hosts` - 获取VMware主机列表
- `POST /api/get_vmware_host_detail` - 获取主机详情
- `POST /api/get_vmware_statistics` - 获取统计信息

### 数据管理接口

- `POST /api/delete_vmware_host` - 删除单个主机记录
- `POST /api/batch_delete_vmware_hosts` - 批量删除主机记录
- `POST /api/export_vmware_hosts` - 导出主机数据

### 同步管理接口

- `POST /api/trigger_vmware_sync` - 手动触发同步
- `POST /api/get_vmware_sync_status` - 获取同步状态
- `POST /api/get_vmware_sync_history` - 获取同步历史

## 前端页面

访问路径：`/cmdb/vmware-hosts`

### 主要功能

1. **统计卡片**: 显示主机总数、在线主机、维护模式主机、虚拟机总数等统计信息
2. **同步状态**: 显示服务运行状态、最后同步时间、数据源地址等信息
3. **搜索过滤**: 支持按主机名、IP地址、状态、集群、数据中心等条件搜索
4. **数据表格**: 展示VMware主机列表，支持排序、分页
5. **详情查看**: 查看主机的基本信息、硬件信息、性能信息
6. **数据管理**: 支持删除、批量删除操作
7. **手动同步**: 支持手动触发数据同步
8. **数据导出**: 支持导出Excel格式数据

## 监控和维护

### 同步状态监控

- 通过前端页面实时查看同步服务状态
- 查看同步历史记录和错误信息
- 监控同步成功率和耗时

### 日志管理

- 同步日志自动保留30天（可配置）
- 支持查看详细的同步过程和错误信息
- 定期清理过期日志

### 故障排查

1. **同步失败**
   - 检查网络连接
   - 验证数据源接口是否正常
   - 查看同步日志中的错误信息

2. **数据不一致**
   - 手动触发同步
   - 检查数据源接口返回的数据格式
   - 验证数据解析逻辑

3. **性能问题**
   - 调整同步间隔
   - 优化数据库索引
   - 监控系统资源使用情况

## 数据格式要求

VMware接口返回的数据格式如下：

```json
{
  "data": [
    {
      "name": "CTP-FZ2-DB-100.3",
      "vcenter_ip": "************",
      "esxi_ip": "*************",
      "vm_ip": "************"
    },
    {
      "name": "gitlab-98.139",
      "vcenter_ip": "************",
      "esxi_ip": "*************",
      "vm_ip": "*************"
    }
  ],
  "updatetime": "2025-08-14 12:56:45"
}
```

### 字段说明

- `data`: 虚拟机数据数组
- `name`: 虚拟机名称
- `vcenter_ip`: vCenter服务器IP地址
- `esxi_ip`: ESXi主机IP地址
- `vm_ip`: 虚拟机IP地址（可能为空）
- `updatetime`: 数据源更新时间

## 注意事项

1. **数据安全**: 确保VMware接口的访问权限和网络安全
2. **性能影响**: 大量数据同步可能影响数据库性能，建议在业务低峰期执行
3. **数据一致性**: 同步过程中避免手动修改相关数据
4. **备份策略**: 定期备份VMware相关数据表
5. **监控告警**: 建议配置同步失败的告警机制

## 版本信息

- **版本**: *******
- **创建时间**: 2025-01-14
- **依赖**: Node.js 16+, PostgreSQL 12+, axios, node-cron