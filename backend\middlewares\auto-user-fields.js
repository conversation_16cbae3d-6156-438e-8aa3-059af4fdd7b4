/**
 * 自动用户字段中间件
 * 自动从JWT token中提取用户名，并设置到请求体中
 * 用于自动填充 created_by 和 updated_by 字段
 */

const jwt = require('jsonwebtoken');
const JWT_SECRET = process.env.JWT_SECRET || 'default-secret-key';

/**
 * 自动设置用户字段的中间件
 * 从JWT token中提取username，并根据操作类型设置相应字段
 */
const autoUserFields = (req, res, next) => {
    try {
        // 从请求头中获取 JWT token
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({ code: 1, msg: '未找到有效的token' });
        }

        // 验证并解析token
        const decoded = jwt.verify(token, JWT_SECRET);
        const username = decoded.username;

        if (!username) {
            return res.status(401).json({ code: 1, msg: 'token中未找到用户名' });
        }

        // 根据请求路径判断操作类型
        const isAddOperation = req.path.includes('/add_') || req.method === 'POST' && !req.body.id;
        const isUpdateOperation = req.path.includes('/update_') || (req.method === 'POST' && req.body.id) || req.method === 'PUT';

        // 自动设置用户字段
        if (isAddOperation) {
            // 新增操作：设置创建人和更新人
            req.body.created_by = username;
            req.body.updated_by = username;
            req.body.username = username; // 保持向后兼容
        } else if (isUpdateOperation) {
            // 更新操作：只设置更新人
            req.body.updated_by = username;
            req.body.username = username; // 保持向后兼容
        }

        // 将用户信息添加到请求对象中，供后续使用
        req.user = {
            username: username,
            userId: decoded.userId,
            role_code: decoded.role_code
        };

        next();
    } catch (error) {
        console.error('自动用户字段中间件错误:', error);
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ code: 1, msg: 'token无效' });
        } else if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ code: 1, msg: 'token已过期' });
        } else {
            return res.status(500).json({ code: 1, msg: '服务器内部错误' });
        }
    }
};

/**
 * 专门用于资产管理API的中间件
 * 只对CMDB相关的API路径生效
 */
const cmdbAutoUserFields = (req, res, next) => {
    // 检查是否是CMDB相关的API
    const isCmdbApi = req.path.includes('/api/add_cmdb') || 
                      req.path.includes('/api/update_cmdb') || 
                      req.path.includes('/api/del_cmdb');

    if (isCmdbApi) {
        return autoUserFields(req, res, next);
    } else {
        // 非CMDB API，直接跳过
        next();
    }
};

module.exports = {
    autoUserFields,
    cmdbAutoUserFields
};