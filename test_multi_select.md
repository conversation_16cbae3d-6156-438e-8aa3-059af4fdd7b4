# 多选机房功能测试指南

## 测试步骤

### 1. 前端测试
1. 打开设备管理页面 (cmdb_device_management)
2. 在查询条件中找到"所属机房"字段
3. 验证以下功能：
   - 可以选择多个机房
   - 选中的机房以标签形式显示
   - 可以通过点击标签删除单个选择
   - 可以通过清空按钮清除所有选择

### 2. 后端测试
1. 打开浏览器开发者工具的网络面板
2. 执行搜索操作
3. 查看发送到 `/api/get_cmdb_device_management` 的请求
4. 验证 `data_center` 字段是否为数组格式

### 3. 数据库查询测试
1. 查看后端控制台日志
2. 确认看到调试信息：
   ```
   === 设备管理查询 - 所属机房参数 ===
   data_center: [选择的机房数组]
   data_center type: object
   data_center is array: true
   ```

### 4. 功能验证
1. 选择单个机房，验证查询结果正确
2. 选择多个机房，验证查询结果包含所有选中机房的设备
3. 不选择任何机房，验证显示所有设备
4. 重置搜索条件，验证机房选择被清空

## 预期结果

- ✅ 前端支持多选机房
- ✅ 后端正确处理数组参数
- ✅ SQL查询支持多值匹配
- ✅ 查询结果准确反映筛选条件

## 故障排除

如果遇到问题，请检查：
1. 浏览器控制台是否有JavaScript错误
2. 后端控制台是否有SQL错误
3. 数据库中是否存在对应的机房数据
4. 网络请求是否正常发送和接收