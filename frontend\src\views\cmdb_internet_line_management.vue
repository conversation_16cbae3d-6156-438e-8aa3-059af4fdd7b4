<template>
  <div class="internet-line-management">
    <!-- 新增线路对话框 -->
    <el-dialog v-model="dialogVisible.add" title="新增互联网线路" width="500" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="formData" :rules="rules" ref="addFormRef" label-position="right" label-width="120px">
        <el-form-item prop="line_name" label="线路名称:">
          <el-input v-model="formData.line_name" placeholder="请输入线路名称" clearable />
        </el-form-item>
        <el-form-item prop="provider" label="运营商:">
          <el-select v-model="formData.provider" placeholder="请选择运营商" clearable>
            <el-option v-for="item in providers" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
        <el-form-item prop="line_type" label="线路类型:">
          <el-select v-model="formData.line_type" placeholder="请选择线路类型" clearable>
            <el-option v-for="item in lineTypes" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
        <el-form-item prop="bandwidth" label="带宽:">
          <el-input v-model="formData.bandwidth" placeholder="请输入带宽，如：100M" clearable />
        </el-form-item>
        <el-form-item prop="ip_range" label="IP地址段:">
          <el-input v-model="formData.ip_range" placeholder="请输入IP地址段，如：***********/24" clearable />
        </el-form-item>
        <el-form-item prop="gateway_ip" label="网关IP:">
          <el-input v-model="formData.gateway_ip" placeholder="请输入网关IP，如：***********" clearable />
        </el-form-item>
        <el-form-item prop="firewall_ip" label="防火墙IP:">
          <el-input v-model="formData.firewall_ip" placeholder="请输入防火墙IP，如：*************" clearable />
        </el-form-item>
        <el-form-item prop="datacenter" label="所属机房:">
          <el-select v-model="formData.datacenter" placeholder="请选择所属机房" clearable>
            <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitAdd" :loading="submitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑线路对话框 -->
    <el-dialog v-model="dialogVisible.edit" title="编辑互联网线路" width="500" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="formData" :rules="rules" ref="editFormRef" label-position="right" label-width="120px">
        <el-form-item prop="line_name" label="线路名称:">
          <el-input v-model="formData.line_name" placeholder="请输入线路名称" clearable />
        </el-form-item>
        <el-form-item prop="provider" label="运营商:">
          <el-select v-model="formData.provider" placeholder="请选择运营商" clearable>
            <el-option v-for="item in providers" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
        <el-form-item prop="line_type" label="线路类型:">
          <el-select v-model="formData.line_type" placeholder="请选择线路类型" clearable>
            <el-option v-for="item in lineTypes" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
        <el-form-item prop="bandwidth" label="带宽:">
          <el-input v-model="formData.bandwidth" placeholder="请输入带宽，如：100M" clearable />
        </el-form-item>
        <el-form-item prop="ip_range" label="IP地址段:">
          <el-input v-model="formData.ip_range" placeholder="请输入IP地址段，如：***********/24" clearable />
        </el-form-item>
        <el-form-item prop="gateway_ip" label="网关IP:">
          <el-input v-model="formData.gateway_ip" placeholder="请输入网关IP，如：***********" clearable />
        </el-form-item>
        <el-form-item prop="firewall_ip" label="防火墙IP:">
          <el-input v-model="formData.firewall_ip" placeholder="请输入防火墙IP，如：*************" clearable />
        </el-form-item>
        <el-form-item prop="datacenter" label="所属机房:">
          <el-select v-model="formData.datacenter" placeholder="请选择所属机房" clearable>
            <el-option v-for="item in datacenters" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitEdit" :loading="submitLoading">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="dialogVisible.delete" title="删除线路" width="400" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-alert type="warning" :title="`确定要删除线路 '${formData.line_name}' 吗？`" :closable="false" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true">
        <el-form-item label="线路名称">
          <el-input v-model="search.line_name" placeholder="请输入线路名称" clearable />
        </el-form-item>
        <el-form-item label="运营商">
          <el-select v-model="search.provider" placeholder="请选择运营商" clearable>
            <el-option v-for="item in providers" :key="item.dict_code" :label="item.dict_name" :value="item.dict_code" />
          </el-select>
        </el-form-item>
        <el-form-item label=" " class="form-item-with-label search-buttons">
          <div class="button-container">
            <el-button type="primary" @click="getData">
              <el-icon>
                <Search />
              </el-icon>查询
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" @click="showAddDialog">
          <el-icon>
            <Plus />
          </el-icon>新增线路
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData" :loading="exportLoading">
          <el-icon>
            <Download />
          </el-icon>导出Excel
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" stripe border v-loading="loading">
        <el-table-column prop="line_name" label="线路名称" min-width="120" />
        <el-table-column prop="provider" label="运营商" width="100">
          <template #default="scope">
            {{ getProviderName(scope.row.provider) }}
          </template>
        </el-table-column>
        <el-table-column prop="line_type" label="线路类型" width="100">
          <template #default="scope">
            {{ getLineTypeName(scope.row.line_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="bandwidth" label="带宽" width="80" />
        <el-table-column prop="ip_range" label="IP地址段" width="140">
          <template #default="scope">
            {{ formatIpRange(scope.row.ip_range) }}
          </template>
        </el-table-column>
        <el-table-column prop="gateway_ip" label="网关IP" width="120">
          <template #default="scope">
            {{ scope.row.gateway_ip || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="firewall_ip" label="防火墙IP" width="120">
          <template #default="scope">
            {{ scope.row.firewall_ip || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="datacenter" label="所属机房" width="120">
          <template #default="scope">
            {{ getDatacenterName(scope.row.datacenter) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right" width="150">
          <template #default="scope">
            <div class="table-actions">
              <el-button size="small" type="warning" @click="editLine(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteLine(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Download } from '@element-plus/icons-vue'

export default {
  name: 'InternetLineManagement',
  components: {
    Plus,
    Search,
    Download
  },
  setup() {
    const loading = ref(false)
    const submitLoading = ref(false)
    const exportLoading = ref(false)
    const tableData = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)

    const dialogVisible = reactive({
      add: false,
      edit: false,
      delete: false
    })

    const addFormRef = ref(null)
    const editFormRef = ref(null)

    const search = reactive({
      line_name: '',
      provider: ''
    })

    const formData = reactive({
      id: null,
      line_name: '',
      provider: '',
      line_type: '',
      bandwidth: '',
      ip_range: '',
      gateway_ip: '',
      firewall_ip: '',
      datacenter: ''
    })

    const providers = ref([])
    const lineTypes = ref([])
    const datacenters = ref([])

    // IP地址段验证函数
    const validateIpRange = (rule, value, callback) => {
      if (!value) {
        // IP地址段为非必填字段，可以为空
        callback()
        return
      }

      // 检查是否包含斜杠
      if (!value.includes('/')) {
        callback(new Error('IP地址段格式不正确，应为 ***********/24 或 2001:db8::/32 格式'))
        return
      }

      const [ip, mask] = value.split('/')
      const maskNum = parseInt(mask)

      // 检查是否为IPv4
      const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
      if (ipv4Regex.test(ip)) {
        // IPv4验证
        const parts = ip.split('.')
        for (const part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            callback(new Error('IPv4地址格式不正确'))
            return
          }
        }
        if (maskNum < 0 || maskNum > 32) {
          callback(new Error('IPv4掩码应在0-32之间'))
          return
        }
      } else {
        // IPv6验证
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/
        
        if (!ipv6Regex.test(ip)) {
          callback(new Error('IPv6地址格式不正确'))
          return
        }
        
        if (maskNum < 0 || maskNum > 128) {
          callback(new Error('IPv6掩码应在0-128之间'))
          return
        }
      }

      callback()
    }

    // 网关IP验证函数
    const validateGatewayIp = (rule, value, callback) => {
      if (!value) {
        // 网关IP为非必填字段，可以为空
        callback()
        return
      }

      const trimmedIp = value.trim()

      // 检查是否为IPv4
      const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
      if (ipv4Regex.test(trimmedIp)) {
        // IPv4验证
        const parts = trimmedIp.split('.')
        for (const part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            callback(new Error('网关IPv4地址格式不正确，每段应在0-255之间'))
            return
          }
        }
      } else {
        // IPv6验证
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/
        
        if (!ipv6Regex.test(trimmedIp)) {
          callback(new Error('网关IP地址格式不正确，请输入有效的IPv4或IPv6地址'))
          return
        }
      }

      callback()
    }

    // 防火墙IP验证函数
    const validateFirewallIp = (rule, value, callback) => {
      if (!value) {
        // 防火墙IP为非必填字段，可以为空
        callback()
        return
      }

      const trimmedIp = value.trim()

      // 检查是否为IPv4
      const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
      if (ipv4Regex.test(trimmedIp)) {
        // IPv4验证
        const parts = trimmedIp.split('.')
        for (const part of parts) {
          const num = parseInt(part)
          if (num < 0 || num > 255) {
            callback(new Error('防火墙IPv4地址格式不正确，每段应在0-255之间'))
            return
          }
        }
      } else {
        // IPv6验证
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/
        
        if (!ipv6Regex.test(trimmedIp)) {
          callback(new Error('防火墙IP地址格式不正确，请输入有效的IPv4或IPv6地址'))
          return
        }
      }

      callback()
    }

    const rules = {
      line_name: [
        { required: true, message: '请输入线路名称', trigger: 'blur' }
      ],
      provider: [
        { required: true, message: '请选择运营商', trigger: 'change' }
      ],
      line_type: [
        { required: true, message: '请选择线路类型', trigger: 'change' }
      ],
      bandwidth: [
        { required: true, message: '请输入带宽', trigger: 'blur' }
      ],
      ip_range: [
        { validator: validateIpRange, trigger: 'blur' }
      ],
      gateway_ip: [
        { validator: validateGatewayIp, trigger: 'blur' }
      ],
      firewall_ip: [
        { validator: validateFirewallIp, trigger: 'blur' }
      ],
      datacenter: [
        { required: true, message: '请选择所属机房', trigger: 'change' }
      ]
    }

    return {
      loading,
      submitLoading,
      exportLoading,
      tableData,
      total,
      currentPage,
      pageSize,
      dialogVisible,
      addFormRef,
      editFormRef,
      search,
      formData,
      providers,
      lineTypes,
      datacenters,
      rules
    }
  },
  methods: {
    async getData() {
      this.loading = true
      try {
        const response = await this.$axios.post('/api/get_internet_lines', {
          pageSize: this.pageSize,
          currentPage: this.currentPage,
          lineName: this.search.line_name,
          provider: this.search.provider
        })

        if (response.data.code === 0) {
          this.tableData = response.data.msg
          this.total = response.data.total || 0
        } else {
          ElMessage.error(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    async getDictData() {
      try {
        const providerResponse = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_type: 'internet_provider'
        })
        if (providerResponse.data.code === 0) {
          this.providers = providerResponse.data.msg
        }

        const lineTypeResponse = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_type: 'internet_line_type'
        })
        if (lineTypeResponse.data.code === 0) {
          this.lineTypes = lineTypeResponse.data.msg
        }

        const datacenterResponse = await this.$axios.post('/api/get_cmdb_data_dictionary', {
          dict_type: 'A'
        })
        if (datacenterResponse.data.code === 0) {
          this.datacenters = datacenterResponse.data.msg
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
      }
    },

    resetSearch() {
      this.search.line_name = ''
      this.search.provider = ''
      this.currentPage = 1
      this.getData()
    },

    showAddDialog() {
      this.resetFormData()
      this.dialogVisible.add = true
    },

    editLine(row) {
      this.resetFormData()
      Object.assign(this.formData, row)
      this.dialogVisible.edit = true
    },

    deleteLine(row) {
      Object.assign(this.formData, row)
      this.dialogVisible.delete = true
    },

    resetFormData() {
      this.formData.id = null
      this.formData.line_name = ''
      this.formData.provider = ''
      this.formData.line_type = ''
      this.formData.bandwidth = ''
      this.formData.ip_range = ''
      this.formData.gateway_ip = ''
      this.formData.firewall_ip = ''
      this.formData.datacenter = ''
    },

    async validateAndSubmitAdd() {
      if (this.submitLoading) return
      try {
        await this.$refs.addFormRef.validate()
        await this.submitAdd()
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    async submitAdd() {
      this.submitLoading = true
      try {
        const response = await this.$axios.post('/api/add_internet_line', {
          lineName: this.formData.line_name,
          provider: this.getProviderName(this.formData.provider),
          lineType: this.getLineTypeName(this.formData.line_type),
          bandwidth: this.formData.bandwidth,
          ipRange: this.formData.ip_range,
          gatewayIp: this.formData.gateway_ip,
          firewallIp: this.formData.firewall_ip,
          datacenter: this.formData.datacenter,
          loginUsername: 'admin'
        })

        if (response.data.code === 0) {
          ElMessage.success('新增线路成功')
          this.dialogVisible.add = false
          this.resetFormData()
          this.getData()
        } else {
          ElMessage.error(response.data.msg || '新增失败')
        }
      } catch (error) {
        console.error('新增失败:', error)
        ElMessage.error('新增失败')
      } finally {
        this.submitLoading = false
      }
    },

    async validateAndSubmitEdit() {
      if (this.submitLoading) return
      try {
        await this.$refs.editFormRef.validate()
        await this.submitEdit()
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    async submitEdit() {
      this.submitLoading = true
      try {
        const response = await this.$axios.post('/api/update_internet_line', {
          id: this.formData.id,
          lineName: this.formData.line_name,
          provider: this.getProviderName(this.formData.provider),
          lineType: this.getLineTypeName(this.formData.line_type),
          bandwidth: this.formData.bandwidth,
          ipRange: this.formData.ip_range,
          gatewayIp: this.formData.gateway_ip,
          firewallIp: this.formData.firewall_ip,
          datacenter: this.formData.datacenter,
          loginUsername: 'admin'
        })

        if (response.data.code === 0) {
          ElMessage.success('更新线路成功')
          this.dialogVisible.edit = false
          this.getData()
        } else {
          ElMessage.error(response.data.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error('更新失败')
      } finally {
        this.submitLoading = false
      }
    },

    async submitDelete() {
      try {
        const response = await this.$axios.post('/api/delete_internet_line', {
          id: this.formData.id,
          loginUsername: 'admin'
        })

        if (response.data.code === 0) {
          ElMessage.success('删除线路成功')
          this.dialogVisible.delete = false
          this.getData()
        } else {
          ElMessage.error(response.data.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    },

    async exportData() {
      this.exportLoading = true
      try {
        const response = await this.$axios.post('/api/export_internet_line_data', {
          exportType: 'filtered',
          filters: {
            lineName: this.search.line_name,
            provider: this.search.provider
          },
          loginUsername: 'admin'
        }, {
          responseType: 'blob'
        })

        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `互联网线路数据_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
      } finally {
        this.exportLoading = false
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.getData()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },



    // 根据运营商代码获取运营商名称
    getProviderName(providerCode) {
      if (!providerCode || !this.providers.length) {
        return providerCode || '-'
      }
      const provider = this.providers.find(item => item.dict_code === providerCode)
      return provider ? provider.dict_name : providerCode
    },

    // 根据机房代码获取机房名称
    getDatacenterName(datacenterCode) {
      if (!datacenterCode || !this.datacenters.length) {
        return datacenterCode || '-'
      }
      const datacenter = this.datacenters.find(item => item.dict_code === datacenterCode)
      return datacenter ? datacenter.dict_name : datacenterCode
    },

    // 根据线路类型代码获取线路类型名称
    getLineTypeName(lineTypeCode) {
      if (!lineTypeCode || !this.lineTypes.length) {
        return lineTypeCode || '-'
      }
      const lineType = this.lineTypes.find(item => item.dict_code === lineTypeCode)
      return lineType ? lineType.dict_name : lineTypeCode
    },

    // 格式化IP地址段显示
    formatIpRange(ipRange) {
      if (!ipRange) {
        return '-'
      }
      
      // 如果IP地址段太长，显示缩写形式
      if (ipRange.length > 18) {
        // 对于IPv6地址，显示前面部分和后面部分
        if (ipRange.includes(':')) {
          const parts = ipRange.split('/')
          if (parts.length === 2) {
            const ip = parts[0]
            const prefix = parts[1]
            // 简化IPv6显示
            if (ip.length > 15) {
              const ipParts = ip.split(':')
              if (ipParts.length >= 4) {
                // 如果已经有::，保持原样但截取前两段
                if (ip.includes('::')) {
                  return `${ipParts[0]}:${ipParts[1]}::/${prefix}`
                } else {
                  // 如果没有::，创建缩写形式
                  return `${ipParts[0]}:${ipParts[1]}::${ipParts[ipParts.length-1]}/${prefix}`
                }
              }
            }
          }
        }
        // 对于过长的IPv4地址段，也进行缩写
        return ipRange.length > 20 ? ipRange.substring(0, 17) + '...' : ipRange
      }
      
      return ipRange
    }
  },
  mounted() {
    this.getDictData()
    this.getData()
  }
}
</script>

<style lang="scss" scoped>
.internet-line-management {
  padding: 0;

  .search-card {
    margin-bottom: 10px;
  }

  .table-card {
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

/* 统一操作按钮区样式 */
.unified-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: nowrap;
}

.action-bar-left,
.action-bar-right {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 按钮容器 */
.button-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

/* 搜索按钮对齐 */
.search-buttons-col {
  display: flex;
  align-items: center;
}

.search-buttons {
  margin-bottom: 0;
  text-align: right;
  width: 100%;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  min-width: 120px; /* 固定最小宽度防止移动 */
}

.table-actions .el-button {
  margin: 0; /* 移除默认margin */
  flex-shrink: 0; /* 防止按钮收缩 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-bar-left,
  .action-bar-right {
    margin-bottom: 8px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-bar-right {
    justify-content: flex-end;
  }
}
</style>