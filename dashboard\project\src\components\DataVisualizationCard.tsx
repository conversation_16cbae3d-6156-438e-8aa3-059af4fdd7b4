import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity } from 'lucide-react';

interface DataPoint {
  label: string;
  value: number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  color?: string;
}

interface DataVisualizationCardProps {
  title: string;
  data: DataPoint[];
  type?: 'bar' | 'line' | 'area' | 'radial';
  className?: string;
  animationDelay?: number;
  showTrend?: boolean;
  height?: number;
}

const DataVisualizationCard: React.FC<DataVisualizationCardProps> = ({
  title,
  data,
  type = 'bar',
  className = '',
  animationDelay = 0,
  showTrend = true,
  height = 200
}) => {
  const [animatedData, setAnimatedData] = useState<number[]>([]);
  const maxValue = Math.max(...data.map(d => d.value));

  useEffect(() => {
    const timer = setTimeout(() => {
      data.forEach((item, index) => {
        setTimeout(() => {
          setAnimatedData(prev => {
            const newData = [...prev];
            newData[index] = item.value;
            return newData;
          });
        }, index * 100);
      });
    }, animationDelay);

    return () => clearTimeout(timer);
  }, [data, animationDelay]);

  const renderBarChart = () => (
    <div className="flex items-end justify-between space-x-2" style={{ height }}>
      {data.map((item, index) => {
        const percentage = (animatedData[index] || 0) / maxValue * 100;
        const color = item.color || '#00d4ff';
        
        return (
          <div key={item.label} className="flex flex-col items-center flex-1">
            <div className="relative w-full flex-1 flex items-end">
              <div
                className="w-full rounded-t-lg transition-all duration-1000 ease-out relative overflow-hidden"
                style={{
                  height: `${percentage}%`,
                  background: `linear-gradient(180deg, ${color}80 0%, ${color} 100%)`,
                  boxShadow: `0 0 20px ${color}40`,
                  minHeight: '4px'
                }}
              >
                {/* 发光顶部 */}
                <div
                  className="absolute top-0 left-0 right-0 h-1 opacity-80"
                  style={{
                    background: color,
                    boxShadow: `0 0 10px ${color}`
                  }}
                />
                
                {/* 数据流动效果 */}
                <div
                  className="absolute inset-0 opacity-30"
                  style={{
                    background: `repeating-linear-gradient(
                      0deg,
                      transparent 0px,
                      ${color}20 2px,
                      transparent 4px
                    )`,
                    animation: 'dataStream 3s linear infinite'
                  }}
                />
              </div>
              
              {/* 数值显示 */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                <span
                  className="text-xs font-bold px-2 py-1 rounded"
                  style={{
                    color: color,
                    backgroundColor: `${color}20`,
                    border: `1px solid ${color}40`
                  }}
                >
                  {animatedData[index] || 0}
                </span>
              </div>
            </div>
            
            {/* 标签和趋势 */}
            <div className="mt-2 text-center">
              <div className="text-xs text-gray-300 mb-1">{item.label}</div>
              {showTrend && item.change !== undefined && (
                <div className="flex items-center justify-center space-x-1">
                  {item.trend === 'up' ? (
                    <TrendingUp className="w-3 h-3 text-neon-green" />
                  ) : item.trend === 'down' ? (
                    <TrendingDown className="w-3 h-3 text-neon-red" />
                  ) : (
                    <Activity className="w-3 h-3 text-gray-400" />
                  )}
                  <span
                    className={`text-xs font-medium ${
                      item.trend === 'up' ? 'text-neon-green' :
                      item.trend === 'down' ? 'text-neon-red' : 'text-gray-400'
                    }`}
                  >
                    {item.change > 0 ? '+' : ''}{item.change}%
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );

  const renderRadialChart = () => (
    <div className="flex justify-center items-center" style={{ height }}>
      <div className="relative">
        <svg width="180" height="180" viewBox="0 0 180 180">
          <defs>
            {data.map((item, index) => (
              <linearGradient key={index} id={`radial-gradient-${index}`} x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={item.color || '#00d4ff'} />
                <stop offset="100%" stopColor={`${item.color || '#00d4ff'}80`} />
              </linearGradient>
            ))}
          </defs>
          
          {data.map((item, index) => {
            const radius = 60 - index * 15;
            const circumference = 2 * Math.PI * radius;
            const percentage = (animatedData[index] || 0) / maxValue * 100;
            const strokeDashoffset = circumference - (percentage / 100) * circumference;
            
            return (
              <g key={index}>
                {/* 背景圆环 */}
                <circle
                  cx="90"
                  cy="90"
                  r={radius}
                  fill="none"
                  stroke="rgba(255, 255, 255, 0.1)"
                  strokeWidth="8"
                />
                
                {/* 进度圆环 */}
                <circle
                  cx="90"
                  cy="90"
                  r={radius}
                  fill="none"
                  stroke={`url(#radial-gradient-${index})`}
                  strokeWidth="8"
                  strokeDasharray={circumference}
                  strokeDashoffset={strokeDashoffset}
                  strokeLinecap="round"
                  transform="rotate(-90 90 90)"
                  style={{
                    transition: 'stroke-dashoffset 1s ease-out',
                    filter: `drop-shadow(0 0 6px ${item.color || '#00d4ff'}40)`
                  }}
                />
              </g>
            );
          })}
        </svg>
        
        {/* 中心数据 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold tech-gradient-text">
              {animatedData.reduce((sum, val) => sum + (val || 0), 0)}
            </div>
            <div className="text-xs text-gray-400">总计</div>
          </div>
        </div>
      </div>
      
      {/* 图例 */}
      <div className="ml-6 space-y-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{
                backgroundColor: item.color || '#00d4ff',
                boxShadow: `0 0 6px ${item.color || '#00d4ff'}60`
              }}
            />
            <div className="text-xs">
              <div className="text-gray-300">{item.label}</div>
              <div className="font-bold" style={{ color: item.color || '#00d4ff' }}>
                {animatedData[index] || 0}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div
      className={`glass-card rounded-xl p-6 hover-lift entrance-animation ${className}`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      {/* 标题 */}
      <div className="flex flex-col items-center mb-4">
        <h3 className="text-lg font-bold tech-gradient-text text-center mb-2">{title}</h3>
        <div className="w-2 h-2 bg-neon-green rounded-full pulse-glow" />
      </div>
      
      {/* 图表内容 */}
      <div className="relative">
        {type === 'radial' ? renderRadialChart() : renderBarChart()}
        
        {/* 扫描线效果 */}
        <div className="absolute inset-0 scan-line opacity-20" />
      </div>
      
      {/* 数据流指示器 */}
      <div className="mt-4 flex justify-center">
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-1 h-1 bg-neon-blue rounded-full opacity-60"
              style={{
                animation: `particleTwinkle 1.5s ease-in-out infinite`,
                animationDelay: `${i * 0.2}s`
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default DataVisualizationCard;