const { validateAllConfigs, getZabbixConfig } = require('./config');
const zabbixApiService = require('../services/zabbixApiService');

/**
 * 启动时配置验证
 * 在应用启动时验证所有关键配置
 */
async function validateStartupConfigs() {
    try {
        // 验证所有配置
        const validationResults = validateAllConfigs();
        
        // 输出验证结果
        if (validationResults.overall.isValid) {
            // 配置验证通过
        } else {
            // 配置验证发现问题
            // 配置验证错误已记录
        }
        
        // 特别处理Zabbix配置
        if (validationResults.zabbix.isValid) {
            const zabbixConfig = getZabbixConfig();
            if (zabbixConfig.enabled) {
                // Zabbix API配置有效，服务已启用
                
                // 测试Zabbix API连接
                try {
                    const connectionTest = await zabbixApiService.testConnection();
                    if (connectionTest.success) {
                        // Zabbix API连接测试成功
                    } else {
                        // Zabbix API连接测试失败
                    }
                } catch (testError) {
                    // Zabbix API连接测试异常
                }
            } else {
                // Zabbix API服务已禁用
            }
        } else {
            // Zabbix API配置无效
        }
        
        return validationResults;
        
    } catch (error) {
        return {
            overall: { isValid: false, errors: [`配置验证异常: ${error.message}`] }
        };
    }
}

/**
 * 输出配置状态摘要
 */
function logConfigSummary() {
    try {
        const zabbixConfig = getZabbixConfig();
        const zabbixStatus = zabbixApiService.getConfigStatus();
        
        // 配置状态摘要已记录
        
    } catch (error) {
        // 输出配置摘要时发生错误
    }
}

module.exports = {
    validateStartupConfigs,
    logConfigSummary
};