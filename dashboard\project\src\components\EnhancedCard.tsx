import React from 'react';

interface EnhancedCardProps {
  title?: string;
  children: React.ReactNode;
  glowEffect?: boolean;
  animationDelay?: number;
  className?: string;
  icon?: React.ReactNode;
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  title,
  children,
  glowEffect = false,
  animationDelay = 0,
  className = '',
  icon
}) => {
  const glowClass = glowEffect ? 'neon-glow' : '';
  const animationStyle = {
    animationDelay: `${animationDelay}ms`
  };

  return (
    <div 
      className={`glass-card rounded-xl p-6 smooth-transition hover-lift entrance-animation ${glowClass} ${className}`}
      style={animationStyle}
    >
      {title && (
        <div className="flex items-center justify-center mb-4">
          {icon && (
            <div className="text-neon-blue mr-3">
              {icon}
            </div>
          )}
          <h3 className="text-lg font-bold tech-gradient-text text-center">
            {title}
          </h3>
        </div>
      )}
      {children}
    </div>
  );
};

export default EnhancedCard;