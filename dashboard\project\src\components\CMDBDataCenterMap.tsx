import React, { useState, useEffect } from 'react';
import { MapPin, Server, Network, Database, RefreshCw } from 'lucide-react';
import { dataCenterService, DataCenterLocation } from '../services/dataCenterService';
import ChinaMapSVG from './ChinaMapSVG';

interface CMDBDataCenterMapProps {
  title: string;
  icon?: React.ReactNode;
  className?: string;
  animationDelay?: number;
  refreshInterval?: number; // 自动刷新间隔（毫秒）
}

const CMDBDataCenterMap: React.FC<CMDBDataCenterMapProps> = ({
  title,
  icon,
  className = '',
  animationDelay = 0,
  refreshInterval = 300000 // 默认5分钟刷新一次
}) => {
  const [locations, setLocations] = useState<DataCenterLocation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [activeLocation, setActiveLocation] = useState<string | null>(null);

  // 获取设备分布数据
  const fetchDeviceDistribution = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await dataCenterService.getDataCenterDistribution();
      setLocations(data);
      setLastUpdate(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
      console.error('获取设备分布数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化和定时刷新
  useEffect(() => {
    // 延迟初始加载
    const initialTimer = setTimeout(() => {
      fetchDeviceDistribution();
    }, animationDelay);

    // 设置定时刷新
    const refreshTimer = setInterval(() => {
      fetchDeviceDistribution();
    }, refreshInterval);

    return () => {
      clearTimeout(initialTimer);
      clearInterval(refreshTimer);
    };
  }, [animationDelay, refreshInterval]);

  const getStatusColor = (status: DataCenterLocation['status']) => {
    switch (status) {
      case 'normal': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
    }
  };

  const getDeviceTypeIcon = (type: 'network' | 'physical' | 'virtual') => {
    switch (type) {
      case 'network': return <Network className="w-3 h-3" />;
      case 'physical': return <Server className="w-3 h-3" />;
      case 'virtual': return <Database className="w-3 h-3" />;
    }
  };

  const formatLastUpdate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div
      className={`glass-card rounded-xl p-6 hover-lift entrance-animation ${className}`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      {/* 标题和状态 */}
      <div className="flex flex-col items-center mb-4">
        <div className="flex items-center space-x-2 mb-2">
          {icon && (
            <div className="text-neon-blue">
              {icon}
            </div>
          )}
          <h3 className="text-lg font-bold tech-gradient-text text-center">{title}</h3>
        </div>
        <div className="flex items-center space-x-3">
          {loading && (
            <RefreshCw className="w-4 h-4 text-neon-blue animate-spin" />
          )}
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${error ? 'bg-neon-red' : 'bg-neon-green'} pulse-glow`} />
            <span className="text-xs text-gray-400">
              {error ? '连接异常' : '实时数据'}
            </span>
          </div>
          {lastUpdate && (
            <span className="text-xs text-gray-500">
              {formatLastUpdate(lastUpdate)}
            </span>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-3 bg-neon-red/10 border border-neon-red/20 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-neon-red rounded-full" />
            <span className="text-sm text-neon-red">{error}</span>
            <button
              onClick={fetchDeviceDistribution}
              className="ml-auto text-xs text-neon-blue hover:text-neon-cyan transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 地图容器 */}
      <div className="relative h-64 rounded-lg overflow-hidden hologram mb-4">
        {/* 中国地图背景 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-full">
            <ChinaMapSVG 
              fillColor="rgba(0, 212, 255, 0.08)"
              strokeColor="rgba(0, 212, 255, 0.25)"
              strokeWidth={1}
            />
          </div>
        </div>

        {/* 背景网格 */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        />

        {/* 位置点 */}
        {locations.map((location, index) => (
          <div
            key={location.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
            style={{
              left: `${location.coordinates?.x || 50}%`,
              top: `${location.coordinates?.y || 50}%`,
              animation: `scaleIn 0.5s ease-out ${index * 0.1}s both`
            }}
            onMouseEnter={() => setActiveLocation(location.id)}
            onMouseLeave={() => setActiveLocation(null)}
          >
            {/* 脉冲圆环 */}
            <div
              className="absolute inset-0 rounded-full animate-ping"
              style={{
                backgroundColor: getStatusColor(location.status),
                width: '20px',
                height: '20px',
                left: '-10px',
                top: '-10px'
              }}
            />

            {/* 主要节点 */}
            <div
              className="relative w-5 h-5 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-125"
              style={{
                backgroundColor: getStatusColor(location.status),
                boxShadow: `0 0 15px ${getStatusColor(location.status)}60`,
                border: `2px solid ${getStatusColor(location.status)}`
              }}
            >
              <MapPin className="w-3 h-3 text-white" />
            </div>

            {/* 悬浮信息卡 */}
            {activeLocation === location.id && (
              <div
                className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 entrance-animation"
                style={{ minWidth: '200px' }}
              >
                <div className="glass-card rounded-lg p-4 text-sm">
                  <div className="font-bold text-white mb-2">{location.name}</div>

                  {/* 设备统计 */}
                  <div className="space-y-2 mb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getDeviceTypeIcon('network')}
                        <span className="text-gray-400">网络设备:</span>
                      </div>
                      <span className="text-neon-blue font-bold">{location.networkDevices}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getDeviceTypeIcon('physical')}
                        <span className="text-gray-400">实体服务器:</span>
                      </div>
                      <span className="text-neon-green font-bold">{location.physicalServers}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getDeviceTypeIcon('virtual')}
                        <span className="text-gray-400">虚拟设备:</span>
                      </div>
                      <span className="text-neon-cyan font-bold">{location.virtualServers}</span>
                    </div>
                  </div>

                  {/* 总计和状态 */}
                  <div className="pt-2 border-t border-gray-600">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">总设备数:</span>
                      <span className="text-white font-bold">{location.totalDevices}</span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-gray-400">状态:</span>
                      <span
                        className="font-medium text-xs px-2 py-1 rounded"
                        style={{
                          color: getStatusColor(location.status),
                          backgroundColor: `${getStatusColor(location.status)}20`
                        }}
                      >
                        {location.status === 'normal' ? '正常' :
                          location.status === 'warning' ? '警告' : '异常'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 箭头 */}
                <div
                  className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
                  style={{
                    borderLeft: '6px solid transparent',
                    borderRight: '6px solid transparent',
                    borderTop: '6px solid rgba(255, 255, 255, 0.1)'
                  }}
                />
              </div>
            )}
          </div>
        ))}

        {/* 加载状态 */}
        {loading && locations.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 text-neon-blue animate-spin mx-auto mb-2" />
              <div className="text-sm text-gray-400">加载设备分布数据...</div>
            </div>
          </div>
        )}

        {/* 扫描效果 */}
        <div className="absolute inset-0 pointer-events-none">
          <div
            className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-neon-cyan to-transparent opacity-60"
            style={{
              animation: 'scanMove 4s ease-in-out infinite',
              top: '20%'
            }}
          />
        </div>
      </div>

      {/* 统计汇总 */}
      <div className="grid grid-cols-4 gap-3 text-center">
        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <Network className="w-4 h-4 text-neon-blue mr-1" />
            <span className="text-xs text-gray-400">网络</span>
          </div>
          <div className="text-lg font-bold text-neon-blue">
            {locations.reduce((sum, loc) => sum + loc.networkDevices, 0)}
          </div>
        </div>

        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <Server className="w-4 h-4 text-neon-green mr-1" />
            <span className="text-xs text-gray-400">实体</span>
          </div>
          <div className="text-lg font-bold text-neon-green">
            {locations.reduce((sum, loc) => sum + loc.physicalServers, 0)}
          </div>
        </div>

        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <Database className="w-4 h-4 text-neon-cyan mr-1" />
            <span className="text-xs text-gray-400">虚拟</span>
          </div>
          <div className="text-lg font-bold text-neon-cyan">
            {locations.reduce((sum, loc) => sum + loc.virtualServers, 0)}
          </div>
        </div>

        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <MapPin className="w-4 h-4 text-neon-purple mr-1" />
            <span className="text-xs text-gray-400">机房</span>
          </div>
          <div className="text-lg font-bold text-neon-purple">
            {locations.length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CMDBDataCenterMap;