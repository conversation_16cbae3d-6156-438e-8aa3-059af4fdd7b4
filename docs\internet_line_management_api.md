# 互联网线路管理API文档

## 概述

互联网线路管理模块提供了完整的企业机房互联网线路资源管理功能，包括线路信息管理、IP映射管理、费用统计和合同管理等核心能力。

## 基础信息

- **基础路径**: `/api`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": 0,           // 0: 成功, 1: 失败
  "msg": {},           // 响应数据或错误信息
  "total": 0           // 总记录数（分页接口）
}
```

## 互联网线路管理API

### 1. 获取线路列表

**接口地址**: `POST /api/get_internet_lines`

**请求参数**:
```json
{
  "pageSize": 10,              // 页面大小
  "currentPage": 1,            // 当前页码
  "lineName": "",              // 线路名称（模糊搜索）
  "provider": "",              // 运营商
  "lineType": "",              // 线路类型
  "datacenter": "",            // 所属机房
  "bandwidth": "",             // 带宽
  "contractNumber": "",        // 合同编号
  "accessMethod": "",          // 接入方式
  "minMonthlyFee": 0,          // 最小月费用
  "maxMonthlyFee": 0,          // 最大月费用
  "contractStatus": "",        // 合同状态: active, expired, expiring
  "keyword": "",               // 关键词搜索
  "sortProp": "id",            // 排序字段
  "sortOrder": "desc"          // 排序方向: asc, desc
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": [
    {
      "id": 1,
      "line_name": "电信专线1",
      "provider": "中国电信",
      "line_type": "专线",
      "bandwidth": "100M",
      "ip_range": "***********/24",
      "datacenter": "机房A",
      "access_method": "光纤接入",
      "line_purpose": "业务系统访问",
      "contract_number": "CT2024001",
      "contract_start_date": "2024-01-01",
      "contract_end_date": "2025-12-31",
      "monthly_fee": 5000.00,
      "remarks": "主要用于生产环境",
      "contract_status": "有效",
      "days_to_expiry": 365,
      "created_at": "2024-01-01T00:00:00Z",
      "created_by": "admin",
      "updated_at": "2024-01-01T00:00:00Z",
      "updated_by": "admin"
    }
  ],
  "total": 1
}
```

### 2. 添加线路

**接口地址**: `POST /api/add_internet_line`

**请求参数**:
```json
{
  "lineName": "电信专线1",           // 必填，线路名称
  "provider": "中国电信",           // 必填，运营商
  "lineType": "专线",              // 必填，线路类型
  "bandwidth": "100M",             // 必填，带宽
  "ipRange": "***********/24",     // 可选，IP段
  "datacenter": "机房A",           // 必填，所属机房
  "accessMethod": "光纤接入",       // 可选，接入方式
  "linePurpose": "业务系统访问",    // 可选，线路用途
  "contractNumber": "CT2024001",   // 可选，合同编号
  "contractStartDate": "2024-01-01", // 可选，合同开始日期
  "contractEndDate": "2025-12-31",   // 可选，合同结束日期
  "monthlyFee": 5000.00,           // 可选，月费用
  "remarks": "备注信息",           // 可选，备注
  "loginUsername": "admin"         // 必填，操作用户
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": {
    "id": 1,
    "line_name": "电信专线1",
    // ... 其他字段
  }
}
```

**错误响应**:
```json
{
  "code": 1,
  "msg": "线路名称已存在，请使用其他名称"
}
```

### 3. 更新线路

**接口地址**: `POST /api/update_internet_line`

**请求参数**:
```json
{
  "id": 1,                        // 必填，线路ID
  "lineName": "电信专线1",         // 必填，线路名称
  "provider": "中国电信",          // 必填，运营商
  // ... 其他字段同添加接口
  "loginUsername": "admin"        // 必填，操作用户
}
```

### 4. 删除线路

**接口地址**: `POST /api/delete_internet_line`

**请求参数**:
```json
{
  "id": 1,                        // 必填，线路ID
  "loginUsername": "admin"        // 必填，操作用户
}
```

**注意**: 如果线路下存在IP映射记录，将无法删除，需要先删除相关的IP映射记录。

### 5. 获取费用统计

**接口地址**: `POST /api/get_cost_statistics`

**请求参数**:
```json
{
  "startDate": "2024-01-01",      // 可选，开始日期
  "endDate": "2024-12-31",        // 可选，结束日期
  "groupBy": "provider",          // 必填，分组方式: provider, lineType, datacenter
  "loginUsername": "admin"        // 必填，操作用户
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": {
    "groupBy": "provider",
    "statistics": [
      {
        "group_name": "中国电信",
        "line_count": 5,
        "total_monthly_fee": 25000.00,
        "avg_monthly_fee": 5000.00,
        "min_monthly_fee": 2000.00,
        "max_monthly_fee": 10000.00,
        "total_annual_fee": 300000.00
      }
    ],
    "total": {
      "total_lines": 10,
      "total_monthly_cost": 50000.00,
      "avg_monthly_cost": 5000.00,
      "total_annual_cost": 600000.00,
      "expired_contracts": 2,
      "expiring_contracts": 3
    },
    "providerStats": [
      {
        "provider": "中国电信",
        "count": 5,
        "monthly_fee": 25000.00
      }
    ],
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-12-31"
    }
  }
}
```

### 6. 获取合同到期提醒

**接口地址**: `POST /api/get_contract_expiry_alerts`

**请求参数**:
```json
{
  "alertDays": 30,                // 可选，提前提醒天数，默认30天
  "loginUsername": "admin"        // 必填，操作用户
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": {
    "alerts": [
      {
        "id": 1,
        "line_name": "电信专线1",
        "provider": "中国电信",
        "line_type": "专线",
        "datacenter": "机房A",
        "contract_number": "CT2024001",
        "contract_start_date": "2024-01-01",
        "contract_end_date": "2024-12-31",
        "monthly_fee": 5000.00,
        "alert_type": "即将过期",
        "days_to_expiry": 15,
        "annual_fee": 60000.00
      }
    ],
    "summary": {
      "expiredCount": 2,
      "expiringCount": 3,
      "totalCount": 5,
      "totalAffectedMonthlyFee": 15000.00,
      "totalAffectedAnnualFee": 180000.00,
      "alertDays": 30
    }
  }
}
```

### 7. 导出线路数据

**接口地址**: `POST /api/export_internet_line_data`

**请求参数**:
```json
{
  "exportType": "all",            // 导出类型: all, filtered
  "filters": {                    // 筛选条件（当exportType为filtered时）
    "lineName": "",
    "provider": "",
    "lineType": "",
    "datacenter": "",
    "contractStatus": ""
  },
  "loginUsername": "admin"        // 必填，操作用户
}
```

**响应**: Excel文件流
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- Content-Disposition: `attachment; filename="互联网线路数据_YYYY-MM-DD.xlsx"`

## IP映射管理API

### 1. 获取IP映射列表

**接口地址**: `POST /api/get_ip_mappings`

**请求参数**:
```json
{
  "pageSize": 10,              // 页面大小
  "currentPage": 1,            // 当前页码
  "internetIp": "",            // 互联网IP（模糊搜索）
  "dmzIp": "",                 // DMZ-IP（模糊搜索）
  "managementIp": "",          // 管理IP（模糊搜索）
  "sourcePort": "",            // 源端口
  "mappedPort": "",            // 映射端口
  "protocol": "",              // 协议: TCP, UDP
  "description": "",           // 功能说明
  "datacenter": "",            // 所属机房
  "lineId": 0,                 // 线路ID
  "assignedUser": "",          // 使用人
  "status": "",                // 状态: active, inactive
  "keyword": "",               // 关键词搜索
  "sortProp": "id",            // 排序字段
  "sortOrder": "desc"          // 排序方向
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": [
    {
      "id": 1,
      "internet_ip": "************",
      "dmz_ip": "**************",
      "management_ip": "*********",
      "source_port": "80",
      "mapped_port": "8080",
      "protocol": "TCP",
      "description": "Web服务器",
      "datacenter": "机房A",
      "whitelist": "[\"***********/24\", \"10.0.0.0/8\"]",
      "line_id": 1,
      "assigned_user": "张三",
      "status": "active",
      "line_name": "电信专线1",
      "provider": "中国电信",
      "line_type": "专线",
      "created_at": "2024-01-01T00:00:00Z",
      "created_by": "admin",
      "updated_at": "2024-01-01T00:00:00Z",
      "updated_by": "admin"
    }
  ],
  "total": 1
}
```

### 2. 添加IP映射

**接口地址**: `POST /api/add_ip_mapping`

**请求参数**:
```json
{
  "internetIp": "************",      // 必填，互联网IP
  "dmzIp": "**************",         // 可选，DMZ-IP
  "managementIp": "*********",       // 可选，管理IP
  "sourcePort": "80",                // 可选，源端口
  "mappedPort": "8080",              // 可选，映射端口
  "protocol": "TCP",                 // 必填，协议
  "description": "Web服务器",         // 可选，功能说明
  "datacenter": "机房A",             // 必填，所属机房
  "whitelist": ["***********/24"],   // 可选，白名单IP列表
  "lineId": 1,                       // 必填，使用的线路ID
  "assignedUser": "张三",            // 可选，使用人
  "status": "active",                // 可选，状态，默认active
  "loginUsername": "admin"           // 必填，操作用户
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": {
    "id": 1,
    "internet_ip": "************",
    // ... 其他字段
  }
}
```

**错误响应**:
```json
{
  "code": 1,
  "msg": "IP地址和端口组合已存在冲突"
}
```

### 3. 更新IP映射

**接口地址**: `POST /api/update_ip_mapping`

**请求参数**:
```json
{
  "id": 1,                           // 必填，映射ID
  "internetIp": "************",      // 必填，互联网IP
  // ... 其他字段同添加接口
  "loginUsername": "admin"           // 必填，操作用户
}
```

### 4. 删除IP映射

**接口地址**: `POST /api/delete_ip_mapping`

**请求参数**:
```json
{
  "id": 1,                        // 必填，映射ID
  "loginUsername": "admin"        // 必填，操作用户
}
```

### 5. 导出IP映射数据

**接口地址**: `POST /api/export_ip_mapping_data`

**请求参数**:
```json
{
  "exportType": "all",            // 导出类型: all, filtered
  "filters": {                    // 筛选条件
    "internetIp": "",
    "dmzIp": "",
    "managementIp": "",
    "protocol": "",
    "datacenter": "",
    "lineId": 0,
    "assignedUser": ""
  },
  "loginUsername": "admin"        // 必填，操作用户
}
```

**响应**: Excel文件流

## 数据验证规则

### 线路数据验证

1. **线路名称**: 必填，2-100字符，只能包含中文、英文、数字、下划线和横线
2. **运营商**: 必填，从数据字典中选择
3. **线路类型**: 必填，从数据字典中选择
4. **带宽**: 必填，格式如"100M"、"1G"等
5. **IP段**: 可选，必须是有效的CIDR格式，如"***********/24"
6. **所属机房**: 必填
7. **月费用**: 可选，必须为非负数
8. **合同日期**: 可选，开始日期必须早于结束日期

### IP映射数据验证

1. **互联网IP**: 必填，必须是有效的IPv4地址格式
2. **DMZ-IP**: 可选，必须是有效的IPv4地址格式
3. **管理IP**: 可选，必须是有效的IPv4地址格式
4. **端口**: 可选，必须在1-65535范围内
5. **协议**: 必填，只能是TCP或UDP
6. **所属机房**: 必填
7. **线路ID**: 必填，必须是存在的线路ID
8. **白名单**: 可选，必须是有效的IP地址或CIDR格式数组

## 错误代码说明

| 错误代码 | 说明 |
|---------|------|
| 0 | 操作成功 |
| 1 | 操作失败 |

常见错误信息：
- "线路名称已存在，请使用其他名称"
- "IP地址格式不正确"
- "端口号必须在1-65535之间"
- "该线路下存在IP映射记录，无法删除"
- "IP地址和端口组合已存在冲突"
- "指定的线路不存在"

## 使用示例

### JavaScript/Axios示例

```javascript
// 获取线路列表
const getInternetLines = async () => {
  try {
    const response = await axios.post('/api/get_internet_lines', {
      pageSize: 10,
      currentPage: 1,
      provider: '中国电信'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 0) {
      console.log('线路列表:', response.data.msg);
      console.log('总数:', response.data.total);
    } else {
      console.error('获取失败:', response.data.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};

// 添加线路
const addInternetLine = async (lineData) => {
  try {
    const response = await axios.post('/api/add_internet_line', {
      ...lineData,
      loginUsername: 'admin'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 0) {
      console.log('添加成功:', response.data.msg);
    } else {
      console.error('添加失败:', response.data.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};
```

### cURL示例

```bash
# 获取线路列表
curl -X POST http://localhost:3000/api/get_internet_lines \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "currentPage": 1,
    "provider": "中国电信"
  }'

# 添加线路
curl -X POST http://localhost:3000/api/add_internet_line \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "lineName": "测试线路",
    "provider": "中国电信",
    "lineType": "专线",
    "bandwidth": "100M",
    "datacenter": "机房A",
    "loginUsername": "admin"
  }'
```

## 注意事项

1. **认证**: 所有接口都需要有效的JWT Token
2. **权限**: 用户需要有相应的操作权限（I,U,D）
3. **日志**: 所有操作都会记录到用户操作日志表
4. **事务**: 涉及多表操作的接口使用数据库事务保证数据一致性
5. **性能**: 大数据量查询建议使用分页和适当的筛选条件
6. **缓存**: 数据字典等相对静态的数据建议在前端进行缓存

## 更新日志

- **v1.0.0** (2024-01-01): 初始版本，包含基础的线路和IP映射管理功能
- **v1.1.0** (2024-02-01): 增加费用统计和合同到期提醒功能
- **v1.2.0** (2024-03-01): 增加数据导入导出功能
- **v1.3.0** (2024-04-01): 优化搜索和筛选功能，增加关键词搜索