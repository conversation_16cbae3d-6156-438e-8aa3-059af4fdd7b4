/**
 * 通用验证服务
 */

const { ValidationError } = require('../middlewares/errorHandler');

// IP地址验证
const validateIP = (ip, fieldName = 'IP地址') => {
  if (!ip) return true; // 允许空值，由required验证处理
  
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipRegex.test(ip)) {
    throw new ValidationError(`${fieldName}格式不正确`);
  }
  
  const parts = ip.split('.');
  const valid = parts.every(part => {
    const num = parseInt(part);
    return num >= 0 && num <= 255;
  });
  
  if (!valid) {
    throw new ValidationError(`${fieldName}范围应在0-255之间`);
  }
  
  return true;
};

// CIDR格式验证（支持IPv4和IPv6）
const validateCIDR = (cidr, fieldName = 'CIDR') => {
  if (!cidr) return true;
  
  if (!cidr.includes('/')) {
    throw new ValidationError(`${fieldName}格式不正确，应为 ***********/24 或 2001:db8::/32 格式`);
  }
  
  const [ip, mask] = cidr.split('/');
  const maskNum = parseInt(mask);
  
  // 检查是否为IPv4
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipv4Regex.test(ip)) {
    // IPv4验证
    validateIP(ip, fieldName);
    if (maskNum < 0 || maskNum > 32) {
      throw new ValidationError(`${fieldName}的IPv4掩码应在0-32之间`);
    }
  } else {
    // IPv6验证
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/;
    
    if (!ipv6Regex.test(ip)) {
      throw new ValidationError(`${fieldName}中的IPv6地址格式不正确`);
    }
    
    if (maskNum < 0 || maskNum > 128) {
      throw new ValidationError(`${fieldName}的IPv6掩码应在0-128之间`);
    }
  }
  
  return true;
};

// 端口验证
const validatePort = (port, fieldName = '端口') => {
  if (!port) return true;
  
  // 支持单个端口、端口范围、端口列表
  const portRegex = /^(\d+(-\d+)?)(,\d+(-\d+)?)*$/;
  if (!portRegex.test(port)) {
    throw new ValidationError(`${fieldName}格式不正确，支持格式：80 或 80-90 或 80,81,82`);
  }
  
  const ports = port.split(',');
  for (const p of ports) {
    if (p.includes('-')) {
      const [start, end] = p.split('-').map(num => parseInt(num));
      if (start < 1 || start > 65535 || end < 1 || end > 65535 || start > end) {
        throw new ValidationError(`${fieldName}范围应在1-65535之间，且起始端口不能大于结束端口`);
      }
    } else {
      const num = parseInt(p);
      if (num < 1 || num > 65535) {
        throw new ValidationError(`${fieldName}应在1-65535之间`);
      }
    }
  }
  
  return true;
};

// 带宽格式验证
const validateBandwidth = (bandwidth, fieldName = '带宽') => {
  if (!bandwidth) return true;
  
  const bandwidthRegex = /^\d+[MG]?$/i;
  if (!bandwidthRegex.test(bandwidth)) {
    throw new ValidationError(`${fieldName}格式不正确，支持格式：100M 或 1G`);
  }
  
  return true;
};

// 日期验证
const validateDate = (date, fieldName = '日期') => {
  if (!date) return true;
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    throw new ValidationError(`${fieldName}格式不正确`);
  }
  
  return true;
};

// 日期范围验证
const validateDateRange = (startDate, endDate, startFieldName = '开始日期', endFieldName = '结束日期') => {
  if (!startDate || !endDate) return true;
  
  validateDate(startDate, startFieldName);
  validateDate(endDate, endFieldName);
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (start >= end) {
    throw new ValidationError(`${startFieldName}不能晚于${endFieldName}`);
  }
  
  return true;
};

// 费用验证
const validateFee = (fee, fieldName = '费用') => {
  if (fee === undefined || fee === null || fee === '') return true;
  
  const feeNum = parseFloat(fee);
  if (isNaN(feeNum) || feeNum < 0) {
    throw new ValidationError(`${fieldName}必须为非负数`);
  }
  
  return true;
};

// 字符串长度验证
const validateLength = (str, min, max, fieldName = '字段') => {
  if (!str) return true;
  
  if (str.length < min || str.length > max) {
    throw new ValidationError(`${fieldName}长度应在${min}-${max}个字符之间`);
  }
  
  return true;
};

// 字符串格式验证
const validateFormat = (str, pattern, fieldName = '字段', formatDesc = '') => {
  if (!str) return true;
  
  if (!pattern.test(str)) {
    throw new ValidationError(`${fieldName}格式不正确${formatDesc ? '，' + formatDesc : ''}`);
  }
  
  return true;
};

// 必填字段验证
const validateRequired = (value, fieldName = '字段') => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName}不能为空`);
  }
  
  return true;
};

// 枚举值验证
const validateEnum = (value, allowedValues, fieldName = '字段') => {
  if (!value) return true;
  
  if (!allowedValues.includes(value)) {
    throw new ValidationError(`${fieldName}值不正确，允许的值：${allowedValues.join(', ')}`);
  }
  
  return true;
};

// JSON格式验证
const validateJSON = (jsonStr, fieldName = 'JSON数据') => {
  if (!jsonStr) return true;
  
  try {
    JSON.parse(jsonStr);
    return true;
  } catch (error) {
    throw new ValidationError(`${fieldName}格式不正确`);
  }
};

// 白名单IP列表验证
const validateWhitelist = (whitelist, fieldName = '白名单') => {
  if (!whitelist) return true;
  
  let ipList = [];
  
  if (typeof whitelist === 'string') {
    try {
      ipList = JSON.parse(whitelist);
    } catch (error) {
      throw new ValidationError(`${fieldName}格式不正确`);
    }
  } else if (Array.isArray(whitelist)) {
    ipList = whitelist;
  } else {
    throw new ValidationError(`${fieldName}格式不正确`);
  }
  
  if (!Array.isArray(ipList)) {
    throw new ValidationError(`${fieldName}必须为数组格式`);
  }
  
  for (const ip of ipList) {
    if (typeof ip !== 'string') {
      throw new ValidationError(`${fieldName}中的IP地址必须为字符串`);
    }
    
    // 支持CIDR格式
    if (ip.includes('/')) {
      validateCIDR(ip, `${fieldName}中的IP地址`);
    } else {
      validateIP(ip, `${fieldName}中的IP地址`);
    }
  }
  
  return true;
};

// 复合验证器
const createValidator = (rules) => {
  return (data) => {
    const errors = [];
    
    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field];
      
      try {
        for (const rule of fieldRules) {
          rule.validator(value, rule.message || field);
        }
      } catch (error) {
        errors.push(`${field}: ${error.message}`);
      }
    }
    
    if (errors.length > 0) {
      throw new ValidationError(errors.join('; '));
    }
    
    return true;
  };
};

module.exports = {
  validateIP,
  validateCIDR,
  validatePort,
  validateBandwidth,
  validateDate,
  validateDateRange,
  validateFee,
  validateLength,
  validateFormat,
  validateRequired,
  validateEnum,
  validateJSON,
  validateWhitelist,
  createValidator
};