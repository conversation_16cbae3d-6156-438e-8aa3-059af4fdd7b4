<template>
  <div class="page-container">
    <div class="page-header">
      <h1>虚拟化信息更新管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="handleManualSync" :loading="syncLoading">
          <el-icon><Refresh /></el-icon>
          手动同步
        </el-button>
        <el-button @click="handleExport" :loading="exportLoading">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.totalVms }}</div>
              <div class="stats-label">虚拟机总数</div>
            </div>
            <el-icon class="stats-icon"><Monitor /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card connected">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.vmsWithIp }}</div>
              <div class="stats-label">有IP虚拟机</div>
            </div>
            <el-icon class="stats-icon"><Connection /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card warning">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.totalVcenters }}</div>
              <div class="stats-label">vCenter数量</div>
            </div>
            <el-icon class="stats-icon"><Cpu /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card info">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.totalEsxiHosts }}</div>
              <div class="stats-label">ESXi主机数</div>
            </div>
            <el-icon class="stats-icon"><Tools /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 同步状态信息 -->
    <el-card class="sync-status-card">
      <template #header>
        <div class="card-header">
          <span>同步状态</span>
          <el-button text @click="refreshSyncStatus">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      <div class="sync-status-content">
        <el-row :gutter="24">
          <el-col :span="6">
            <div class="sync-info-item">
              <div class="sync-info-label">服务状态</div>
              <div class="sync-info-value">
                <el-tag :type="syncStatus.isRunning ? 'success' : 'danger'" size="small">
                  {{ syncStatus.isRunning ? '运行中' : '已停止' }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="sync-info-item">
              <div class="sync-info-label">最后同步时间</div>
              <div class="sync-info-value">
                {{ formatDateTime(syncStatus.lastSyncTime) || '未同步' }}
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="sync-info-item">
              <div class="sync-info-label">数据源地址</div>
              <div class="sync-info-value">
                <el-tooltip :content="syncStatus.dataSourceUrl" placement="top" :disabled="!syncStatus.dataSourceUrl">
                  <span class="data-source-url">{{ syncStatus.dataSourceUrl || '未配置' }}</span>
                </el-tooltip>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" class="search-form">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="虚拟机名称">
              <el-input
                v-model="searchForm.vm_name"
                placeholder="请输入虚拟机名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="vCenter IP">
              <el-input
                v-model="searchForm.vcenter_ip"
                placeholder="请输入vCenter IP"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="ESXi主机IP">
              <el-input
                v-model="searchForm.esxi_ip"
                placeholder="请输入ESXi主机IP"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="同步状态">
              <el-select
                v-model="searchForm.sync_status"
                placeholder="请选择同步状态"
                clearable
              >
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
                <el-option label="待同步" value="pending" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="24" class="search-buttons">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>虚拟机列表</span>
          <div class="table-actions">
            <el-button 
              type="danger" 
              :disabled="selectedRows.length === 0"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="tableData"
        :loading="tableLoading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="vm_name" label="虚拟机名称" min-width="200" sortable="custom">
          <template #default="{ row }">
            <el-button link @click="handleViewDetail(row)">
              {{ row.vm_name }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column prop="vcenter_ip" label="vCenter IP" width="140" sortable="custom" />
        
        <el-table-column prop="esxi_ip" label="ESXi主机IP" width="140" sortable="custom" />
        
        <el-table-column prop="vm_ip" label="虚拟机IP" width="140" sortable="custom">
          <template #default="{ row }">
            <span v-if="row.vm_ip">{{ row.vm_ip }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="data_source_time" label="数据源时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.data_source_time) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="sync_status" label="同步状态" width="100" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getSyncStatusType(row.sync_status)">
              {{ getSyncStatusText(row.sync_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_sync_time" label="最后同步时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.last_sync_time) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="140" fixed="right" align="center">
          <template #default="{ row }">
            <div style="display: flex; gap: 8px; justify-content: center;">
              <el-button size="small" type="primary" @click="handleViewDetail(row)">
                详情
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="虚拟机详情"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="虚拟机名称">{{ currentDetail.vm_name }}</el-descriptions-item>
          <el-descriptions-item label="vCenter IP">{{ currentDetail.vcenter_ip || '-' }}</el-descriptions-item>
          <el-descriptions-item label="ESXi主机IP">{{ currentDetail.esxi_ip || '-' }}</el-descriptions-item>
          <el-descriptions-item label="虚拟机IP">{{ currentDetail.vm_ip || '-' }}</el-descriptions-item>
          <el-descriptions-item label="数据源时间">
            {{ formatDateTime(currentDetail.data_source_time) || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后同步时间">
            {{ formatDateTime(currentDetail.last_sync_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="同步状态">
            <el-tag :type="getSyncStatusType(currentDetail.sync_status)">
              {{ getSyncStatusText(currentDetail.sync_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentDetail.created_at_formatted }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ currentDetail.updated_at_formatted }}
          </el-descriptions-item>
          <el-descriptions-item label="版本号">{{ currentDetail.version_num }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentDetail.sync_error_message" style="margin-top: 20px;">
          <h4>同步错误信息：</h4>
          <el-alert
            :title="currentDetail.sync_error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, RefreshLeft, Refresh, Download, Delete, 
  Cpu, Connection, Tools, Monitor 
} from '@element-plus/icons-vue'
import axios from '@/utils/request'

// 响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const syncLoading = ref(false)
const exportLoading = ref(false)
const selectedRows = ref([])
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  vm_name: '',
  vcenter_ip: '',
  esxi_ip: '',
  vm_ip: '',
  sync_status: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序
const sortConfig = reactive({
  sortProp: 'last_sync_time',
  sortOrder: 'desc'
})

// 统计数据
const statistics = reactive({
  totalVms: 0,
  vmsWithIp: 0,
  vmsWithoutIp: 0,
  syncSuccess: 0,
  syncFailed: 0,
  totalVcenters: 0,
  totalEsxiHosts: 0,
  lastSyncTime: null,
  lastDataSourceTime: null
})

// 同步状态
const syncStatus = reactive({
  isRunning: false,
  lastSyncTime: null,
  dataSourceUrl: '',
  syncInterval: ''
})

// 获取VMware虚拟机列表
const fetchVmwareHosts = async () => {
  try {
    tableLoading.value = true
    const response = await axios.post('/api/get_vmware_hosts', {
      ...searchForm,
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      sortProp: sortConfig.sortProp,
      sortOrder: sortConfig.sortOrder
    })

    if (response.code === 0) {
      tableData.value = response.msg
      pagination.total = response.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取VMware虚拟机列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await axios.post('/api/get_vmware_statistics')
    if (response.code === 0) {
      Object.assign(statistics, response.msg)
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 获取同步状态
const fetchSyncStatus = async () => {
  try {
    const response = await axios.post('/api/get_vmware_sync_status')
    if (response.code === 0) {
      Object.assign(syncStatus, response.msg)
    }
  } catch (error) {
    console.error('获取同步状态失败:', error)
  }
}

// 刷新同步状态
const refreshSyncStatus = () => {
  fetchSyncStatus()
}

// 手动触发同步
const handleManualSync = async () => {
  try {
    syncLoading.value = true
    const response = await axios.post('/api/trigger_vmware_sync')
    
    if (response.code === 0) {
      ElMessage.success('同步任务已启动')
      // 刷新数据
      setTimeout(() => {
        fetchVmwareHosts()
        fetchStatistics()
        fetchSyncStatus()
      }, 2000)
    } else {
      ElMessage.error(response.msg || '同步失败')
    }
  } catch (error) {
    console.error('手动同步失败:', error)
    ElMessage.error('同步失败')
  } finally {
    syncLoading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const response = await axios.post('/api/export_vmware_hosts')
    
    if (response.code === 0) {
      // 这里可以添加导出Excel的逻辑
      ElMessage.success('导出成功')
    } else {
      ElMessage.error(response.msg || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchVmwareHosts()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1
  fetchVmwareHosts()
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  sortConfig.sortProp = prop || 'last_sync_time'
  sortConfig.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  fetchVmwareHosts()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchVmwareHosts()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchVmwareHosts()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await axios.post('/api/get_vmware_host_detail', { id: row.id })
    if (response.code === 0) {
      currentDetail.value = response.msg
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 删除单个记录
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除虚拟机 "${row.vm_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await axios.post('/api/delete_vmware_host', {
      id: row.id,
      username: 'admin' // 这里应该从用户状态获取
    })

    if (response.code === 0) {
      ElMessage.success('删除成功')
      fetchVmwareHosts()
      fetchStatistics()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await axios.post('/api/batch_delete_vmware_hosts', {
      ids,
      username: 'admin' // 这里应该从用户状态获取
    })

    if (response.code === 0) {
      ElMessage.success('批量删除成功')
      fetchVmwareHosts()
      fetchStatistics()
      selectedRows.value = []
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具方法
const getSyncStatusType = (status) => {
  const statusMap = {
    'success': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const getSyncStatusText = (status) => {
  const statusMap = {
    'success': '成功',
    'failed': '失败',
    'pending': '待同步'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const truncateUrl = (url) => {
  if (!url) return ''
  return url.length > 50 ? url.substring(0, 50) + '...' : url
}

// 页面加载时获取数据
onMounted(() => {
  fetchVmwareHosts()
  fetchStatistics()
  fetchSyncStatus()
})
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #303133;
    margin: 0;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.stats-cards {
  margin-bottom: 20px;
  
  .stats-card {
    position: relative;
    overflow: hidden;
    
    &.connected {
      border-left: 4px solid #67c23a;
    }
    
    &.warning {
      border-left: 4px solid #e6a23c;
    }
    
    &.info {
      border-left: 4px solid #409eff;
    }
    
    .stats-content {
      .stats-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
      }
    }
    
    .stats-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40px;
      color: #e4e7ed;
    }
  }
}

.sync-status-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .sync-status-content {
    .sync-info-item {
      text-align: center;
      padding: 8px 0;

      .sync-info-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .sync-info-value {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        word-break: break-all;
        line-height: 1.4;
      }

      .data-source-url {
        color: #409eff;
        cursor: pointer;
        text-decoration: underline;
        font-size: 13px;
        display: inline-block;
        max-width: 100%;
        word-break: break-all;
        line-height: 1.4;
      }
    }

    // 数据源地址的特殊样式
    .sync-info-item:has(.data-source-url) {
      .sync-info-value {
        text-align: left;
        padding: 0 8px;
      }
    }
  }
}

.search-card {
  margin-bottom: 20px;

  .search-form {
    margin-bottom: 0;

    .el-form-item {
      margin-bottom: 16px;
    }

    .search-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 8px;

      .el-form-item {
        margin-bottom: 0;
      }

      .el-button {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.table-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .no-data {
    color: #c0c4cc;
  }
}

.detail-content {
  .el-descriptions {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .stats-cards {
    .el-col {
      margin-bottom: 10px;
    }
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>