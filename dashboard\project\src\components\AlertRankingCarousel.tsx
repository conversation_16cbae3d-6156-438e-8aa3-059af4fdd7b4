import React, { useState, useEffect } from 'react';
import { TrendingUp, BarChart3 } from 'lucide-react';
import EnhancedCard from './EnhancedCard';

interface AlertRanking {
  systemName: string;
  adminName: string;
  alertCount: number;
  trend: 'up' | 'down';
  dailyData: number[]; // 15日内每日告警数据
}

interface AlertRankingCarouselProps {
  systemRankings: AlertRanking[];
  adminRankings: AlertRanking[];
  autoPlayInterval?: number;
  className?: string;
}

// 堆叠折线图组件
interface StackedLineChartProps {
  data: AlertRanking[];
  viewType: 'system' | 'admin';
  width?: number;
  height?: number;
}

const StackedLineChart: React.FC<StackedLineChartProps> = ({ 
  data, 
  viewType,
  width = 400, 
  height = 200 
}) => {
  const top5Data = data.slice(0, 5);
  const colors = ['#00d4ff', '#f59e0b', '#6b7280', '#10b981', '#3b82f6'];
  
  // 计算堆叠数据 - 每个系统的数据累加到前面系统的数据上
  const stackedData = top5Data.map((item, index) => {
    if (index === 0) {
      return item.dailyData;
    } else {
      return item.dailyData.map((value, dayIndex) => {
        // 累加前面所有系统在这一天的数据
        let stackedValue = value;
        for (let i = 0; i < index; i++) {
          stackedValue += top5Data[i].dailyData[dayIndex] || 0;
        }
        return stackedValue;
      });
    }
  });

  // 计算总的最大值用于Y轴缩放
  const allStackedValues = stackedData.flat();
  const max = Math.max(...allStackedValues);
  const min = 0; // 堆叠图从0开始
  const range = max - min || 1;

  // 生成Y轴刻度
  const yTicks = [];
  const tickCount = 5;
  for (let i = 0; i <= tickCount; i++) {
    const value = (max / tickCount) * i;
    const y = height - (value / range) * height;
    yTicks.push({ value: Math.round(value), y });
  }

  return (
    <div className="glass-card rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-300">Top5 15日告警堆叠趋势</h4>
        <div className="text-xs text-gray-400">
          {top5Data[0]?.dailyData.length || 0} 天数据
        </div>
      </div>
      
      <div className="relative">
        <svg width={width} height={height} className="overflow-visible">
          <defs>
            {colors.map((color, index) => (
              <linearGradient key={`gradient-${index}`} id={`stackedGradient-${index}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={color} stopOpacity="0.6" />
                <stop offset="100%" stopColor={color} stopOpacity="0.2" />
              </linearGradient>
            ))}
            <filter id="stackedGlow">
              <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {/* Y轴网格线 */}
          {yTicks.map((tick, index) => (
            <line
              key={`grid-${index}`}
              x1="0"
              y1={tick.y}
              x2={width}
              y2={tick.y}
              stroke="rgba(255, 255, 255, 0.1)"
              strokeWidth="0.5"
              strokeDasharray="2,2"
            />
          ))}
          
          {/* 绘制堆叠区域 - 从上到下绘制 */}
          {stackedData.map((currentStackData, index) => {
            // 当前层的上边界
            const topPoints = currentStackData.map((value, dayIndex) => {
              const x = (dayIndex / (currentStackData.length - 1)) * width;
              const y = height - (value / range) * height;
              return `${x},${y}`;
            }).join(' ');
            
            // 当前层的下边界（前一层的上边界，如果是第一层则为底部）
            let bottomPoints;
            if (index === 0) {
              // 第一层，下边界是底部
              bottomPoints = currentStackData.map((_, dayIndex) => {
                const x = (dayIndex / (currentStackData.length - 1)) * width;
                return `${x},${height}`;
              }).reverse().join(' ');
            } else {
              // 其他层，下边界是前一层的上边界
              bottomPoints = stackedData[index - 1].map((value, dayIndex) => {
                const x = (dayIndex / (stackedData[index - 1].length - 1)) * width;
                const y = height - (value / range) * height;
                return `${x},${y}`;
              }).reverse().join(' ');
            }
            
            const areaPath = `M ${topPoints} L ${bottomPoints} Z`;
            const linePath = `M ${topPoints}`;
            
            return (
              <g key={index}>
                {/* 填充区域 */}
                <path
                  d={areaPath}
                  fill={`url(#stackedGradient-${index})`}
                  className="transition-all duration-500"
                  style={{
                    opacity: 0,
                    animation: `fadeIn 0.8s ease-out ${index * 0.15}s forwards`
                  }}
                />
                
                {/* 边界线 */}
                <path
                  d={linePath}
                  fill="none"
                  stroke={colors[index]}
                  strokeWidth="2"
                  filter="url(#stackedGlow)"
                  className="transition-all duration-500"
                  style={{
                    strokeDasharray: currentStackData.length * 8,
                    strokeDashoffset: currentStackData.length * 8,
                    animation: `drawLine 1.5s ease-out ${index * 0.15}s forwards`
                  }}
                />
                
                {/* 数据点 */}
                {currentStackData.map((value, dayIndex) => {
                  const x = (dayIndex / (currentStackData.length - 1)) * width;
                  const y = height - (value / range) * height;
                  const isLastPoint = dayIndex === currentStackData.length - 1;
                  
                  return (
                    <g key={dayIndex}>
                      <circle
                        cx={x}
                        cy={y}
                        r={isLastPoint ? "3.5" : "2.5"}
                        fill={colors[index]}
                        stroke="white"
                        strokeWidth={isLastPoint ? "2" : "1"}
                        className="opacity-0"
                        style={{
                          animation: `fadeIn 0.3s ease-out ${index * 0.15 + dayIndex * 0.03}s forwards`
                        }}
                      />
                      
                      {/* 最后一个数据点的标签 */}
                      {isLastPoint && (
                        <text
                          x={x + 8}
                          y={y - 8}
                          fontSize="10"
                          fill={colors[index]}
                          fontWeight="bold"
                          className="opacity-0"
                          style={{
                            animation: `fadeIn 0.5s ease-out ${index * 0.15 + 0.5}s forwards`
                          }}
                        >
                          {top5Data[index].dailyData[top5Data[index].dailyData.length - 1]}
                        </text>
                      )}
                    </g>
                  );
                })}
              </g>
            );
          })}
        </svg>
        
        {/* Y轴标签 */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-400 -ml-12">
          {yTicks.reverse().map((tick, index) => (
            <span key={index} style={{ transform: 'translateY(-50%)' }}>
              {tick.value}
            </span>
          ))}
        </div>
        
        {/* X轴标签 */}
        <div className="flex justify-between text-xs text-gray-400 mt-2">
          <span>15天前</span>
          <span>10天前</span>
          <span>5天前</span>
          <span>今天</span>
        </div>
      </div>
      
      {/* 图例 - 从上到下排列，对应堆叠顺序 */}
      <div className="flex flex-wrap gap-3 mt-4">
        {top5Data.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full border border-white"
              style={{ backgroundColor: colors[index] }}
            />
            <span className="text-xs text-gray-300">
              {viewType === 'system' ? item.systemName : item.adminName}
            </span>
            <span className="text-xs text-gray-400">
              (平均: {(item.dailyData.reduce((sum, val) => sum + val, 0) / item.dailyData.length).toFixed(1)})
            </span>
          </div>
        ))}
      </div>
      
      <style>{`
        @keyframes drawLine {
          to {
            stroke-dashoffset: 0;
          }
        }
        
        @keyframes fadeIn {
          to {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

const AlertRankingCarousel: React.FC<AlertRankingCarouselProps> = ({
  systemRankings,
  adminRankings,
  autoPlayInterval = 8000,
  className = ""
}) => {
  const [currentView, setCurrentView] = useState<'system' | 'admin'>('system');
  const [isTransitioning, setIsTransitioning] = useState(false);

  const rankingTypes = [
    { key: 'system', title: '15日内告警排名（按系统）', data: systemRankings },
    { key: 'admin', title: '15日内告警排名（按管理员）', data: adminRankings }
  ];

  const currentRankingType = rankingTypes.find(type => type.key === currentView);

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      handleSwitch();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentView, autoPlayInterval]);

  const handleSwitch = () => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentView(prev => prev === 'system' ? 'admin' : 'system');
      setIsTransitioning(false);
    }, 300);
  };

  const handleManualSwitch = (viewType: 'system' | 'admin') => {
    if (isTransitioning || viewType === currentView) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentView(viewType);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <EnhancedCard
      title={currentRankingType?.title || '15日内告警排名'}
      icon={<BarChart3 className="w-5 h-5" />}
      className={`h-full flex flex-col overflow-hidden ${className}`}
      animationDelay={600}
    >
      {/* 切换指示器和控制按钮 */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-2">
          <button
            onClick={() => handleManualSwitch('system')}
            className={`px-3 py-1 rounded-full text-xs transition-all duration-300 ${
              currentView === 'system'
                ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/50'
                : 'bg-gray-700/50 text-gray-400 hover:bg-gray-600/50'
            }`}
          >
            按系统
          </button>
          <button
            onClick={() => handleManualSwitch('admin')}
            className={`px-3 py-1 rounded-full text-xs transition-all duration-300 ${
              currentView === 'admin'
                ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/50'
                : 'bg-gray-700/50 text-gray-400 hover:bg-gray-600/50'
            }`}
          >
            按管理员
          </button>
        </div>

        <div className="flex space-x-2">
          {rankingTypes.map((type) => (
            <div
              key={type.key}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                type.key === currentView
                  ? 'bg-neon-blue shadow-lg shadow-neon-blue/50'
                  : 'bg-gray-600'
              }`}
            />
          ))}
        </div>
      </div>

      {/* 内容容器 */}
      <div className="relative overflow-hidden flex-1">
        <div 
          className={`transition-all duration-500 ease-in-out h-full ${
            isTransitioning ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
          }`}
        >
          <div className="h-full flex flex-col">
            {/* 堆叠折线图 */}
            <StackedLineChart 
              data={currentRankingType?.data || []}
              viewType={currentView}
              width={380}
              height={220}
            />
          </div>
        </div>
      </div>
    </EnhancedCard>
  );
};



export default AlertRankingCarousel;