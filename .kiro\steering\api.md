---
description: 
globs: 
alwaysApply: false
---
# CMDB v2.0 API开发规范

## API架构
- **框架**: Express.js
- **认证**: JWT Token
- **数据格式**: JSON
- **API版本**: RESTful API
- **主入口**: [index.js](mdc:backend/index.js)
- **路由目录**: [api/](mdc:backend/api)

## RESTful API 设计规范

### HTTP方法使用
- `GET` - 查询数据（幂等）
- `POST` - 创建数据
- `PUT` - 完整更新数据（幂等）
- `PATCH` - 部分更新数据
- `DELETE` - 删除数据（幂等）

### URL设计规范
- 使用名词复数形式：`/api/servers`、`/api/users`
- 层级关系：`/api/departments/{id}/users`
- 查询参数：`/api/servers?page=1&size=10&status=active`
- 资源ID：`/api/servers/{id}`

### 状态码规范
```javascript
// 成功响应
200 OK - 查询成功
201 Created - 创建成功
204 No Content - 删除成功

// 客户端错误
400 Bad Request - 请求参数错误
401 Unauthorized - 未认证
403 Forbidden - 无权限
404 Not Found - 资源不存在
409 Conflict - 资源冲突

// 服务端错误
500 Internal Server Error - 服务器内部错误
502 Bad Gateway - 网关错误
503 Service Unavailable - 服务不可用
```

## 统一响应格式

### 成功响应格式
```javascript
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}, // 或 []
  "timestamp": "2024-01-15 10:30:00"
}
```

### 错误响应格式
```javascript
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "details": "具体错误信息",
    "field": "错误字段"
  },
  "timestamp": "2024-01-15 10:30:00"
}
```

### 分页响应格式
```javascript
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [], // 数据列表
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 100,
      "totalPages": 10
    }
  },
  "timestamp": "2024-01-15 10:30:00"
}
```

## API模块组织

### 资产管理API
```javascript
// 路由: /api/cmdb/servers
GET    /api/cmdb/servers          // 查询服务器列表
POST   /api/cmdb/servers          // 创建服务器
GET    /api/cmdb/servers/{id}     // 查询单个服务器
PUT    /api/cmdb/servers/{id}     // 更新服务器
DELETE /api/cmdb/servers/{id}     // 删除服务器
```

### 用户管理API
```javascript
// 路由: /api/system/users
GET    /api/system/users          // 查询用户列表
POST   /api/system/users          // 创建用户
GET    /api/system/users/{id}     // 查询单个用户
PUT    /api/system/users/{id}     // 更新用户
DELETE /api/system/users/{id}     // 删除用户
```

### 认证API
```javascript
POST   /api/auth/login           // 用户登录
POST   /api/auth/logout          // 用户登出
POST   /api/auth/refresh         // 刷新Token
GET    /api/auth/profile         // 获取用户信息
```

## 请求参数规范

### 查询参数
```javascript
// 分页参数
{
  "page": 1,        // 页码，从1开始
  "size": 10,       // 每页大小
  "sort": "id",     // 排序字段
  "order": "asc"    // 排序方向: asc/desc
}

// 搜索参数
{
  "keyword": "搜索关键词",
  "status": "active",
  "dateFrom": "2024-01-01",
  "dateTo": "2024-12-31"
}
```

### 请求体参数
```javascript
// 创建服务器
{
  "hostname": "web-server-01",
  "ipAddress": "*************",
  "serverType": "Web服务器",
  "cpuCores": 4,
  "memoryGb": 16,
  "diskGb": 500,
  "osType": "Linux",
  "osVersion": "Ubuntu 20.04",
  "departmentId": 1,
  "ownerId": 2,
  "location": "机房A-01",
  "purchaseDate": "2024-01-15"
}
```

## 参数验证

### 输入验证中间件
```javascript
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        code: 400,
        message: "参数验证失败",
        error: {
          details: error.details[0].message,
          field: error.details[0].path[0]
        },
        timestamp: new Date().toLocaleString('zh-CN')
      });
    }
    next();
  };
};
```

### 通用验证规则
```javascript
// 必填字段验证
required: true

// 字符串长度验证
minLength: 1,
maxLength: 100

// 数字范围验证
min: 0,
max: 9999

// 日期格式验证
pattern: /^\d{4}-\d{2}-\d{2}$/

// 邮箱格式验证
pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
```

## 认证与授权

### JWT Token 格式
```javascript
// Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// Payload
{
  "userId": 1,
  "username": "admin",
  "role": "admin",
  "iat": 1642234567,
  "exp": 1642320967
}
```

### 认证中间件
```javascript
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      success: false,
      code: 401,
      message: "未提供认证令牌",
      timestamp: new Date().toLocaleString('zh-CN')
    });
  }
  
  // 验证token逻辑
  next();
};
```

## 错误处理

### 统一错误处理中间件
```javascript
const errorHandler = (error, req, res, next) => {
  console.error('API Error:', error);
  
  res.status(error.status || 500).json({
    success: false,
    code: error.status || 500,
    message: error.message || "服务器内部错误",
    error: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toLocaleString('zh-CN')
  });
};
```

### 常见错误类型
```javascript
// 参数验证错误
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.status = 400;
    this.field = field;
  }
}

// 资源不存在错误
class NotFoundError extends Error {
  constructor(resource) {
    super(`${resource} 不存在`);
    this.status = 404;
  }
}

// 权限不足错误
class ForbiddenError extends Error {
  constructor(message = "权限不足") {
    super(message);
    this.status = 403;
  }
}
```

## 日志记录

### API访问日志
```javascript
const apiLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.url} ${res.statusCode} ${duration}ms`);
  });
  
  next();
};
```

### 操作日志
```javascript
// 记录重要操作
const logOperation = (operation, userId, details) => {
  console.log(`[${new Date().toLocaleString('zh-CN')}] ${operation} by user ${userId}: ${details}`);
};
```

## 性能优化

### 缓存策略
- 静态数据缓存（字典数据）
- 查询结果缓存（复杂查询）
- 用户session缓存

### 分页优化
- 限制最大页面大小
- 使用索引优化查询
- 避免深度分页

### 数据库连接优化
- 连接池管理
- 长连接复用
- 查询超时设置

