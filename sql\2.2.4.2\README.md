# 版本 2.2.4.2 - 虚拟机登记表生命周期字段功能

## 概述
本版本为 `cmdb_vm_registry` 虚拟机登记表添加了生命周期字段 (`operation_status`)，参考了 `cmdb_server_management` 服务器管理表的实现。

## 功能特性

### 1. 数据库层面
- **新增字段**: `operation_status` (VARCHAR(50) NOT NULL)
- **字段注释**: 生命周期（系统管理员填写）
- **默认值**: 为现有记录设置默认值为 'D001'（运行中）
- **数据字典**: 添加生命周期状态选项

### 2. 数据字典项
新增以下生命周期状态选项：
- `D001` - 运行中
- `D002` - 维护中  
- `D003` - 已下线
- `D004` - 待部署
- `D005` - 故障中

### 3. 视图更新
- **更新视图**: `v_cmdb_vm_registry` 包含生命周期字段
- **字典关联**: 通过数据字典显示中文名称
- **权限授予**: 为 `cjmonitor` 用户授予视图查询权限

### 4. 前端功能
- **表单字段**: 在新增和编辑对话框中添加生命周期选择器
- **搜索功能**: 支持按生命周期状态筛选
- **表格显示**: 在数据表格中显示生命周期列，带有状态标签
- **数据验证**: 添加生命周期字段的必填验证

### 5. 后端API
- **新增接口**: 支持 `operation_status` 字段的增删改查
- **搜索参数**: 支持按生命周期状态搜索
- **数据验证**: 后端验证生命周期字段的有效性

## 技术实现

### 数据库迁移
```sql
-- 添加生命周期字段
ALTER TABLE public.cmdb_vm_registry 
ADD COLUMN operation_status character varying(50);

-- 设置默认值并添加非空约束
UPDATE public.cmdb_vm_registry 
SET operation_status = 'D001' 
WHERE operation_status IS NULL;

ALTER TABLE public.cmdb_vm_registry 
ALTER COLUMN operation_status SET NOT NULL;
```

### 前端实现
- **Vue组件**: 更新 `cmdb_vm_registry.vue`
- **表单控件**: Element Plus 选择器组件
- **状态标签**: 根据生命周期状态显示不同颜色的标签
- **数据字典**: 从后端动态加载生命周期选项

### 后端实现
- **API端点**: 更新 `/api/add_cmdb_vm_registry`、`/api/update_cmdb_vm_registry`、`/api/get_cmdb_vm_registry`
- **字段处理**: 在所有相关API中添加 `operation_status` 字段支持
- **查询优化**: 更新SQL查询以包含生命周期字段

## 部署说明

### 1. 数据库迁移
执行以下SQL脚本：
```bash
psql -d cmdb -f sql/2.2.4.2/add_vm_registry_lifecycle_field.sql
```

### 2. 应用重启
重启前后端应用以加载新功能：
```bash
# 后端
cd backend && npm restart

# 前端
cd frontend && npm run build
```

### 3. 验证部署
1. 检查数据库表结构是否正确添加了 `operation_status` 字段
2. 验证数据字典是否包含生命周期选项
3. 测试前端页面的生命周期功能是否正常工作

## 兼容性说明

### 向后兼容
- 现有数据自动设置为"运行中"状态
- 不影响现有API的正常使用
- 保持与其他模块的兼容性

### 数据一致性
- 所有现有虚拟机记录都会被设置默认的生命周期状态
- 新增记录必须指定生命周期状态
- 与服务器管理和设备管理的生命周期字段保持一致

## 使用指南

### 管理员操作
1. **新增虚拟机**: 必须选择生命周期状态
2. **编辑虚拟机**: 可以修改生命周期状态
3. **搜索筛选**: 可以按生命周期状态筛选虚拟机
4. **状态管理**: 建议定期更新虚拟机的生命周期状态

### 最佳实践
- 及时更新虚拟机的生命周期状态
- 在虚拟机下线前先修改状态为"已下线"
- 定期审查和清理无效的虚拟机记录
- 建立标准化的生命周期管理流程

## 版本信息
- **版本号**: 2.2.4.2
- **发布日期**: 2025-08-01
- **兼容版本**: 2.2.4.1 及以上
- **数据库版本**: PostgreSQL 12.0+

## 相关文档
- [服务器管理生命周期功能](../cmdb_server_management/)
- [设备管理生命周期功能](../cmdb_device_management/)
- [数据字典管理指南](../../docs/data_dictionary_guide.md)