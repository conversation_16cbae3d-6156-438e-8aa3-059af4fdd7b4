// CMDB API 服务
interface DeviceDistribution {
  id: string;
  name: string;
  location: string;
  networkDevices: number;
  physicalServers: number;
  virtualServers: number;
  totalDevices: number;
  status: 'normal' | 'warning' | 'critical';
  coordinates?: {
    x: number;
    y: number;
  };
}

interface CMDBApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

class CMDBApi {
  private baseUrl: string;

  constructor() {
    // 从环境变量获取后端API地址，默认为本地开发地址
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  }

  /**
   * 获取设备机房分布数据
   */
  async getDeviceDistribution(): Promise<DeviceDistribution[]> {
    try {
      // 并行获取三种设备类型的数据
      const [networkDevices, physicalServers, virtualServers] = await Promise.all([
        this.getNetworkDevicesByLocation(),
        this.getPhysicalServersByLocation(),
        this.getVirtualServersByLocation()
      ]);

      // 合并数据按机房分组
      const locationMap = new Map<string, DeviceDistribution>();

      // 处理网络设备
      networkDevices.forEach(item => {
        const location = item.location || '未知机房';
        if (!locationMap.has(location)) {
          locationMap.set(location, {
            id: this.generateLocationId(location),
            name: location,
            location: location,
            networkDevices: 0,
            physicalServers: 0,
            virtualServers: 0,
            totalDevices: 0,
            status: 'normal',
            coordinates: this.getLocationCoordinates(location)
          });
        }
        const dist = locationMap.get(location)!;
        dist.networkDevices += item.count;
        dist.totalDevices += item.count;
      });

      // 处理实体服务器
      physicalServers.forEach(item => {
        const location = item.location || '未知机房';
        if (!locationMap.has(location)) {
          locationMap.set(location, {
            id: this.generateLocationId(location),
            name: location,
            location: location,
            networkDevices: 0,
            physicalServers: 0,
            virtualServers: 0,
            totalDevices: 0,
            status: 'normal',
            coordinates: this.getLocationCoordinates(location)
          });
        }
        const dist = locationMap.get(location)!;
        dist.physicalServers += item.count;
        dist.totalDevices += item.count;
      });

      // 处理虚拟化设备
      virtualServers.forEach(item => {
        const location = item.location || '未知机房';
        if (!locationMap.has(location)) {
          locationMap.set(location, {
            id: this.generateLocationId(location),
            name: location,
            location: location,
            networkDevices: 0,
            physicalServers: 0,
            virtualServers: 0,
            totalDevices: 0,
            status: 'normal',
            coordinates: this.getLocationCoordinates(location)
          });
        }
        const dist = locationMap.get(location)!;
        dist.virtualServers += item.count;
        dist.totalDevices += item.count;
      });

      // 转换为数组并设置状态
      const result = Array.from(locationMap.values()).map(dist => ({
        ...dist,
        status: this.calculateLocationStatus(dist.totalDevices)
      }));

      return result;
    } catch (error) {
      console.error('获取设备分布数据失败:', error);
      // 返回默认数据以防API失败
      return this.getDefaultDistributionData();
    }
  }

  /**
   * 获取网络设备按机房分布
   */
  private async getNetworkDevicesByLocation(): Promise<Array<{location: string, count: number}>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/cmdb/network-devices/distribution`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CMDBApiResponse<Array<{location: string, count: number}>> = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || '获取网络设备分布失败');
      }
    } catch (error) {
      console.error('获取网络设备分布失败:', error);
      return [];
    }
  }

  /**
   * 获取实体服务器按机房分布
   */
  private async getPhysicalServersByLocation(): Promise<Array<{location: string, count: number}>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/cmdb/physical-servers/distribution`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CMDBApiResponse<Array<{location: string, count: number}>> = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || '获取实体服务器分布失败');
      }
    } catch (error) {
      console.error('获取实体服务器分布失败:', error);
      return [];
    }
  }

  /**
   * 获取虚拟化设备按机房分布
   */
  private async getVirtualServersByLocation(): Promise<Array<{location: string, count: number}>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/cmdb/virtual-servers/distribution`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CMDBApiResponse<Array<{location: string, count: number}>> = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || '获取虚拟化设备分布失败');
      }
    } catch (error) {
      console.error('获取虚拟化设备分布失败:', error);
      return [];
    }
  }

  /**
   * 生成机房位置ID
   */
  private generateLocationId(location: string): string {
    return location.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').toLowerCase();
  }

  /**
   * 获取机房坐标位置（用于地图显示）
   */
  private getLocationCoordinates(location: string): {x: number, y: number} {
    // 根据机房名称映射到地图坐标
    const locationCoords: Record<string, {x: number, y: number}> = {
      '上海机房': { x: 75, y: 35 },
      '北京机房': { x: 65, y: 20 },
      '深圳机房': { x: 70, y: 60 },
      '广州机房': { x: 68, y: 65 },
      '杭州机房': { x: 72, y: 40 },
      '南京机房': { x: 73, y: 32 },
      '成都机房': { x: 58, y: 45 },
      '西安机房': { x: 60, y: 35 },
      '武汉机房': { x: 67, y: 42 },
      '重庆机房': { x: 56, y: 48 },
      '天津机房': { x: 66, y: 22 },
      '青岛机房': { x: 69, y: 28 },
      '大连机房': { x: 71, y: 18 },
      '厦门机房': { x: 74, y: 55 },
      '长沙机房': { x: 65, y: 50 },
      '郑州机房': { x: 64, y: 38 },
      '济南机房': { x: 68, y: 30 },
      '合肥机房': { x: 71, y: 38 },
      '南昌机房': { x: 72, y: 48 },
      '福州机房': { x: 75, y: 52 },
      '未知机房': { x: 50, y: 50 }
    };

    return locationCoords[location] || { x: 50 + Math.random() * 40, y: 30 + Math.random() * 40 };
  }

  /**
   * 根据设备数量计算机房状态
   */
  private calculateLocationStatus(totalDevices: number): 'normal' | 'warning' | 'critical' {
    if (totalDevices === 0) return 'critical';
    if (totalDevices < 10) return 'warning';
    return 'normal';
  }

  /**
   * 获取默认分布数据（API失败时使用）
   */
  private getDefaultDistributionData(): DeviceDistribution[] {
    return [
      {
        id: 'shanghai',
        name: '上海机房',
        location: '上海机房',
        networkDevices: 45,
        physicalServers: 32,
        virtualServers: 28,
        totalDevices: 105,
        status: 'normal',
        coordinates: { x: 75, y: 35 }
      },
      {
        id: 'beijing',
        name: '北京机房',
        location: '北京机房',
        networkDevices: 38,
        physicalServers: 28,
        virtualServers: 35,
        totalDevices: 101,
        status: 'normal',
        coordinates: { x: 65, y: 20 }
      },
      {
        id: 'shenzhen',
        name: '深圳机房',
        location: '深圳机房',
        networkDevices: 25,
        physicalServers: 18,
        virtualServers: 22,
        totalDevices: 65,
        status: 'warning',
        coordinates: { x: 70, y: 60 }
      }
    ];
  }
}

export const cmdbApi = new CMDBApi();
export type { DeviceDistribution };