# Technology Stack

## Core Technologies

- **Frontend Framework**: React 18.3.1 with TypeScript 5.5.3
- **Build Tool**: Vite 5.4.2 with ES modules
- **Styling**: Tailwind CSS 3.4.1 with custom tech theme
- **Icons**: Lucide React 0.344.0
- **Language**: TypeScript with strict mode enabled

## Build System

### Development Commands
```bash
npm run dev          # Start development server
npm run build        # Production build
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Key Configuration Files
- `vite.config.ts` - Vite configuration with React plugin
- `tailwind.config.js` - Custom tech theme with glassmorphism and neon colors
- `tsconfig.json` - TypeScript project references
- `eslint.config.js` - ESLint with React hooks and TypeScript rules

## Styling Architecture

### Custom Theme System
- **Tech Colors**: Dark backgrounds (`tech-dark`, `tech-darker`, `tech-surface`)
- **Neon Palette**: Bright accent colors (`neon-blue`, `neon-cyan`, `neon-purple`, etc.)
- **Glass Effects**: Glassmorphism with backdrop blur and transparency
- **Animations**: Custom keyframes for fade-in, glow effects, and floating elements

### Typography
- **Primary Font**: Inter, SF Pro Display (tech font family)
- **Monospace**: JetBrains Mono, Fira Code for data display

## Development Standards

- ES2020 target with modern JavaScript features
- Strict TypeScript with unused variable detection
- React hooks linting and refresh plugin
- Module bundler resolution with import extensions
- JSX automatic runtime (no React import needed)