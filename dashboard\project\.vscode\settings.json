{"css.validate": false, "less.validate": false, "scss.validate": false, "postcss.validate": false, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"plaintext": "html", "css": "css"}, "tailwindCSS.experimental.classRegex": ["class[Name]*\\s*[:=]\\s*[\"']([^\"']*)[\"']", "className\\s*[:=]\\s*[\"']([^\"']*)[\"']"], "tailwindCSS.validate": true, "tailwindCSS.emmetCompletions": true}