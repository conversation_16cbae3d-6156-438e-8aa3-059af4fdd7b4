#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, '..', 'src');
const originalApp = path.join(srcDir, 'App.tsx');
const enhancedApp = path.join(srcDir, 'App-Enhanced.tsx');
const backupApp = path.join(srcDir, 'App-Original.tsx');

console.log('🎨 启用增强版 CMDB Dashboard UI...\n');

try {
  // 检查文件是否存在
  if (!fs.existsSync(enhancedApp)) {
    console.error('❌ 错误: App-Enhanced.tsx 文件不存在');
    process.exit(1);
  }

  // 备份原始文件
  if (fs.existsSync(originalApp)) {
    if (!fs.existsSync(backupApp)) {
      fs.copyFileSync(originalApp, backupApp);
      console.log('✅ 已备份原始 App.tsx 为 App-Original.tsx');
    } else {
      console.log('ℹ️  原始文件备份已存在');
    }
  }

  // 替换为增强版
  fs.copyFileSync(enhancedApp, originalApp);
  console.log('✅ 已启用增强版界面');

  console.log('\n🚀 增强版 UI 已启用！主要特性：');
  console.log('   • 3D 指标卡片');
  console.log('   • 实时数据流');
  console.log('   • 全息地图');
  console.log('   • 数据可视化图表');
  console.log('   • 科技感动画效果');
  console.log('   • 粒子系统');
  console.log('   • 全息投影效果');

  console.log('\n📖 详细说明请查看: UI-OPTIMIZATION-GUIDE.md');
  console.log('\n🔄 如需恢复原版界面，请运行: npm run restore-original-ui');

} catch (error) {
  console.error('❌ 启用失败:', error.message);
  process.exit(1);
}