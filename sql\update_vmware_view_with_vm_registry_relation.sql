-- 更新VMware视图，添加与cmdb_vm_registry的关联
-- 版本: *******
-- 创建时间: 2025-08-14
-- 描述: 通过管理IP与虚拟机IP的关联，获取cmdb_vm_registry中的ESXi主机IP

-- 重新创建视图，添加关联查询
CREATE OR REPLACE VIEW v_cmdb_vmware_info_auto_update AS
SELECT 
    v.id,
    v.vm_name,
    v.vcenter_ip,
    -- 优先使用VMware表中的esxi_ip，如果为空则通过关联查询获取
    COALESCE(v.esxi_ip, vm_reg.host_ip::inet) as esxi_ip,
    v.vm_ip,
    v.data_source_time,
    v.data_source_url,
    v.last_sync_time,
    v.sync_status,
    v.sync_error_message,
    TO_CHAR(v.created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at_formatted,
    v.created_by,
    TO_CHAR(v.updated_at, 'YYYY-MM-DD HH24:MI:SS') as updated_at_formatted,
    v.updated_by,
    v.version_num,
    -- 添加关联信息字段
    vm_reg.management_ip as vm_registry_management_ip,
    vm_reg.host_ip as vm_registry_host_ip
FROM cmdb_vmware_info_auto_update v
LEFT JOIN cmdb_vm_registry vm_reg ON vm_reg.management_ip = v.vm_ip::text 
    AND vm_reg.del_flag = '0'
WHERE v.is_deleted = FALSE
ORDER BY v.last_sync_time DESC, v.id DESC;

-- 添加视图注释
COMMENT ON VIEW v_cmdb_vmware_info_auto_update IS '虚拟化信息自动更新视图 - 包含与cmdb_vm_registry的关联查询';

-- 验证视图创建结果
SELECT 
    vm_name,
    vcenter_ip,
    esxi_ip,
    vm_ip,
    vm_registry_management_ip,
    vm_registry_host_ip
FROM v_cmdb_vmware_info_auto_update 
LIMIT 5;
