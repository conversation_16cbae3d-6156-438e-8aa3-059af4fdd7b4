<template>
    <div class="discovery-cleanup">
        <!-- 页面标题 -->
        <div class="page-header">
            <p class="description"
                style="line-height: 1.5; margin-bottom: 10px; color: #E6A23C; background-color: #FDF6EC; padding: 8px 12px; border-radius: 4px; border-left: 4px solid #E6A23C;">
                管理自动发现结果的数据清理，自动清理超过指定天数的过期数据
            </p>
        </div>

        <!-- 统计信息卡片 -->
        <el-row :gutter="20" class="stats-row">
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
                <el-card class="stats-card">
                    <div class="stats-content">
                        <div class="stats-icon expired">
                            <el-icon>
                                <Warning />
                            </el-icon>
                        </div>
                        <div class="stats-info">
                            <div class="stats-number">{{ statsData.expiredCount || 0 }}</div>
                            <div class="stats-label">过期数据</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
                <el-card class="stats-card">
                    <div class="stats-content">
                        <div class="stats-icon active">
                            <el-icon>
                                <Check />
                            </el-icon>
                        </div>
                        <div class="stats-info">
                            <div class="stats-number">{{ statsData.activeCount || 0 }}</div>
                            <div class="stats-label">有效数据</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
                <el-card class="stats-card">
                    <div class="stats-content">
                        <div class="stats-icon deleted">
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </div>
                        <div class="stats-info">
                            <div class="stats-number">{{ statsData.deletedCount || 0 }}</div>
                            <div class="stats-label">已清理数据</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
                <el-card class="stats-card">
                    <div class="stats-content">
                        <div class="stats-icon total">
                            <el-icon>
                                <DataBoard />
                            </el-icon>
                        </div>
                        <div class="stats-info">
                            <div class="stats-number">{{ statsData.totalCount || 0 }}</div>
                            <div class="stats-label">总数据量</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 操作面板 -->
        <el-card class="operation-card">
            <template #header>
                <div class="card-header">
                    <span>清理操作</span>
                    <el-button link @click="refreshStats">
                        <el-icon>
                            <Refresh />
                        </el-icon> 刷新统计
                    </el-button>
                </div>
            </template>

            <el-form :inline="true" class="operation-form">
                <el-form-item label="过期天数">
                    <el-input-number v-model="cleanupForm.expireDays" :min="1" :max="365" placeholder="过期天数"
                        style="width: 120px" />
                    <span class="form-help">天前的数据将被清理</span>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="previewCleanup" :loading="previewLoading">
                        <el-icon>
                            <View />
                        </el-icon> 预览清理数据
                    </el-button>
                    <el-button type="warning" @click="executeCleanup" :loading="cleanupLoading">
                        <el-icon>
                            <Delete />
                        </el-icon> 执行清理
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 预览数据表格 -->
        <el-card v-if="showPreview" class="preview-card">
            <template #header>
                <div class="card-header">
                    <span>预览清理数据 ({{ previewData.total }} 条)</span>
                    <el-button link @click="showPreview = false">
                        <el-icon>
                            <Close />
                        </el-icon> 关闭预览
                    </el-button>
                </div>
            </template>

            <el-table :data="previewData.rows" border stripe v-loading="previewLoading" style="width: 100%"
                :max-height="400">
                <el-table-column prop="ip_address" label="IP地址" width="200" />
                <el-table-column prop="hostname" label="主机名" width="200" />
                <el-table-column prop="discovery_time" label="发现时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.discovery_time) }}
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="过期天数" width="100">
                    <template #default="scope">
                        <el-tag type="warning">
                            {{ getDaysAgo(scope.row.discovery_time) }} 天前
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination v-model:current-page="previewPagination.currentPage"
                    v-model:page-size="previewPagination.pageSize" :page-sizes="[10, 20, 50, 100]"
                    :total="previewData.total" layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handlePreviewSizeChange" @current-change="handlePreviewCurrentChange" />
            </div>
        </el-card>

        <!-- 清理历史 -->
        <el-card class="history-card">
            <template #header>
                <div class="card-header">
                    <span>清理历史</span>
                    <el-button link @click="loadHistory">
                        <el-icon>
                            <Refresh />
                        </el-icon> 刷新历史
                    </el-button>
                </div>
            </template>

            <el-table :data="historyData.rows" border stripe v-loading="historyLoading" style="width: 100%"
                :max-height="300">
                <el-table-column prop="log_level" label="级别" width="80">
                    <template #default="scope">
                        <el-tag :type="getLogLevelType(scope.row.log_level)">
                            {{ scope.row.log_level }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="message" label="操作" width="150" />
                <el-table-column prop="details" label="详情" min-width="200" show-overflow-tooltip />
                <el-table-column prop="created_by" label="操作人" width="100" />
                <el-table-column prop="created_at" label="操作时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.created_at) }}
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination v-model:current-page="historyPagination.currentPage"
                    v-model:page-size="historyPagination.pageSize" :page-sizes="[10, 20, 50]" :total="historyData.total"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleHistorySizeChange"
                    @current-change="handleHistoryCurrentChange" />
            </div>
        </el-card>

        <!-- 配置设置 -->
        <el-card class="config-card">
            <template #header>
                <div class="card-header">
                    <span>清理配置</span>
                    <el-button link @click="loadConfig">
                        <el-icon>
                            <Refresh />
                        </el-icon> 刷新配置
                    </el-button>
                </div>
            </template>

            <el-form :model="configForm" label-width="120px" class="config-form">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="启用自动清理">
                            <el-switch v-model="configForm.cleanup_enabled" active-text="启用" inactive-text="禁用" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="默认过期天数">
                            <el-input-number v-model="configForm.cleanup_expire_days" :min="1" :max="365"
                                style="width: 120px" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="执行时间">
                            <el-input 
                                v-model="configForm.cleanup_schedule" 
                                placeholder="Cron表达式，如：0 2 * * *"
                                @blur="validateCronExpression"
                                :class="{ 'is-error': cronValidation.error }"
                            >
                                <template #append>
                                    <el-button @click="showCronExamples" type="primary" size="small">
                                        <el-icon><QuestionFilled /></el-icon>
                                    </el-button>
                                </template>
                            </el-input>
                            <div class="form-help" v-if="!cronValidation.error">
                                {{ cronValidation.description || 'Cron表达式，默认每天凌晨2点执行' }}
                                <span v-if="cronValidation.nextExecution" class="next-execution">
                                    (下次执行: {{ formatDateTime(cronValidation.nextExecution) }})
                                </span>
                            </div>
                            <div class="form-error" v-if="cronValidation.error">
                                {{ cronValidation.error }}
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="批处理大小">
                            <el-input-number v-model="configForm.cleanup_batch_size" :min="100" :max="10000"
                                style="width: 120px" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" @click="saveConfig" :loading="configLoading">
                        <el-icon>
                            <Check />
                        </el-icon> 保存配置
                    </el-button>
                    <el-button @click="loadConfig">
                        <el-icon>
                            <Refresh />
                        </el-icon> 重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- Cron 示例对话框 -->
        <el-dialog
            v-model="cronExamplesVisible"
            title="Cron 表达式示例"
            width="800px"
            destroy-on-close
        >
            <div v-loading="cronExamplesLoading">
                <el-alert
                    title="Cron 表达式格式说明"
                    type="info"
                    :closable="false"
                    style="margin-bottom: 20px"
                >
                    <template #default>
                        <p><strong>格式：</strong>分钟 小时 日 月 星期</p>
                        <p><strong>字段范围：</strong></p>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                            <li>分钟：0-59</li>
                            <li>小时：0-23</li>
                            <li>日：1-31</li>
                            <li>月：1-12</li>
                            <li>星期：0-7 (0和7都表示周日)</li>
                        </ul>
                        <p><strong>特殊字符：</strong>* (任意值)、- (范围)、, (列表)、/ (步长)</p>
                    </template>
                </el-alert>

                <div class="cron-examples">
                    <h4>常用示例：</h4>
                    <el-row :gutter="20">
                        <el-col :span="12" v-for="example in cronExamples" :key="example.expression">
                            <el-card 
                                class="example-card" 
                                shadow="hover"
                                @click="selectCronExample(example.expression)"
                                style="cursor: pointer; margin-bottom: 10px;"
                            >
                                <div class="example-content">
                                    <div class="example-expression">
                                        <el-tag type="primary">{{ example.expression }}</el-tag>
                                    </div>
                                    <div class="example-description">
                                        {{ example.description }}
                                    </div>
                                    <div class="example-category">
                                        <el-tag size="small" type="info">{{ example.category }}</el-tag>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cronExamplesVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    getExpiredStats,
    executeManualCleanup,
    getCleanupConfig,
    updateCleanupConfig,
    getCleanupHistory,
    previewCleanupData,
    validateCronExpression as validateCronAPI,
    getCronExamples
} from '@/api/discovery-cleanup'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/dateUtils'
import { Warning, Check, Delete, RefreshRight, QuestionFilled, DataBoard, Refresh, View, Close } from '@element-plus/icons-vue'

export default {
    name: 'DiscoveryCleanup',
    components: {
        Warning,
        Check,
        Delete,
        RefreshRight,
        QuestionFilled,
        DataBoard,
        Refresh,
        View,
        Close
    },
    data() {
        return {
            // 统计数据
            statsData: {
                expiredCount: 0,
                activeCount: 0,
                deletedCount: 0,
                totalCount: 0
            },

            // 清理表单
            cleanupForm: {
                expireDays: 1
            },

            // 预览数据
            showPreview: false,
            previewData: {
                rows: [],
                total: 0
            },
            previewPagination: {
                currentPage: 1,
                pageSize: 10
            },

            // 历史数据
            historyData: {
                rows: [],
                total: 0
            },
            historyPagination: {
                currentPage: 1,
                pageSize: 10
            },

            // 配置表单
            configForm: {
                cleanup_enabled: true,
                cleanup_expire_days: 1,
                cleanup_schedule: '0 2 * * *',
                cleanup_batch_size: 1000
            },

            // 加载状态
            statsLoading: false,
            previewLoading: false,
            cleanupLoading: false,
            historyLoading: false,
            configLoading: false,

            // Cron 表达式验证
            cronValidation: {
                error: null,
                description: null,
                nextExecution: null
            },

            // Cron 示例对话框
            cronExamplesVisible: false,
            cronExamples: [],
            cronExamplesLoading: false
        }
    },

    mounted() {
        this.init()
    },

    methods: {
        // 初始化
        async init() {
            await this.refreshStats()
            await this.loadHistory()
            await this.loadConfig()
        },

        // 刷新统计信息
        async refreshStats() {
            this.statsLoading = true
            try {
                const response = await getExpiredStats({
                    expireDays: this.cleanupForm.expireDays
                })

                if (response.code === 0) {
                    this.statsData = response.data
                } else {
                    ElMessage.error(response.msg || '获取统计信息失败')
                }
            } catch (error) {
                console.error('获取统计信息失败:', error)
                ElMessage.error('获取统计信息失败')
            } finally {
                this.statsLoading = false
            }
        },

        // 预览清理数据
        async previewCleanup() {
            this.previewLoading = true
            try {
                const response = await previewCleanupData({
                    expireDays: this.cleanupForm.expireDays,
                    currentPage: this.previewPagination.currentPage,
                    pageSize: this.previewPagination.pageSize
                })

                if (response.code === 0) {
                    this.previewData = response.data
                    this.showPreview = true
                } else {
                    ElMessage.error(response.msg || '预览数据失败')
                }
            } catch (error) {
                console.error('预览数据失败:', error)
                ElMessage.error('预览数据失败')
            } finally {
                this.previewLoading = false
            }
        },

        // 执行清理
        async executeCleanup() {
            try {
                await ElMessageBox.confirm(
                    `确定要清理 ${this.cleanupForm.expireDays} 天前的发现结果数据吗？此操作不可恢复！`,
                    '确认清理',
                    {
                        confirmButtonText: '确定清理',
                        cancelButtonText: '取消',
                        type: 'warning',
                        confirmButtonClass: 'el-button--danger'
                    }
                )

                this.cleanupLoading = true
                const response = await executeManualCleanup({
                    expireDays: this.cleanupForm.expireDays
                })

                if (response.code === 0) {
                    ElMessage.success(response.msg || '清理完成')
                    await this.refreshStats()
                    await this.loadHistory()
                    this.showPreview = false
                } else {
                    ElMessage.error(response.msg || '清理失败')
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('执行清理失败:', error)
                    ElMessage.error('执行清理失败')
                }
            } finally {
                this.cleanupLoading = false
            }
        },

        // 加载历史记录
        async loadHistory() {
            this.historyLoading = true
            try {
                const response = await getCleanupHistory({
                    currentPage: this.historyPagination.currentPage,
                    pageSize: this.historyPagination.pageSize
                })

                if (response.code === 0) {
                    this.historyData = response.data
                } else {
                    ElMessage.error(response.msg || '获取历史记录失败')
                }
            } catch (error) {
                console.error('获取历史记录失败:', error)
                ElMessage.error('获取历史记录失败')
            } finally {
                this.historyLoading = false
            }
        },

        // 加载配置
        async loadConfig() {
            this.configLoading = true
            try {
                const response = await getCleanupConfig({})

                if (response.code === 0) {
                    const config = response.data.config
                    this.configForm = {
                        cleanup_enabled: config.cleanup_enabled === 'true',
                        cleanup_expire_days: parseInt(config.cleanup_expire_days) || 1,
                        cleanup_schedule: config.cleanup_schedule || '0 2 * * *',
                        cleanup_batch_size: parseInt(config.cleanup_batch_size) || 1000
                    }
                } else {
                    ElMessage.error(response.msg || '获取配置失败')
                }
            } catch (error) {
                console.error('获取配置失败:', error)
                ElMessage.error('获取配置失败')
            } finally {
                this.configLoading = false
            }
        },

        // 保存配置
        async saveConfig() {
            this.configLoading = true
            try {
                const config = {
                    cleanup_enabled: this.configForm.cleanup_enabled ? 'true' : 'false',
                    cleanup_expire_days: this.configForm.cleanup_expire_days.toString(),
                    cleanup_schedule: this.configForm.cleanup_schedule,
                    cleanup_batch_size: this.configForm.cleanup_batch_size.toString()
                }

                const response = await updateCleanupConfig({ config })

                if (response.code === 0) {
                    ElMessage.success('配置保存成功')
                } else {
                    ElMessage.error(response.msg || '配置保存失败')
                }
            } catch (error) {
                console.error('保存配置失败:', error)
                ElMessage.error('保存配置失败')
            } finally {
                this.configLoading = false
            }
        },

        // 预览分页处理
        handlePreviewSizeChange(size) {
            this.previewPagination.pageSize = size
            this.previewCleanup()
        },

        handlePreviewCurrentChange(page) {
            this.previewPagination.currentPage = page
            this.previewCleanup()
        },

        // 历史分页处理
        handleHistorySizeChange(size) {
            this.historyPagination.pageSize = size
            this.loadHistory()
        },

        handleHistoryCurrentChange(page) {
            this.historyPagination.currentPage = page
            this.loadHistory()
        },

        // 工具方法
        formatDateTime(dateTime) {
            return formatDateTime(dateTime)
        },

        getDaysAgo(dateTime) {
            const now = new Date()
            const date = new Date(dateTime)
            const diffTime = Math.abs(now - date)
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
            return diffDays
        },

        getLogLevelType(level) {
            const typeMap = {
                'info': 'success',
                'warning': 'warning',
                'error': 'danger'
            }
            return typeMap[level] || 'info'
        },

        // Cron 表达式验证
        async validateCronExpression() {
            if (!this.configForm.cleanup_schedule) {
                this.cronValidation = {
                    error: null,
                    description: null,
                    nextExecution: null
                }
                return
            }

            try {
                const response = await validateCronAPI({
                    cronExpression: this.configForm.cleanup_schedule
                })

                if (response.code === 0) {
                    this.cronValidation = {
                        error: null,
                        description: response.data.description,
                        nextExecution: response.data.nextExecution
                    }
                } else {
                    this.cronValidation = {
                        error: response.msg || response.data?.error || 'Cron表达式格式错误',
                        description: null,
                        nextExecution: null
                    }
                }
            } catch (error) {
                console.error('验证Cron表达式失败:', error)
                this.cronValidation = {
                    error: '验证Cron表达式失败',
                    description: null,
                    nextExecution: null
                }
            }
        },

        // 显示 Cron 示例
        async showCronExamples() {
            this.cronExamplesVisible = true
            this.cronExamplesLoading = true

            try {
                const response = await getCronExamples()
                if (response.code === 0) {
                    this.cronExamples = response.data.examples || []
                } else {
                    ElMessage.error('获取Cron示例失败')
                }
            } catch (error) {
                console.error('获取Cron示例失败:', error)
                ElMessage.error('获取Cron示例失败')
            } finally {
                this.cronExamplesLoading = false
            }
        },

        // 选择 Cron 示例
        selectCronExample(expression) {
            this.configForm.cleanup_schedule = expression
            this.cronExamplesVisible = false
            this.validateCronExpression()
        }
    }
}
</script>

<style scoped>
.discovery-cleanup {
    padding: 20px;
}

.page-header {
    margin-bottom: 20px;
}

.stats-row {
    margin-bottom: 20px;
}

.stats-card {
    height: 100px;
}

.stats-content {
    display: flex;
    align-items: center;
    height: 100%;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stats-icon.expired {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.stats-icon.active {
    background: linear-gradient(135deg, #51cf66, #40c057);
}

.stats-icon.deleted {
    background: linear-gradient(135deg, #868e96, #6c757d);
}

.stats-icon.total {
    background: linear-gradient(135deg, #339af0, #228be6);
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    line-height: 1;
}

.stats-label {
    font-size: 14px;
    color: #7f8c8d;
    margin-top: 5px;
}

.operation-card,
.preview-card,
.history-card,
.config-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.operation-form {
    margin: 0;
}

.form-help {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.config-form {
    margin-top: 20px;
}

/* Cron 表达式验证样式 */
.form-error {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
}

.next-execution {
    color: #67c23a;
    font-weight: 500;
}

.is-error :deep(.el-input__inner) {
    border-color: #f56c6c;
}

/* Cron 示例对话框样式 */
.cron-examples h4 {
    margin: 0 0 15px 0;
    color: #303133;
}

.example-card {
    transition: all 0.3s ease;
}

.example-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.example-content {
    padding: 10px;
}

.example-expression {
    margin-bottom: 8px;
}

.example-description {
    color: #606266;
    font-size: 14px;
    margin-bottom: 8px;
}

.example-category {
    text-align: right;
}

@media (max-width: 768px) {
    .discovery-cleanup {
        padding: 10px;
    }

    .stats-row {
        margin-bottom: 10px;
    }

    .operation-card,
    .preview-card,
    .history-card,
    .config-card {
        margin-bottom: 10px;
    }
}
</style>