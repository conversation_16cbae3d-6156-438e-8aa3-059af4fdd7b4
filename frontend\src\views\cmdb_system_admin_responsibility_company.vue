<style lang="scss" scoped>
.user-manage {
  .search-card {
    margin-bottom: 10px;
    height: auto;
  }

  :deep(.el-table) {
    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap; /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all; /* 尽量保持单词完整，不强制断开 */
      max-width: 400px; /* 保持最大宽度不变 */
      width: 100%; /* 确保宽度一致 */
      word-wrap: break-word; /* 当单词超过容器宽度时允许换行 */
      display: inline-block; /* 确保元素可以正确处理宽度 */
    }
    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }

  .pagination {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
  }

  /* 搜索按钮对齐 */
  .search-buttons-col {
    display: flex;
    align-items: center;
  }

  .search-buttons {
    margin-bottom: 0;
    text-align: right;
    width: 100%;
  }

  /* 按钮容器 */
  .button-container {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    gap: 10px;
  }

  /* 统一操作按钮区样式 */
  .unified-action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: nowrap;
  }

  .action-bar-left, .action-bar-right {
    display: flex;
    gap: 8px;
    flex-wrap: nowrap;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .unified-action-bar {
      flex-direction: column;
      align-items: flex-start;
    }

    .action-bar-left, .action-bar-right {
      margin-bottom: 8px;
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
    }

    .action-bar-right {
      justify-content: flex-end;
    }
  }
}

.form-control {
  width: auto; /* 占满父容器宽度 */
  min-width: 190px;
}

/* 功能域样式 */
.domain-preview {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.domain-preview:hover {
  background-color: #f5f7fa;
}

.core-count {
  color: #f56c6c;
  font-weight: bold;
  margin-left: 4px;
  white-space: nowrap;
}

.domain-detail {
  padding: 10px;
}

.domain-detail h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.domain-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 12px;
}

.domain-item {
  padding: 6px 10px;
  margin-bottom: 4px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.core-domain {
  background-color: #fef0f0;
  color: #f56c6c;
  font-weight: 500;
}

.core-tag {
  background-color: #f56c6c;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

.domain-summary {
  font-size: 14px;
  color: #606266;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

/* 自定义弹出框样式 */
:deep(.domain-popover) {
  max-width: 500px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表单验证样式 */
.inline-form-item {
  margin-bottom: 0;
  width: 100%;
}

:deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 110px;
  font-size: 12px;
  color: #f56c6c;
}

:deep(.el-form-item.is-error .el-input__inner),
:deep(.el-form-item.is-error .el-textarea__inner),
:deep(.el-form-item.is-error .el-select .el-input__inner) {
  border-color: #f56c6c;
}

/* 必填字段标签样式 */
.required-label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>

<template>
  <div class="user-manage">
    <el-dialog
      v-model="dialogVisible.add"
      title="新增系统信息"
      width="450"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="addFormRef" label-position="right" status-icon>
          <!-- 自建系统编号 -->
          <p>
            <span class="label">自建系统编号:</span>
            <el-input
              v-model="formData.self_build_system_id"
              style="width: 240px"
              clearable
            />
          </p>

          <!-- 业务系统简称 -->
          <p>
            <el-form-item prop="system_abbreviation" class="inline-form-item">
              <span class="label required-label">业务系统简称:</span>
              <el-input
                v-model="formData.system_abbreviation"
                style="width: 240px"
                clearable
                placeholder="请输入业务系统简称"
              />
            </el-form-item>
          </p>

        <!-- 团队线条 -->
        <p>
          <el-form-item prop="business_line" class="inline-form-item">
            <span class="label required-label">团队线条:</span>
            <el-select
              v-model="formData.business_line"
              style="width: 240px"
              placeholder="请选择团队线条"
              clearable
            >
              <el-option
                v-for="item in businessLines"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

          <!-- 主岗 -->
          <p>
            <el-form-item prop="main_admin" class="inline-form-item">
              <span class="label required-label">主岗:</span>
              <el-select
                v-model="formData.main_admin"
                style="width: 240px"
                filterable
                placeholder="请选择主岗"
                clearable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.username"
                  :label="item.real_name"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </p>

        <!-- 备岗 -->
        <p>
          <span class="label">备岗:</span>
          <el-select
            v-model="formData.backup_admin"
            style="width: 240px"
            filterable
            clearable
            placeholder="请选择备岗"
          >
            <el-option
              v-for="item in userList"
              :key="item.username"
              :label="item.real_name"
              :value="item.username"
            />
          </el-select>
        </p>

        <!-- 系统分级 -->
        <p>
          <el-form-item prop="system_level" class="inline-form-item">
            <span class="label required-label">系统分级:</span>
            <el-select
              v-model="formData.system_level"
              style="width: 240px"
              placeholder="请选择系统分级"
              clearable
            >
              <el-option
                v-for="item in systemlevels"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 运行状态 -->
        <p>
          <el-form-item prop="operation_status" class="inline-form-item">
            <span class="label required-label">运行状态:</span>
            <el-select
              v-model="formData.operation_status"
              style="width: 240px"
              placeholder="请选择运行状态"
              clearable
            >
              <el-option
                v-for="item in operationStatuses"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 业务主管部门 -->
        <p>
          <span class="label">业务主管部门:</span>
          <el-input
            v-model="formData.business_department"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 系统属性 -->
        <p>
          <el-form-item prop="system_attribute" class="inline-form-item">
            <span class="label required-label">系统属性:</span>
            <el-select
              v-model="formData.system_attribute"
              style="width: 240px"
              placeholder="请选择系统属性"
              clearable
            >
              <el-option
                v-for="item in systemattributes"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_name"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 上线时间 -->
        <p>
          <span class="label">上线时间:</span>
          <el-date-picker
            v-model="formData.go_live_date"
            type="date"
            placeholder="选择日期"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </p>

        <!-- 下线时间 -->
        <p>
          <span class="label">下线时间:</span>
          <el-date-picker
            v-model="formData.decommission_date"
            type="date"
            placeholder="选择日期"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </p>

        <!-- 重大历程 -->
        <p>
          <el-form-item prop="major_milestones" class="inline-form-item">
            <span class="label required-label">重大历程:</span>
            <el-input
              type="textarea"
              :rows="2"
              v-model="formData.major_milestones"
              style="width: 240px"
              placeholder="请输入重大历程"
              clearable
            />
          </el-form-item>
        </p>

        <!-- 业务系统行业名称 -->
        <p>
          <el-form-item prop="industry_name" class="inline-form-item">
            <span class="label required-label">业务系统行业名称:</span>
            <el-input
              v-model="formData.industry_name"
              style="width: 240px"
              clearable
              placeholder="请输入业务系统行业名称"
            />
          </el-form-item>
        </p>

        <!-- 业务系统英文简称 -->
        <p>
          <span class="label">业务系统英文简称:</span>
          <el-input
            v-model="formData.monitoring_system_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 系统功能简述 -->
        <p>
          <el-form-item prop="system_function_summary" class="inline-form-item">
            <span class="label required-label">系统功能简述:</span>
            <el-input
              type="textarea"
              :rows="2"
              v-model="formData.system_function_summary"
              style="width: 240px"
              placeholder="请输入系统功能简述"
              clearable
            />
          </el-form-item>
        </p>

        <!-- 系统形态 -->
        <p>
          <span class="label">系统形态:</span>
          <el-input
            v-model="formData.system_form"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- CS客户端程序名称 -->
        <p>
          <span class="label">CS客户端程序名称:</span>
          <el-input
            v-model="formData.cs_client_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- BS URL地址 -->
        <p>
          <span class="label">BS URL地址:</span>
          <el-input v-model="formData.bs_url" style="width: 240px" clearable />
        </p>

        <!-- IP:端口 -->
        <p>
          <span class="label">IP:端口:</span>
          <el-input v-model="formData.ip_port" style="width: 240px" clearable />
        </p>

        <!-- 业务系统大类 -->
        <p>
          <el-form-item prop="system_category" class="inline-form-item">
            <span class="label required-label">业务系统大类:</span>
            <el-select
              v-model="formData.system_category"
              style="width: 240px"
              placeholder="请选择业务系统大类"
              clearable
            >
              <el-option
                v-for="item in systemCategories"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>


        <!-- 是否建立备份策略 -->
        <p>
          <span class="label">是否建立备份策略:</span>
          <el-select
            v-model="formData.has_backup_strategy"
            style="width: 240px"
            clearable
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </p>

        <!-- 数字化分类 -->
        <p>
          <span class="label">数字化分类:</span>
          <el-input
            v-model="formData.digital_classification"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- JR/T 0059-2010备份能力标准 -->
        <p>
          <span class="label">JR/T 0059-2010备份能力标准:</span>
          <el-select
            v-model="formData.jrt_0059_backup_standard"
            style="width: 240px"
            placeholder="请选择备份能力标准"
            clearable
          >
            <el-option
              v-for="item in backupStandards"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>

        <!-- 系统分类 -->
        <p>
          <span class="label">系统分类:</span>
          <el-select
            v-model="formData.xinchuang_category_major"
            style="width: 240px"
            placeholder="请选择系统分类"
            clearable
          >
            <el-option
              v-for="item in xinchuangcategorymajors"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>
        <!-- 信创小类 -->
        <p>
          <span class="label">信创小类:</span>
          <el-select
            v-model="formData.xinchuang_category_minor"
            style="width: 240px"
            placeholder="请选择信创小类"
            clearable
          >
            <el-option
              v-for="item in xinchuangcategoryminors"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>

        <!-- 软著名称 -->
        <p>
          <span class="label">软著名称:</span>
          <el-input
            v-model="formData.software_copyright_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 建设方式 -->
        <p>
          <span class="label">建设方式:</span>
          <el-select
            v-model="formData.construction_method"
            style="width: 240px"
            placeholder="请选择建设方式"
            clearable
          >
            <el-option
              v-for="item in constructionMethods"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_name"
            />
          </el-select>
        </p>

        <!-- 技术路线 -->
        <p>
          <span class="label">技术路线:</span>
          <el-select
            v-model="formData.technical_route"
            style="width: 240px"
            placeholder="请选择技术路线"
            clearable
          >
            <el-option
              v-for="item in technicalRoutes"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_name"
            />
          </el-select>
        </p>

        <!-- 信创状态 -->
        <p>
          <span class="label">信创状态:</span>
          <el-select
            v-model="formData.xinchuang_status"
            style="width: 240px"
            placeholder="请选择信创状态"
            clearable
          >
            <el-option label="未信创" value="未信创"></el-option>
            <el-option label="完成开发或测试" value="完成开发或测试"></el-option>
            <el-option label="非全栈双轨" value="非全栈双轨"></el-option>
            <el-option label="非全栈单轨" value="非全栈单轨"></el-option>
            <el-option label="全栈双轨" value="全栈双轨"></el-option>
            <el-option label="全栈单轨" value="全栈单轨"></el-option>
          </el-select>
        </p>

        <!-- 等保等级 -->
        <p>
          <span class="label">等保等级:</span>
          <el-select
            v-model="formData.security_level"
            style="width: 240px"
            placeholder="请选择等保等级"
            clearable
          >
            <el-option label="一级" value="一级"></el-option>
            <el-option label="二级" value="二级"></el-option>
            <el-option label="三级" value="三级"></el-option>
          </el-select>
        </p>

        <!-- 通用功能域 -->
        <p>
          <span class="label">通用功能域:</span>
          <el-select
            v-model="formData.general_function_domains"
            multiple
            collapse-tags
            placeholder="请选择通用功能域"
            style="width: 240px"
            clearable
          >
            <el-option
              v-for="item in generalFunctionDomains"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span :style="{ color: item.isCore ? 'red' : 'inherit' }">{{ item.label }}</span>
              <span v-if="item.isCore" style="color: red; margin-left: 5px;">(核心)</span>
            </el-option>
          </el-select>
        </p>

        <!-- 经营机构功能域 -->
        <p>
          <span class="label">经营机构功能域:</span>
          <el-select
            v-model="formData.futures_function_domains"
            multiple
            collapse-tags
            placeholder="请选择经营机构功能域"
            style="width: 240px"
            clearable
          >
            <el-option
              v-for="item in futuresFunctionDomains"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span :style="{ color: item.isCore ? 'red' : 'inherit' }">{{ item.label }}</span>
              <span v-if="item.isCore" style="color: red; margin-left: 5px;">(核心)</span>
            </el-option>
          </el-select>
        </p>

        <!-- 是否外部上报 -->
        <p>
          <span class="label">是否外部上报:</span>
          <el-select
            v-model="formData.is_reported_to_external"
            style="width: 240px"
            clearable
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </p>

        <!-- CentOS7数量 -->
        <!-- <p>
          <span class="label">CentOS7数量:</span>
          <el-input-number
            v-model="formData.centos7_count"
            controls-position="right"
            style="width: 240px"
          />
        </p> -->

        <!-- 备注 -->
        <p>
          <span class="label">备注:</span>
          <el-input
            type="textarea"
            :rows="2"
            v-model="formData.remarks"
            style="width: 240px"
            clearable
          />
        </p>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">返回</el-button>
          <el-button type="primary" @click="validateAndSubmitAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogVisible.edit"
      title="编辑系统信息"
      width="450"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="editFormRef" label-position="right" status-icon>
          <!-- 自建系统编号 -->
          <p>
            <span class="label">自建系统编号:</span>
            <el-input
              v-model="formData.self_build_system_id"
              style="width: 240px"
              clearable
            />
          </p>

          <!-- 业务系统简称 -->
          <p>
            <el-form-item prop="system_abbreviation" class="inline-form-item">
              <span class="label required-label">业务系统简称:</span>
              <el-input
                v-model="formData.system_abbreviation"
                style="width: 240px"
                clearable
                placeholder="请输入业务系统简称"
              />
            </el-form-item>
          </p>

        <!-- 团队线条 -->
        <p>
          <el-form-item prop="business_line" class="inline-form-item">
            <span class="label required-label">团队线条:</span>
            <el-select
              v-model="formData.business_line"
              style="width: 240px"
              placeholder="请选择团队线条"
              clearable
            >
              <el-option
                v-for="item in businessLines"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

          <!-- 主岗 -->
          <p>
            <el-form-item prop="main_admin" class="inline-form-item">
              <span class="label required-label">主岗:</span>
              <el-select
                v-model="formData.main_admin"
                style="width: 240px"
                filterable
                placeholder="请选择主岗"
                clearable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.username"
                  :label="item.real_name"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </p>

        <!-- 备岗 -->
        <p>
          <span class="label">备岗:</span>
          <el-select
            v-model="formData.backup_admin"
            style="width: 240px"
            filterable
            clearable
            placeholder="请选择备岗"
          >
            <el-option
              v-for="item in userList"
              :key="item.username"
              :label="item.real_name"
              :value="item.username"
            />
          </el-select>
        </p>

        <!-- 系统分级 -->
        <p>
          <el-form-item prop="system_level" class="inline-form-item">
            <span class="label required-label">系统分级:</span>
            <el-select
              v-model="formData.system_level"
              style="width: 240px"
              placeholder="请选择系统分级"
              clearable
            >
              <el-option
                v-for="item in systemlevels"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 运行状态 -->
        <p>
          <el-form-item prop="operation_status" class="inline-form-item">
            <span class="label required-label">运行状态:</span>
            <el-select
              v-model="formData.operation_status"
              style="width: 240px"
              placeholder="请选择运行状态"
              clearable
            >
              <el-option
                v-for="item in operationStatuses"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 业务主管部门 -->
        <p>
          <span class="label">业务主管部门:</span>
          <el-input
            v-model="formData.business_department"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 系统属性 -->
        <p>
          <el-form-item prop="system_attribute" class="inline-form-item">
            <span class="label required-label">系统属性:</span>
            <el-select
              v-model="formData.system_attribute"
              style="width: 240px"
              placeholder="请选择系统属性"
              clearable
            >
              <el-option
                v-for="item in systemattributes"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_name"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 上线时间 -->
        <p>
          <span class="label">上线时间:</span>
          <el-date-picker
            v-model="formData.go_live_date"
            type="date"
            placeholder="选择日期"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </p>

        <!-- 下线时间 -->
        <p>
          <span class="label">下线时间:</span>
          <el-date-picker
            v-model="formData.decommission_date"
            type="date"
            placeholder="选择日期"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </p>

        <!-- 重大历程 -->
        <p>
          <el-form-item prop="major_milestones" class="inline-form-item">
            <span class="label required-label">重大历程:</span>
            <el-input
              type="textarea"
              :rows="2"
              v-model="formData.major_milestones"
              style="width: 240px"
              placeholder="请输入重大历程"
              clearable
            />
          </el-form-item>
        </p>

        <!-- 业务系统行业名称 -->
        <p>
          <el-form-item prop="industry_name" class="inline-form-item">
            <span class="label required-label">业务系统行业名称:</span>
            <el-input
              v-model="formData.industry_name"
              style="width: 240px"
              clearable
              placeholder="请输入业务系统行业名称"
            />
          </el-form-item>
        </p>

        <!-- 业务系统英文简称 -->
        <p>
          <span class="label">业务系统英文简称:</span>
          <el-input
            v-model="formData.monitoring_system_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 系统功能简述 -->
        <p>
          <el-form-item prop="system_function_summary" class="inline-form-item">
            <span class="label required-label">系统功能简述:</span>
            <el-input
              type="textarea"
              :rows="2"
              v-model="formData.system_function_summary"
              style="width: 240px"
              placeholder="请输入系统功能简述"
              clearable
            />
          </el-form-item>
        </p>

        <!-- 系统形态 -->
        <p>
          <span class="label">系统形态:</span>
          <el-input
            v-model="formData.system_form"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- CS客户端程序名称 -->
        <p>
          <span class="label">CS客户端程序名称:</span>
          <el-input
            v-model="formData.cs_client_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- BS URL地址 -->
        <p>
          <span class="label">BS URL地址:</span>
          <el-input v-model="formData.bs_url" style="width: 240px" clearable />
        </p>

        <!-- IP:端口 -->
        <p>
          <span class="label">IP:端口:</span>
          <el-input v-model="formData.ip_port" style="width: 240px" clearable />
        </p>

        <!-- 业务系统大类 -->
        <p>
          <el-form-item prop="system_category" class="inline-form-item">
            <span class="label required-label">业务系统大类:</span>
            <el-select
              v-model="formData.system_category"
              style="width: 240px"
              placeholder="请选择业务系统大类"
              clearable
            >
              <el-option
                v-for="item in systemCategories"
                :key="item.dict_code"
                :label="item.dict_name"
                :value="item.dict_code"
              />
            </el-select>
          </el-form-item>
        </p>

        <!-- 是否建立备份策略 -->
        <p>
          <span class="label">是否建立备份策略:</span>
          <el-select
            v-model="formData.has_backup_strategy"
            style="width: 240px"
            clearable
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </p>

        <!-- 数字化分类 -->
        <p>
          <span class="label">数字化分类:</span>
          <el-input
            v-model="formData.digital_classification"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- JR/T 0059-2010备份能力标准 -->
        <p>
          <span class="label">JR/T 0059-2010备份能力标准:</span>
          <el-select
            v-model="formData.jrt_0059_backup_standard"
            style="width: 240px"
            placeholder="请选择备份能力标准"
            clearable
          >
            <el-option
              v-for="item in backupStandards"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>

        <!-- 系统分类 -->
        <p>
          <span class="label">系统分类:</span>
          <el-select
            v-model="formData.xinchuang_category_major"
            style="width: 240px"
            placeholder="请选择系统分类"
            clearable
          >
            <el-option
              v-for="item in xinchuangcategorymajors"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>
        <!-- 信创小类 -->
        <p>
          <span class="label">信创小类:</span>
          <el-select
            v-model="formData.xinchuang_category_minor"
            style="width: 240px"
            placeholder="请选择信创小类"
            clearable
          >
            <el-option
              v-for="item in xinchuangcategoryminors"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_code"
            />
          </el-select>
        </p>

        <!-- 软著名称 -->
        <p>
          <span class="label">软著名称:</span>
          <el-input
            v-model="formData.software_copyright_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 建设方式 -->
        <p>
          <span class="label">建设方式:</span>
          <el-select
            v-model="formData.construction_method"
            style="width: 240px"
            placeholder="请选择建设方式"
            clearable
          >
            <el-option
              v-for="item in constructionMethods"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_name"
            />
          </el-select>
        </p>

        <!-- 技术路线 -->
        <p>
          <span class="label">技术路线:</span>
          <el-select
            v-model="formData.technical_route"
            style="width: 240px"
            placeholder="请选择技术路线"
            clearable
          >
            <el-option
              v-for="item in technicalRoutes"
              :key="item.dict_code"
              :label="item.dict_name"
              :value="item.dict_name"
            />
          </el-select>
        </p>

        <!-- 信创状态 -->
        <p>
          <span class="label">信创状态:</span>
          <el-select
            v-model="formData.xinchuang_status"
            style="width: 240px"
            placeholder="请选择信创状态"
            clearable
          >
            <el-option label="未信创" value="未信创"></el-option>
            <el-option label="完成开发或测试" value="完成开发或测试"></el-option>
            <el-option label="非全栈双轨" value="非全栈双轨"></el-option>
            <el-option label="非全栈单轨" value="非全栈单轨"></el-option>
            <el-option label="全栈双轨" value="全栈双轨"></el-option>
            <el-option label="全栈单轨" value="全栈单轨"></el-option>
          </el-select>
        </p>

        <!-- 等保等级 -->
        <p>
          <span class="label">等保等级:</span>
          <el-select
            v-model="formData.security_level"
            style="width: 240px"
            placeholder="请选择等保等级"
            clearable
          >
            <el-option label="一级" value="一级"></el-option>
            <el-option label="二级" value="二级"></el-option>
            <el-option label="三级" value="三级"></el-option>
          </el-select>
        </p>

        <!-- 通用功能域 -->
        <p>
          <span class="label">通用功能域:</span>
          <el-select
            v-model="formData.general_function_domains"
            multiple
            collapse-tags
            style="width: 240px"
            placeholder="请选择通用功能域"
            clearable
          >
            <el-option
              v-for="item in generalFunctionDomains"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span :style="{ color: item.isCore ? 'red' : 'inherit' }">{{ item.label }}</span>
              <span v-if="item.isCore" style="color: red; margin-left: 5px;">(核心)</span>
            </el-option>
          </el-select>
        </p>

        <!-- 经营机构功能域 -->
        <p>
          <span class="label">经营机构功能域:</span>
          <el-select
            v-model="formData.futures_function_domains"
            multiple
            collapse-tags
            style="width: 240px"
            placeholder="请选择经营机构功能域"
            clearable
          >
            <el-option
              v-for="item in futuresFunctionDomains"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span :style="{ color: item.isCore ? 'red' : 'inherit' }">{{ item.label }}</span>
              <span v-if="item.isCore" style="color: red; margin-left: 5px;">(核心)</span>
            </el-option>
          </el-select>
        </p>

        <!-- 是否外部上报 -->
        <p>
          <span class="label">是否外部上报:</span>
          <el-select
            v-model="formData.is_reported_to_external"
            style="width: 240px"
            clearable
          >
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </p>

        <!-- 备注 -->
        <p>
          <span class="label">备注:</span>
          <el-input
            type="textarea"
            :rows="2"
            v-model="formData.remarks"
            style="width: 240px"
            clearable
          />
        </p>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitEdit">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogVisible.delete"
      title="删除自建系统编号"
      width="500"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-alert
        type="warning"
        :title="`确定要删除 自建系统编号 为 ${formData.self_build_system_id} 的记录吗？`"
        :closable="false"
      />
      <template #footer>
        <div>
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <p class="description" style="line-height: 1.5; margin-bottom: 10px; color: #E6A23C; background-color: #FDF6EC; padding: 8px 12px; border-radius: 4px; border-left: 4px solid #E6A23C;">
        一级为必须监控，实时交易；二级为必须监控，非实时交易、归档备份；三级为非必须监控，生产交易终端、办公、服务类；四级非必须监控，测试、仿真、已下线
    </p>


    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true">
        <el-row :gutter="10">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item label="自建系统编号">
              <el-input
                v-model="search.self_build_system_id"
                placeholder="请输入自建系统编号"
                clearable
                class="form-control"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务系统简称">
              <el-input
                v-model="search.system_abbreviation"
                placeholder="请输入业务系统简称"
                clearable
                class="form-control"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主岗">
              <el-select
                v-model="search.main_admin"
                placeholder="请选择主岗"
                filterable
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.username"
                  :label="item.real_name"
                  :value="item.real_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统属性">
              <el-select
                v-model="search.system_attribute"
                placeholder="请选择系统属性"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in systemattributes"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item label="系统分级">
              <el-select
                v-model="search.system_level"
                placeholder="请选择系统分级"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in systemlevels"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="JR/T 0059-2010">
              <el-select
                v-model="search.jrt_0059_backup_standard"
                placeholder="请选择备份能力标准"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in backupStandards"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统分类">
              <el-select
                v-model="search.xinchuang_category_major"
                placeholder="请选择系统分类"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in xinchuangcategorymajors"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第三行 -->
          <el-col :span="6">
            <el-form-item label="信创小类">
              <el-select
                v-model="search.xinchuang_category_minor"
                placeholder="请选择信创小类"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in xinchuangcategoryminors"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否外部上报">
              <el-select
                v-model="search.is_reported_to_external"
                placeholder="请选择是否外部上报"
                clearable
                class="form-control"
              >
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第四行 -->
          <el-col :span="6">
            <el-form-item label="建设方式">
              <el-select
                v-model="search.construction_method"
                placeholder="请选择建设方式"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in constructionMethods"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="技术路线">
              <el-select
                v-model="search.technical_route"
                placeholder="请选择技术路线"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in technicalRoutes"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运行状态">
              <el-select
                v-model="search.operation_status"
                placeholder="请选择运行状态"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in operationStatuses"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第五行 -->
          <el-col :span="6">
            <el-form-item label="信创状态">
              <el-select
                v-model="search.xinchuang_status"
                placeholder="请选择信创状态"
                clearable
                class="form-control"
              >
                <el-option label="未信创" value="未信创" />
                <el-option label="完成开发或测试" value="完成开发或测试" />
                <el-option label="非全栈双轨" value="非全栈双轨" />
                <el-option label="非全栈单轨" value="非全栈单轨" />
                <el-option label="全栈双轨" value="全栈双轨" />
                <el-option label="全栈单轨" value="全栈单轨" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="等保等级">
              <el-select
                v-model="search.security_level"
                placeholder="请选择等保等级"
                clearable
                class="form-control"
              >
                <el-option label="一级" value="一级" />
                <el-option label="二级" value="二级" />
                <el-option label="三级" value="三级" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第六行 -->
          <el-col :span="6">
            <el-form-item label="团队线条">
              <el-select
                v-model="search.business_line"
                placeholder="请选择团队线条"
                filterable
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in businessLines"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务系统大类">
              <el-select
                v-model="search.system_category"
                placeholder="请选择业务系统大类"
                filterable
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in systemCategories"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第六行 -->
          <el-col :span="24" class="search-buttons-col">
            <el-form-item label=" " class="form-item-with-label search-buttons">
              <div class="button-container">
                <el-button type="primary" @click="loadData">
                  <el-icon><Search /></el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button
          type="success"
          :disabled="!hasInsertPermission"
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>新增系统
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData">
          <el-icon><Download /></el-icon> 导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="userArr"
        ref="table"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="序号" v-if="false"></el-table-column>
        <!-- 自建系统编号 -->
        <el-table-column
          prop="self_build_system_id"
          label="自建系统编号"
          sortable
        ></el-table-column>

        <!-- 业务系统简称 -->
        <el-table-column
          prop="system_abbreviation"
          label="业务系统简称"
          sortable
        ></el-table-column>

        <!-- 团队线条 -->
        <el-table-column
          label="团队线条"
          sortable
        >
          <template #default="scope">
            {{ getBusinessLineName(scope.row.business_line) }}
          </template>
        </el-table-column>

        <!-- 主岗 -->
        <el-table-column
          label="主岗"
          sortable
        >
          <template #default="scope">
            {{ getUserRealName(scope.row.main_admin) }}
          </template>
        </el-table-column>

        <!-- 备岗 -->
        <el-table-column
          label="备岗"
          sortable
        >
          <template #default="scope">
            {{ getUserRealName(scope.row.backup_admin) }}
          </template>
        </el-table-column>

        <!-- 系统分级 -->
        <el-table-column
          prop="system_level"
          label="系统分级"
          sortable
        ></el-table-column>

        <!-- 运行状态 -->
        <el-table-column
          label="运行状态"
          sortable
        >
          <template #default="scope">
            <el-tag :type="getOperationStatusColor(scope.row.operation_status)">
              {{ getOperationStatusName(scope.row.operation_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 对应服务器数量 -->
        <el-table-column
          prop="server_count"
          label="对应服务器数量"
          sortable
        ></el-table-column>

        <!-- 监控覆盖率 -->
        <el-table-column
          prop="monitoring_coverage"
          label="监控覆盖率"
          sortable
        ></el-table-column>

        <!-- 业务主管部门 -->
        <el-table-column
          prop="business_department"
          label="业务主管部门"
          sortable
        ></el-table-column>

        <!-- 系统属性 -->
        <el-table-column
          prop="system_attribute"
          label="系统属性"
          sortable
        ></el-table-column>

        <!-- 上线时间 -->
        <el-table-column
          prop="go_live_date"
          label="上线时间"
          sortable
        ></el-table-column>

        <!-- 下线时间 -->
        <el-table-column
          prop="decommission_date"
          label="下线时间"
          sortable
        ></el-table-column>

        <!-- 重大历程 -->
        <el-table-column
          prop="major_milestones"
          label="重大历程"
          sortable
        ></el-table-column>

        <!-- 业务系统行业名称 -->
        <el-table-column
          prop="industry_name"
          label="业务系统行业名称"
          sortable
        ></el-table-column>

        <!-- 业务系统英文简称 -->
        <el-table-column
          prop="monitoring_system_name"
          label="业务系统英文简称"
          sortable
        ></el-table-column>

        <!-- 系统功能简述 -->
        <el-table-column
          prop="system_function_summary"
          label="系统功能简述"
          sortable
        ></el-table-column>

        <!-- 系统形态 -->
        <el-table-column
          prop="system_form"
          label="系统形态"
          sortable
        ></el-table-column>

        <!-- CS客户端程序名称 -->
        <el-table-column
          prop="cs_client_name"
          label="CS客户端程序名称"
          sortable
        ></el-table-column>

        <!-- BS URL地址 -->
        <el-table-column
          prop="bs_url"
          label="BS URL地址"
          sortable
        ></el-table-column>

        <!-- IP:端口 -->
        <el-table-column
          prop="ip_port"
          label="IP:端口"
          sortable
        ></el-table-column>

        <!-- 业务系统大类 -->
        <el-table-column
          label="业务系统大类"
          sortable
        >
          <template #default="scope">
            {{ getSystemCategoryName(scope.row.system_category) }}
          </template>
        </el-table-column>

        <!-- 是否建立备份策略 -->
        <el-table-column
          prop="has_backup_strategy"
          label="是否建立备份策略"
          sortable
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.has_backup_strategy === '是' ? 'success' : 'danger'"
            >
              {{ scope.row.has_backup_strategy }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 数字化分类 -->
        <el-table-column
          prop="digital_classification"
          label="数字化分类"
          sortable
        ></el-table-column>

        <!-- JR/T 0059-2010备份能力标准 -->
        <el-table-column
          label="JR/T 0059-2010备份能力标准"
          sortable
        >
          <template #default="scope">
            {{ getBackupStandardName(scope.row.jrt_0059_backup_standard) }}
          </template>
        </el-table-column>

        <!-- 系统分类 -->
        <el-table-column
          prop="xinchuang_category_major"
          label="系统分类"
          sortable
        ></el-table-column>

        <!-- 信创小类 -->
        <el-table-column
          prop="xinchuang_category_minor"
          label="信创小类"
          sortable
        ></el-table-column>

        <!-- 软著名称 -->
        <el-table-column
          prop="software_copyright_name"
          label="软著名称"
          sortable
        ></el-table-column>

        <!-- 建设方式 -->
        <el-table-column
          prop="construction_method"
          label="建设方式"
          sortable
        ></el-table-column>

        <!-- 技术路线 -->
        <el-table-column
          prop="technical_route"
          label="技术路线"
          sortable
        ></el-table-column>

        <!-- 是否外部上报 -->
        <el-table-column
          prop="is_reported_to_external"
          label="是否外部上报"
          sortable
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.is_reported_to_external === '是' ? 'success' : 'danger'"
            >
              {{ scope.row.is_reported_to_external }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- CentOS7数量 -->
        <el-table-column
          prop="centos7_count"
          label="CentOS7数量"
          sortable
        ></el-table-column>

        <!-- 信创状态 -->
        <el-table-column
          prop="xinchuang_status"
          label="信创状态"
          sortable
        ></el-table-column>

        <!-- 等保等级 -->
        <el-table-column
          prop="security_level"
          label="等保等级"
          sortable
        ></el-table-column>

        <!-- 通用功能域 -->
        <el-table-column
          label="通用功能域"
          sortable
          width="200"
        >
          <template #default="scope">
            <div v-if="scope.row.general_function_domains">
              <el-popover
                placement="right"
                trigger="click"
                :width="400"
                popper-class="domain-popover"
              >
                <template #reference>
                  <div class="domain-preview" @click="handleDomainClick">
                    {{ formatFunctionDomainNames(scope.row.general_function_domains, 'generalFunctionDomains', true) }}
                    <span v-if="countCoreDomains(scope.row.general_function_domains, 'generalFunctionDomains') > 0"
                          class="core-count">
                      (核心:{{ countCoreDomains(scope.row.general_function_domains, 'generalFunctionDomains') }})
                    </span>
                  </div>
                </template>
                <div class="domain-detail">
                  <h4>通用功能域详情</h4>
                  <div class="domain-list">
                    <div v-for="(domain, index) in parseDomainDetails(scope.row.general_function_domains, 'generalFunctionDomains')"
                         :key="index"
                         :class="{'domain-item': true, 'core-domain': domain.isCore}">
                      {{ domain.name }}
                      <span v-if="domain.isCore" class="core-tag">核心</span>
                    </div>
                  </div>
                  <div class="domain-summary">
                    总计: {{ getDomainCount(scope.row.general_function_domains) }} 项
                    <span v-if="countCoreDomains(scope.row.general_function_domains, 'generalFunctionDomains') > 0">
                      (其中核心功能域: {{ countCoreDomains(scope.row.general_function_domains, 'generalFunctionDomains') }} 项)
                    </span>
                  </div>
                </div>
              </el-popover>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 经营机构功能域 -->
        <el-table-column
          label="经营机构功能域"
          sortable
          width="200"
        >
          <template #default="scope">
            <div v-if="scope.row.futures_function_domains">
              <el-popover
                placement="right"
                trigger="click"
                :width="400"
                popper-class="domain-popover"
              >
                <template #reference>
                  <div class="domain-preview" @click="handleDomainClick">
                    {{ formatFunctionDomainNames(scope.row.futures_function_domains, 'futuresFunctionDomains', true) }}
                    <span v-if="countCoreDomains(scope.row.futures_function_domains, 'futuresFunctionDomains') > 0"
                          class="core-count">
                      (核心:{{ countCoreDomains(scope.row.futures_function_domains, 'futuresFunctionDomains') }})
                    </span>
                  </div>
                </template>
                <div class="domain-detail">
                  <h4>经营机构功能域详情</h4>
                  <div class="domain-list">
                    <div v-for="(domain, index) in parseDomainDetails(scope.row.futures_function_domains, 'futuresFunctionDomains')"
                         :key="index"
                         :class="{'domain-item': true, 'core-domain': domain.isCore}">
                      {{ domain.name }}
                      <span v-if="domain.isCore" class="core-tag">核心</span>
                    </div>
                  </div>
                  <div class="domain-summary">
                    总计: {{ getDomainCount(scope.row.futures_function_domains) }} 项
                    <span v-if="countCoreDomains(scope.row.futures_function_domains, 'futuresFunctionDomains') > 0">
                      (其中核心功能域: {{ countCoreDomains(scope.row.futures_function_domains, 'futuresFunctionDomains') }} 项)
                    </span>
                  </div>
                </div>
              </el-popover>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column
          prop="remarks"
          label="备注"
          sortable
        ></el-table-column>

        <!-- 创建时间 -->
        <el-table-column
          prop="created_at"
          label="创建时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="created_by"
          label="创建人"
          sortable
        ></el-table-column>
        <el-table-column
          prop="updated_at"
          label="更新时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="updated_by"
          label="更新人"
          sortable
        ></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap">
              <el-button
                size="small"
                type="warning"
                :disabled="!hasUpdatePermission"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                size="small"
                type="danger"
                :disabled="!hasDeletePermission"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          background
          :current-page="search.currentPage"
          :page-size="search.pageSize"
          :total="search.total"
          :page-sizes="[10, 20, 50, 100, 1000, 10000]"
          :pager-count="5"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { Plus, Search, Download } from "@element-plus/icons-vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export default {
  components: {
    Plus,
    Search,
    Download,
  },

  data() {
    return {
      userArr: [], // 监控IP列表
      loading: false, // 加载状态
      userList: [], // 用户列表
      systemlevels: [], // 数据字典值
      xinchuangcategoryminors: [], //数据字典值
      xinchuangcategorymajors: [], //数据字典值
      systemattributes: [], //数据字典值
      constructionMethods: [], //数据字典值
      technicalRoutes: [], //数据字典值
      backupStandards: [], //数据字典值 - JR/T 0059-2010备份能力标准
      businessLines: [], //数据字典值 - 团队线条
      systemCategories: [], //数据字典值 - 业务系统大类
      operationStatuses: [], //数据字典值 - 运行状态
      hasDeletePermission: localStorage.getItem("role_code")?.includes("D"), // 是否有删除权限
      hasUpdatePermission: localStorage.getItem("role_code")?.includes("U"), // 是否有删除权限
      hasInsertPermission: localStorage.getItem("role_code")?.includes("I"), // 是否有删除权限

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false,
        delete: false,
      },
      // 查询数据
      search: {
        self_build_system_id: "",
        system_abbreviation: "",
        main_admin: "",
        system_attribute: "",
        system_level: "",
        jrt_0059_backup_standard: "",
        xinchuang_category_major: "",
        xinchuang_category_minor: "",
        construction_method: "",
        technical_route: "",
        is_reported_to_external: "",
        operation_status: "",
        xinchuang_status: "",
        security_level: "",
        general_function_domain: "",
        futures_function_domain: "",
        business_line: "",
        system_category: "",

        total: 0, // 总记录数
        pageSize: 10, // 每页显示条目数
        currentPage: 1, // 当前页码
        sortProp: "updated_at", // 排序字段
        sortOrder: "desc", // 排序顺序
      },

      // 表单数据
      formData: {
        id: null,
        self_build_system_id: "",
        system_abbreviation: "",
        main_admin: "",
        backup_admin: "",
        business_department: "",
        system_attribute: "",
        go_live_date: "",
        decommission_date: "",
        major_milestones: "",
        industry_name: "",
        monitoring_system_name: "",
        system_function_summary: "",
        system_form: "",
        cs_client_name: "",
        bs_url: "",
        ip_port: "",
        business_line: "",
        system_category: "",
        system_level: "",
        has_backup_strategy: "",
        server_count: 0,
        remarks: "",
        monitoring_coverage: "",
        digital_classification: "",
        jrt_0059_backup_standard: "",
        xinchuang_category_major: "",
        xinchuang_category_minor: "",
        software_copyright_name: "",
        construction_method: "",
        technical_route: "",
        operation_status: "",
        xinchuang_status: "",
        security_level: "",
        general_function_domains: [],
        futures_function_domains: [],
        is_reported_to_external: "",
        centos7_count: 0,
        created_at: "",
      },

      // 功能域选项
      generalFunctionDomains: [],
      futuresFunctionDomains: [],

      // 表单验证规则
      rules: {
        system_abbreviation: [
          { required: true, message: '请输入业务系统简称', trigger: 'blur' }
        ],
        main_admin: [
          { required: true, message: '请选择主岗', trigger: 'change' }
        ],
        operation_status: [
          { required: true, message: '请选择运行状态', trigger: 'change' }
        ],
        system_level: [
          { required: true, message: '请选择系统分级', trigger: 'change' }
        ],
        system_attribute: [
          { required: true, message: '请选择系统属性', trigger: 'change' }
        ],
        industry_name: [
          { required: true, message: '请输入业务系统行业名称', trigger: 'blur' }
        ],
        system_function_summary: [
          { required: true, message: '请输入系统功能描述', trigger: 'blur' }
        ],
        business_line: [
          { required: true, message: '请输入团队线条', trigger: 'blur' }
        ],
        system_category: [
          { required: true, message: '请输入业务系统大类', trigger: 'blur' }
        ],
        major_milestones: [
          { required: true, message: '请输入重大历程', trigger: 'blur' }
        ]
      },
    };
  },
  mounted() {
    this.loadData();

    this.getDatadict("C", "systemattributes");
    this.getDatadict("I", "systemlevels");
    this.getDatadict("J", "xinchuangcategoryminors");
    this.getDatadict("M", "xinchuangcategorymajors");
    this.getDatadict("N", "constructionMethods");
    this.getDatadict("O", "technicalRoutes");
    this.getDatadict("Q", "backupStandards");
    this.getDatadict("R", "businessLines");
    this.getDatadict("S", "systemCategories");
    this.getDatadict("H", "operationStatuses");

    // 获取功能域数据
    this.getFunctionDomains("通用功能域", "generalFunctionDomains");
    this.getFunctionDomains("期货经营机构", "futuresFunctionDomains");

    // 获取用户列表
    this.getUserList();
  },
  methods: {
    // 页码选择
    handlePageChange(newPage) {
      this.search.currentPage = newPage;
      this.loadData();
    },

    // 每页显示条目数变化
    handlePageSizeChange(newSize) {
      this.search.pageSize = parseInt(newSize);
      this.search.currentPage = 1; // 重置当前页码为第一页
      this.loadData();
    },

    //增加排序
    handleSortChange({ prop, order }) {
      this.search.sortProp = prop;
      this.search.sortOrder = order === "ascending" ? "asc" : "desc";
      this.loadData();
    },

    // 重置搜索条件
    resetSearch() {
      this.search = {
        self_build_system_id: "",
        system_abbreviation: "",
        main_admin: "",
        system_attribute: "",
        system_level: "",
        jrt_0059_backup_standard: "",
        xinchuang_category_major: "",
        xinchuang_category_minor: "",
        construction_method: "",
        technical_route: "",
        is_reported_to_external: "",
        operation_status: "",
        xinchuang_status: "",
        security_level: "",
        general_function_domain: "",
        futures_function_domain: "",
        business_line: "",
        system_category: "",
        total: 0,
        pageSize: 10,
        currentPage: 1,
        sortProp: "updated_at",
        sortOrder: "desc",
      };
      this.loadData();
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        const response = await this.$axios.post(
          `/api/get_cmdb_system_admin_responsibility_company`,
          this.search
        );
        this.userArr = response.data.msg;
        this.search.total = response.data.total;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading = false;
      }
    },

    // 验证并添加系统信息
    async validateAndSubmitAdd() {
      try {
        // 表单验证
        await this.$refs.addFormRef.validate();

        // 验证通过，提交表单
        await this.submitAdd();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 添加系统信息
    async submitAdd() {
      try {
        // 创建一个新对象，避免修改原始formData
        const formDataToSubmit = { ...this.formData };

        // 添加当前用户名用于创建记录
        formDataToSubmit.username = localStorage.getItem('loginUsername') || 'admin';

        // 将功能域数组转换为JSON字符串
        if (Array.isArray(formDataToSubmit.general_function_domains)) {
          formDataToSubmit.general_function_domains = JSON.stringify(formDataToSubmit.general_function_domains);
        }

        if (Array.isArray(formDataToSubmit.futures_function_domains)) {
          formDataToSubmit.futures_function_domains = JSON.stringify(formDataToSubmit.futures_function_domains);
        }

        await this.$axios.post(
          `/api/add_cmdb_system_admin_responsibility_company`,
          formDataToSubmit
        );
        this.$message.success("添加成功");
        this.dialogVisible.add = false;
        this.loadData();
      } catch (error) {
        console.error("添加失败:", error);
        this.$message.error("添加失败");
      }
    },

    // 验证并编辑系统信息
    async validateAndSubmitEdit() {
      try {
        // 表单验证
        await this.$refs.editFormRef.validate();

        // 验证通过，提交表单
        await this.submitEdit();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 编辑系统信息
    async submitEdit() {
      try {
        // 创建一个新对象，避免修改原始formData
        const formDataToSubmit = { ...this.formData };

        // 添加当前用户名用于更新记录
        formDataToSubmit.username = localStorage.getItem('loginUsername') || 'admin';

        // 将功能域数组转换为JSON字符串
        if (Array.isArray(formDataToSubmit.general_function_domains)) {
          formDataToSubmit.general_function_domains = JSON.stringify(formDataToSubmit.general_function_domains);
        }

        if (Array.isArray(formDataToSubmit.futures_function_domains)) {
          formDataToSubmit.futures_function_domains = JSON.stringify(formDataToSubmit.futures_function_domains);
        }

        await this.$axios.post(
          `/api/update_cmdb_system_admin_responsibility_company`,
          formDataToSubmit
        );
        this.$message.success("更新成功");
        this.dialogVisible.edit = false;
        this.loadData();
      } catch (error) {
        console.error("更新失败:", error);
        this.$message.error("更新失败");
      }
    },

    // 删除监控IP
    async submitDelete() {
      try {
        await this.$axios.post(
          `/api/del_cmdb_system_admin_responsibility_company`,
          this.formData
        );
        this.$message.success("删除成功");
        this.loadData();
        this.dialogVisible.delete = false;
      } catch (error) {
        console.error("删除失败:", error);
        this.$message.error("删除失败");
      }
    },

    // 得到数据字典
    async getDatadict(dictCode, targetArray) {
      try {
        const response = await this.$axios.post(
          `/api/get_cmdb_data_dictionary`,
          {
            dict_code: dictCode,
          }
        );
        this[targetArray] = response.data.msg;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      }
    },

    // 获取功能域数据
    async getFunctionDomains(domainType, targetArray) {
      try {
        const response = await this.$axios.post(
          `/api/get_cmdb_function_domains`,
          {
            function_domain_type: domainType,
          }
        );

        // 将返回的数据转换为选项格式
        this[targetArray] = response.data.msg.map(item => ({
          value: item.function_domain_code,
          label: item.function_domain_name,
          isCore: item.is_core_domain
        }));
      } catch (error) {
        console.error("功能域数据加载失败:", error);
        this.$message.error("功能域数据加载失败");
      }
    },

    // 获取用户列表
    async getUserList() {
      try {
        const response = await this.$axios.post(
          `/api/get_all_users_real_name`
        );
        this.userList = response.data.msg;
      } catch (error) {
        console.error("获取用户列表失败:", error);
        this.$message.error("获取用户列表失败");
      }
    },

    // 根据用户名获取真实姓名
    getUserRealName(username) {
      if (!username) return '-';
      const user = this.userList.find(user => user.username === username);
      return user ? user.real_name : username;
    },

    // 根据备份能力标准代码获取名称
    getBackupStandardName(code) {
      if (!code) return '-';
      const standard = this.backupStandards.find(item => item.dict_code === code);
      return standard ? standard.dict_name : code;
    },

    // 根据团队线条代码获取名称
    getBusinessLineName(code) {
      if (!code) return '-';
      const businessLine = this.businessLines.find(item => item.dict_code === code);
      return businessLine ? businessLine.dict_name : code;
    },

    // 根据业务系统大类代码获取名称
    getSystemCategoryName(code) {
      if (!code) return '-';
      const systemCategory = this.systemCategories.find(item => item.dict_code === code);
      return systemCategory ? systemCategory.dict_name : code;
    },

    // 根据运行状态代码获取名称
    getOperationStatusName(code) {
      if (!code) return '-';
      const operationStatus = this.operationStatuses.find(item => item.dict_code === code);
      return operationStatus ? operationStatus.dict_name : code;
    },

    // 根据运行状态代码获取颜色类型
    getOperationStatusColor(code) {
      if (!code) return 'info';
      switch (code) {
        case 'H00001':
          return 'success'; // 绿色
        case 'H00002':
          return 'danger'; // 红色
        case 'H00003':
          return 'warning'; // 橙色
        default:
          return 'info';
      }
    },

    // 格式化功能域名称
    formatFunctionDomainNames(domainsStr, arrayName, truncate = false) {
      try {
        if (!domainsStr) return '-';

        // 尝试解析JSON字符串为数组
        let domains = [];

        if (typeof domainsStr === 'string') {
          // 处理可能的格式问题
          try {
            // 尝试直接解析
            domains = JSON.parse(domainsStr);
          } catch (parseError) {
            console.warn("JSON解析失败，尝试修复格式:", parseError);

            // 尝试修复常见的格式问题
            // 1. 如果是以 [ 开头，以 ] 结尾，可能是数组格式
            if (domainsStr.trim().startsWith('[') && domainsStr.trim().endsWith(']')) {
              try {
                // 尝试清理并重新解析
                const cleanedStr = domainsStr.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                domains = JSON.parse(cleanedStr);
              } catch (e) {
                // 如果仍然失败，尝试简单的分割方法
                domains = domainsStr.replace(/[\[\]'"\s]/g, '').split(',').filter(Boolean);
              }
            } else {
              // 2. 如果不是数组格式，可能是逗号分隔的字符串
              domains = domainsStr.split(',').map(item => item.trim()).filter(Boolean);
            }
          }
        } else if (Array.isArray(domainsStr)) {
          domains = domainsStr;
        }

        // 确保domains是数组
        if (!Array.isArray(domains)) {
          domains = [domains];
        }

        if (domains.length === 0) return '-';

        // 获取对应的功能域数组
        const domainOptions = this[arrayName] || [];

        // 将编码转换为名称
        const domainNames = domains.map(code => {
          // 确保code是字符串
          const codeStr = String(code).trim();
          const domain = domainOptions.find(d => d.value === codeStr);
          return domain ? (domain.isCore ? `${domain.label}(核心)` : domain.label) : codeStr;
        });

        // 如果需要截断，只显示前两个，其余用"等X项"表示
        if (truncate && domainNames.length > 2) {
          return `${domainNames.slice(0, 2).join('、')} 等${domainNames.length}项`;
        }

        return domainNames.join('、');
      } catch (error) {
        console.error("格式化功能域名称失败:", error);
        // 返回一个友好的错误提示，而不是原始字符串
        return '格式错误';
      }
    },

    // 解析功能域详情
    parseDomainDetails(domainsStr, arrayName) {
      try {
        if (!domainsStr) return [];

        // 解析域数组
        let domains = [];

        if (typeof domainsStr === 'string') {
          try {
            domains = JSON.parse(domainsStr);
          } catch (e) {
            // 尝试修复格式
            if (domainsStr.trim().startsWith('[') && domainsStr.trim().endsWith(']')) {
              try {
                const cleanedStr = domainsStr.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                domains = JSON.parse(cleanedStr);
              } catch (e2) {
                domains = domainsStr.replace(/[\[\]'"\s]/g, '').split(',').filter(Boolean);
              }
            } else {
              domains = domainsStr.split(',').map(item => item.trim()).filter(Boolean);
            }
          }
        } else if (Array.isArray(domainsStr)) {
          domains = domainsStr;
        }

        // 确保domains是数组
        if (!Array.isArray(domains)) {
          domains = [domains];
        }

        if (domains.length === 0) return [];

        // 获取对应的功能域数组
        const domainOptions = this[arrayName] || [];

        // 将编码转换为详细信息
        return domains.map(code => {
          const codeStr = String(code).trim();
          const domain = domainOptions.find(d => d.value === codeStr);

          if (domain) {
            return {
              code: codeStr,
              name: domain.label,
              isCore: domain.isCore
            };
          } else {
            return {
              code: codeStr,
              name: codeStr,
              isCore: false
            };
          }
        });
      } catch (error) {
        console.error("解析功能域详情失败:", error);
        return [];
      }
    },

    // 获取功能域总数
    getDomainCount(domainsStr) {
      try {
        if (!domainsStr) return 0;

        let domains = [];

        if (typeof domainsStr === 'string') {
          try {
            domains = JSON.parse(domainsStr);
          } catch (e) {
            // 尝试修复格式
            if (domainsStr.trim().startsWith('[') && domainsStr.trim().endsWith(']')) {
              try {
                const cleanedStr = domainsStr.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                domains = JSON.parse(cleanedStr);
              } catch (e2) {
                domains = domainsStr.replace(/[\[\]'"\s]/g, '').split(',').filter(Boolean);
              }
            } else {
              domains = domainsStr.split(',').map(item => item.trim()).filter(Boolean);
            }
          }
        } else if (Array.isArray(domainsStr)) {
          domains = domainsStr;
        }

        // 确保domains是数组
        if (!Array.isArray(domains)) {
          domains = [domains];
        }

        return domains.length;
      } catch (error) {
        console.error("获取功能域总数失败:", error);
        return 0;
      }
    },

    // 统计核心功能域数量
    countCoreDomains(domainsStr, arrayName) {
      try {
        if (!domainsStr) return 0;

        // 解析域数组
        let domains = [];

        if (typeof domainsStr === 'string') {
          try {
            domains = JSON.parse(domainsStr);
          } catch (e) {
            // 尝试修复格式
            if (domainsStr.trim().startsWith('[') && domainsStr.trim().endsWith(']')) {
              try {
                const cleanedStr = domainsStr.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                domains = JSON.parse(cleanedStr);
              } catch (e2) {
                domains = domainsStr.replace(/[\[\]'"\s]/g, '').split(',').filter(Boolean);
              }
            } else {
              domains = domainsStr.split(',').map(item => item.trim()).filter(Boolean);
            }
          }
        } else if (Array.isArray(domainsStr)) {
          domains = domainsStr;
        }

        // 确保domains是数组
        if (!Array.isArray(domains)) {
          domains = [domains];
        }

        if (domains.length === 0) return 0;

        // 获取对应的功能域数组
        const domainOptions = this[arrayName] || [];

        // 计算核心功能域数量
        return domains.reduce((count, code) => {
          const codeStr = String(code).trim();
          const domain = domainOptions.find(d => d.value === codeStr);
          return domain && domain.isCore ? count + 1 : count;
        }, 0);
      } catch (error) {
        console.error("统计核心功能域数量失败:", error);
        return 0;
      }
    },

    // 处理功能域点击事件
    handleDomainClick(event) {
      // 阻止事件冒泡，避免触发表格行的点击事件
      event.stopPropagation();
    },

    // 新增效果实现
    handleAdd() {
      this.dialogVisible.add = !this.dialogVisible.add;
      this.formData = {};
    },

    // 编辑按钮实现
    handleEdit(_, row) {
      this.dialogVisible.edit = true;
      this.formData.id = row.id;
      this.formData.self_build_system_id = row.self_build_system_id;
      this.formData.system_abbreviation = row.system_abbreviation;
      this.formData.main_admin = row.main_admin;
      this.formData.backup_admin = row.backup_admin;
      this.formData.business_department = row.business_department;
      // 系统属性：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.system_attribute = row.system_attribute_code || row.system_attribute;
      this.formData.go_live_date = row.go_live_date;
      this.formData.decommission_date = row.decommission_date;
      this.formData.major_milestones = row.major_milestones;
      this.formData.industry_name = row.industry_name;
      this.formData.monitoring_system_name = row.monitoring_system_name;
      this.formData.system_function_summary = row.system_function_summary;
      this.formData.system_form = row.system_form;
      this.formData.cs_client_name = row.cs_client_name;
      this.formData.bs_url = row.bs_url;
      this.formData.ip_port = row.ip_port;
      // 团队线条：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.business_line = row.business_line_code || row.business_line;
      // 业务系统大类：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.system_category = row.system_category_code || row.system_category;
      // 系统分级：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.system_level = row.system_level_code || row.system_level;
      this.formData.has_backup_strategy = row.has_backup_strategy;
      this.formData.server_count = row.server_count;
      this.formData.remarks = row.remarks;
      this.formData.monitoring_coverage = row.monitoring_coverage;
      this.formData.digital_classification = row.digital_classification;
      this.formData.jrt_0059_backup_standard = row.jrt_0059_backup_standard;
      this.formData.xinchuang_category_major = row.xinchuang_category_major;
      this.formData.xinchuang_category_minor = row.xinchuang_category_minor;
      this.formData.software_copyright_name = row.software_copyright_name;
      this.formData.construction_method = row.construction_method;
      this.formData.technical_route = row.technical_route;
      // 运行状态：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.operation_status = row.operation_status_code || row.operation_status;
      this.formData.xinchuang_status = row.xinchuang_status;
      this.formData.security_level = row.security_level;
      // 处理通用功能域
      try {
        if (row.general_function_domains) {
          if (typeof row.general_function_domains === 'string') {
            try {
              this.formData.general_function_domains = JSON.parse(row.general_function_domains);
            } catch (e) {
              console.warn("解析通用功能域失败，尝试修复格式:", e);
              // 尝试修复格式
              if (row.general_function_domains.trim().startsWith('[') && row.general_function_domains.trim().endsWith(']')) {
                try {
                  const cleanedStr = row.general_function_domains.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                  this.formData.general_function_domains = JSON.parse(cleanedStr);
                } catch (e2) {
                  // 如果仍然失败，尝试简单的分割方法
                  this.formData.general_function_domains = row.general_function_domains
                    .replace(/[\[\]'"\s]/g, '')
                    .split(',')
                    .filter(Boolean);
                }
              } else {
                // 如果不是数组格式，可能是逗号分隔的字符串
                this.formData.general_function_domains = row.general_function_domains
                  .split(',')
                  .map(item => item.trim())
                  .filter(Boolean);
              }
            }
          } else if (Array.isArray(row.general_function_domains)) {
            this.formData.general_function_domains = row.general_function_domains;
          } else {
            this.formData.general_function_domains = [];
          }
        } else {
          this.formData.general_function_domains = [];
        }
      } catch (error) {
        console.error("处理通用功能域失败:", error);
        this.formData.general_function_domains = [];
      }

      // 处理经营机构功能域
      try {
        if (row.futures_function_domains) {
          if (typeof row.futures_function_domains === 'string') {
            try {
              this.formData.futures_function_domains = JSON.parse(row.futures_function_domains);
            } catch (e) {
              console.warn("解析经营机构功能域失败，尝试修复格式:", e);
              // 尝试修复格式
              if (row.futures_function_domains.trim().startsWith('[') && row.futures_function_domains.trim().endsWith(']')) {
                try {
                  const cleanedStr = row.futures_function_domains.replace(/'/g, '"').replace(/(\w+):/g, '"$1":');
                  this.formData.futures_function_domains = JSON.parse(cleanedStr);
                } catch (e2) {
                  // 如果仍然失败，尝试简单的分割方法
                  this.formData.futures_function_domains = row.futures_function_domains
                    .replace(/[\[\]'"\s]/g, '')
                    .split(',')
                    .filter(Boolean);
                }
              } else {
                // 如果不是数组格式，可能是逗号分隔的字符串
                this.formData.futures_function_domains = row.futures_function_domains
                  .split(',')
                  .map(item => item.trim())
                  .filter(Boolean);
              }
            }
          } else if (Array.isArray(row.futures_function_domains)) {
            this.formData.futures_function_domains = row.futures_function_domains;
          } else {
            this.formData.futures_function_domains = [];
          }
        } else {
          this.formData.futures_function_domains = [];
        }
      } catch (error) {
        console.error("处理经营机构功能域失败:", error);
        this.formData.futures_function_domains = [];
      }
      this.formData.is_reported_to_external = row.is_reported_to_external;
      this.formData.centos7_count = row.centos7_count;
      this.formData.created_at = row.created_at;
    },

    // 删除效果实现
    handleDelete(_, row) {
      this.dialogVisible.delete = !this.dialogVisible.delete;
      this.formData.id = row.id;
      this.formData.self_build_system_id = row.self_build_system_id;
    },
    //导出数据
    async exportData() {
      try {
        this.loading = true;
        
        // 创建导出请求参数，使用当前搜索条件但不限制分页
        const exportParams = {
          ...this.search,
          currentPage: 1,
          pageSize: 999999 // 设置一个很大的数字来获取所有数据
        };

        // 获取所有数据用于导出
        const response = await this.$axios.post(
          `/api/get_cmdb_system_admin_responsibility_company`,
          exportParams
        );

        if (response.data.code === 0) {
          const allData = response.data.msg;
          
          // 定义导出的表头和对应的数据处理方法
          const exportColumns = [
            { label: '自建系统编号', key: 'self_build_system_id' },
            { label: '业务系统简称', key: 'system_abbreviation' },
            { label: '主岗', key: 'main_admin', formatter: (value) => this.getUserRealName(value) },
            { label: '备岗', key: 'backup_admin', formatter: (value) => this.getUserRealName(value) },
            { label: '运行状态', key: 'operation_status', formatter: (value) => this.getOperationStatusName(value) },
            { label: '系统分级', key: 'system_level' },
            { label: '对应服务器数量', key: 'server_count' },
            { label: '监控覆盖率', key: 'monitoring_coverage' },
            { label: '业务主管部门', key: 'business_department' },
            { label: '系统属性', key: 'system_attribute' },
            { label: '上线时间', key: 'go_live_date' },
            { label: '下线时间', key: 'decommission_date' },
            { label: '重大历程', key: 'major_milestones' },
            { label: '业务系统行业名称', key: 'industry_name' },
            { label: '业务系统英文简称', key: 'monitoring_system_name' },
            { label: '系统功能简述', key: 'system_function_summary' },
            { label: '系统形态', key: 'system_form' },
            { label: 'CS客户端程序名称', key: 'cs_client_name' },
            { label: 'BS URL地址', key: 'bs_url' },
            { label: 'IP:端口', key: 'ip_port' },
            { label: '团队线条', key: 'business_line', formatter: (value) => this.getBusinessLineName(value) },
            { label: '业务系统大类', key: 'system_category', formatter: (value) => this.getSystemCategoryName(value) },
            { label: '是否建立备份策略', key: 'has_backup_strategy' },
            { label: '数字化分类', key: 'digital_classification' },
            { label: 'JR/T 0059-2010备份能力标准', key: 'jrt_0059_backup_standard', formatter: (value) => this.getBackupStandardName(value) },
            { label: '系统分类', key: 'xinchuang_category_major' },
            { label: '信创小类', key: 'xinchuang_category_minor' },
            { label: '软著名称', key: 'software_copyright_name' },
            { label: '建设方式', key: 'construction_method' },
            { label: '技术路线', key: 'technical_route' },
            { label: '是否外部上报', key: 'is_reported_to_external' },
            { label: 'CentOS7数量', key: 'centos7_count' },
            { label: '信创状态', key: 'xinchuang_status' },
            { label: '等保等级', key: 'security_level' },
            { label: '通用功能域', key: 'general_function_domains', formatter: (value) => this.formatFunctionDomainNames(value, 'generalFunctionDomains', false) },
            { label: '经营机构功能域', key: 'futures_function_domains', formatter: (value) => this.formatFunctionDomainNames(value, 'futuresFunctionDomains', false) },
            { label: '备注', key: 'remarks' }
          ];

          // 生成表头
          const headers = exportColumns.map(col => col.label);
          
          // 生成数据行
          const data = allData.map(row => 
            exportColumns.map(col => {
              const value = row[col.key];
              // 如果有格式化函数，使用格式化函数处理数据
              if (col.formatter && typeof col.formatter === 'function') {
                return col.formatter(value) || '-';
              }
              // 否则直接返回原值，空值显示为 '-'
              return value || '-';
            })
          );

          const wsData = [headers, ...data]; // 将表头和数据合并
          const ws = XLSX.utils.aoa_to_sheet(wsData);

          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

          const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
          const blob = new Blob([wbout], { type: "application/octet-stream" });
          saveAs(blob, `系统管理员责任表-公司管理_${new Date().toISOString().slice(0, 10)}.xlsx`);
          
          this.$message.success(`导出成功！共导出 ${allData.length} 条记录`);
        } else {
          this.$message.error('导出失败：' + response.data.msg);
        }
      } catch (error) {
        console.error('导出数据失败:', error);
        this.$message.error('导出失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
