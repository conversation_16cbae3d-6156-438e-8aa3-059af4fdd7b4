---
description: 
globs: *.sql
alwaysApply: false
---
# CMDB v2.0 数据库设计规则

## 数据库技术栈
- **数据库类型**: PostgreSQL
- **连接管理**: 通过[.env](mdc:backend/.env)配置
- **脚本管理**: [sql/](mdc:backend/sql)目录

## 命名规范

### 表命名规范
- **资产管理表**: `cmdb_表名`
  - 例：`cmdb_servers`、`cmdb_networks`、`cmdb_applications`
- **报表表**: `report_表名`
  - 例：`report_asset_summary`、`report_monthly_stats`
- **系统表**: `sys_表名`
  - 例：`sys_users`、`sys_roles`、`sys_permissions`

### 视图命名规范
- **资产管理视图**: `v_cmdb_表名`
  - 例：`v_cmdb_servers`、`v_cmdb_asset_details`
- **报表视图**: `v_report_表名`
  - 例：`v_report_asset_summary`、`v_report_usage_stats`

### 序列命名规范
- **序列命名**: `表名_id_seq`
  - 例：`cmdb_servers_id_seq`、`sys_users_id_seq`

### 索引命名规范
- **主键索引**: `pk_表名`
- **唯一索引**: `uk_表名_字段名`
- **普通索引**: `idx_表名_字段名`
- **复合索引**: `idx_表名_字段1_字段2`

## 字段设计规范

### 通用字段
每个业务表都应包含以下通用字段：
```sql
id SERIAL PRIMARY KEY,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
created_by VARCHAR(50) DEFAULT 'admin',
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_by VARCHAR(50) DEFAULT 'admin',
is_deleted BOOLEAN DEFAULT FALSE,
version INTEGER DEFAULT 1
```

### 数据类型选择
- **ID字段**: `SERIAL` 或 `BIGSERIAL`
- **字符串**: `VARCHAR(长度)` 或 `TEXT`
- **数字**: `INTEGER`、`BIGINT`、`DECIMAL(精度,小数位)`
- **日期**: `DATE` (格式: YYYY-MM-DD)
- **时间**: `TIMESTAMP` (格式: YYYY-MM-DD HH:MM:SS)
- **布尔**: `BOOLEAN`
- **JSON**: `JSONB`

### 外键设计
- 外键字段命名：`关联表名_id`
- 例：`server_id`、`user_id`、`department_id`
- 必须添加外键约束和索引

## 表设计规范

### 资产管理表结构示例
```sql
-- 服务器资产表
CREATE TABLE cmdb_servers (
    id SERIAL PRIMARY KEY,
    hostname VARCHAR(100) NOT NULL,
    ip_address INET,
    server_type VARCHAR(50),
    cpu_cores INTEGER,
    memory_gb INTEGER,
    disk_gb INTEGER,
    os_type VARCHAR(50),
    os_version VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    department_id INTEGER,
    owner_id INTEGER,
    location VARCHAR(200),
    purchase_date DATE,
    warranty_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'admin',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) DEFAULT 'admin',
    is_deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);
```

### 用户权限表结构示例
```sql
-- 用户表
CREATE TABLE sys_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    email VARCHAR(100),
    full_name VARCHAR(100),
    department VARCHAR(100),
    role_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 视图设计规范

### 资产详情视图示例
```sql
CREATE VIEW v_cmdb_server_details AS
SELECT 
    s.id,
    s.hostname,
    s.ip_address,
    s.server_type,
    s.cpu_cores,
    s.memory_gb,
    s.disk_gb,
    s.os_type,
    s.os_version,
    s.status,
    d.name as department_name,
    u.full_name as owner_name,
    s.location,
    s.purchase_date,
    s.warranty_date,
    TO_CHAR(s.created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at_formatted,
    s.created_by
FROM cmdb_servers s
LEFT JOIN sys_departments d ON s.department_id = d.id
LEFT JOIN sys_users u ON s.owner_id = u.id
WHERE s.is_deleted = FALSE;
```

## 数据库优化

### 索引策略
- 主键自动创建主键索引
- 外键字段创建索引
- 查询频繁的字段创建索引
- 复合查询创建复合索引

### 分区策略
- 大表按时间分区
- 历史数据按月分区
- 日志表按日分区

### 性能优化
- 定期更新统计信息
- 定期清理历史数据
- 监控慢查询
- 优化查询语句

## 数据备份与恢复

### 备份策略
- 每日全量备份
- 每小时增量备份
- 重要操作前手动备份
- 备份文件异地存储

### 恢复测试
- 定期恢复测试
- 文档化恢复流程
- 验证数据完整性

## 数据迁移

### 版本控制
- SQL脚本版本化
- 迁移脚本可回滚
- 迁移日志记录

### 迁移流程
1. 备份现有数据
2. 执行迁移脚本
3. 验证数据完整性
4. 更新应用配置
5. 测试应用功能

