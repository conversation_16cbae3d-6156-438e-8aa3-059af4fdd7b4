import { useState, useEffect } from 'react';
import { Monitor, Server, Database, Activity, Wifi, HardDrive } from 'lucide-react';
import { EnhancedCard, TechProgressBar, MonitoringCoverageCarousel, DeviceOnlineCarousel, AlertRankingCarousel, AlertWaterLevelChart } from './components';

// 覆盖率数据配置 - 支持18个指标
const coverageMetrics = [
  { name: '网络设备', value: 95.74, total: 235, covered: 225, icon: <Wifi className="w-5 h-5" /> },
  { name: '一级系统服务器', value: 98.74, total: 159, covered: 157, icon: <Server className="w-5 h-5" /> },
  { name: '二级系统服务器', value: 100.00, total: 128, covered: 128, icon: <Database className="w-5 h-5" /> },
  { name: '存储设备', value: 92.50, total: 80, covered: 74, icon: <HardDrive className="w-5 h-5" /> },
  { name: '安全设备', value: 89.30, total: 65, covered: 58, icon: <Monitor className="w-5 h-5" /> },
  { name: '负载均衡器', value: 96.20, total: 42, covered: 41, icon: <Activity className="w-5 h-5" /> },
  { name: '核心交换机', value: 100.00, total: 35, covered: 35, icon: <Wifi className="w-5 h-5" /> },
  { name: '汇聚交换机', value: 94.80, total: 58, covered: 55, icon: <Wifi className="w-5 h-5" /> },
  { name: '接入交换机', value: 91.20, total: 125, covered: 114, icon: <Wifi className="w-5 h-5" /> },
  { name: '防火墙', value: 100.00, total: 28, covered: 28, icon: <Monitor className="w-5 h-5" /> },
  { name: 'VPN网关', value: 87.50, total: 16, covered: 14, icon: <Monitor className="w-5 h-5" /> },
  { name: '应用服务器', value: 93.60, total: 89, covered: 83, icon: <Server className="w-5 h-5" /> },
  { name: '数据库服务器', value: 98.50, total: 67, covered: 66, icon: <Database className="w-5 h-5" /> },
  { name: '中间件服务器', value: 95.40, total: 43, covered: 41, icon: <Server className="w-5 h-5" /> },
  { name: '缓存服务器', value: 88.90, total: 27, covered: 24, icon: <Server className="w-5 h-5" /> },
  { name: '消息队列', value: 92.30, total: 13, covered: 12, icon: <Activity className="w-5 h-5" /> },
  { name: '监控探针', value: 97.80, total: 156, covered: 153, icon: <Monitor className="w-5 h-5" /> },
  { name: '日志收集器', value: 90.60, total: 32, covered: 29, icon: <Activity className="w-5 h-5" /> }
];

// 在线状态数据
const onlineMetrics = [
  { name: '网络设备', value: 100.00, total: 232, online: 232, icon: <Wifi className="w-5 h-5" /> },
  { name: '实体服务器', value: 100.00, total: 210, online: 210, icon: <Server className="w-5 h-5" /> },
  { name: '虚拟化服务器', value: 84.78, total: 361, online: 323, icon: <Database className="w-5 h-5" /> }
];

// 告警排名数据 - 按系统排名
const systemAlertRankings = [
  { systemName: '交易系统', adminName: '张三', alertCount: 149, trend: 'up' as const },
  { systemName: '风控系统', adminName: '李四', alertCount: 122, trend: 'down' as const },
  { systemName: '结算系统', adminName: '王五', alertCount: 117, trend: 'up' as const },
  { systemName: '监控系统', adminName: '赵六', alertCount: 114, trend: 'down' as const },
  { systemName: '报表系统', adminName: '钱七', alertCount: 110, trend: 'up' as const },
  { systemName: '备份系统', adminName: '孙八', alertCount: 102, trend: 'down' as const },
  { systemName: '日志系统', adminName: '周九', alertCount: 100, trend: 'up' as const },
  { systemName: '认证系统', adminName: '吴十', alertCount: 97, trend: 'down' as const },
  { systemName: '消息系统', adminName: '郑一', alertCount: 93, trend: 'up' as const },
  { systemName: '存储系统', adminName: '冯二', alertCount: 92, trend: 'down' as const }
];

// 告警排名数据 - 按管理员排名
const adminAlertRankings = [
  { systemName: '交易系统', adminName: '张三', alertCount: 149, trend: 'up' as const },
  { systemName: '报表系统', adminName: '钱七', alertCount: 135, trend: 'down' as const },
  { systemName: '风控系统', adminName: '李四', alertCount: 122, trend: 'up' as const },
  { systemName: '结算系统', adminName: '王五', alertCount: 117, trend: 'down' as const },
  { systemName: '监控系统', adminName: '赵六', alertCount: 114, trend: 'up' as const },
  { systemName: '备份系统', adminName: '孙八', alertCount: 102, trend: 'down' as const },
  { systemName: '日志系统', adminName: '周九', alertCount: 100, trend: 'up' as const },
  { systemName: '认证系统', adminName: '吴十', alertCount: 97, trend: 'down' as const },
  { systemName: '消息系统', adminName: '郑一', alertCount: 93, trend: 'up' as const },
  { systemName: '存储系统', adminName: '冯二', alertCount: 92, trend: 'down' as const }
];

function App() {
  const [currentTime, setCurrentTime] = useState(new Date());

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };


  return (
    <div className="min-h-screen bg-tech-gradient text-white overflow-hidden font-tech">
      {/* 动态科技感背景 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 动态网格背景 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'gridMove 20s linear infinite'
          }}></div>
        </div>

        {/* 浮动的几何图形 */}
        <div className="absolute inset-0 opacity-15">
          {/* 大型浮动圆环 */}
          <div className="absolute top-10 left-10 w-96 h-96 border-2 border-neon-blue/40 rounded-full floating pulse-glow"></div>
          <div className="absolute top-1/2 right-20 w-72 h-72 border-2 border-neon-cyan/40 rounded-full floating" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-20 left-1/3 w-48 h-48 border-2 border-neon-purple/40 rounded-full floating" style={{ animationDelay: '2s' }}></div>

          {/* 中型装饰元素 */}
          <div className="absolute top-1/4 left-1/2 w-32 h-32 border border-neon-green/30 rounded-full pulse-glow" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute bottom-1/4 right-1/4 w-24 h-24 border border-neon-orange/30 rounded-full floating" style={{ animationDelay: '1.5s' }}></div>

          {/* 六边形装饰 */}
          <div className="absolute top-1/3 right-1/3 w-20 h-20 border border-neon-blue/25 transform rotate-45 floating" style={{ animationDelay: '2.5s' }}></div>
          <div className="absolute bottom-1/3 left-1/4 w-16 h-16 border border-neon-cyan/25 transform rotate-12 floating" style={{ animationDelay: '3s' }}></div>
        </div>

        {/* 增强粒子效果 */}
        <div className="absolute inset-0 opacity-25">
          {/* 大粒子 */}
          <div className="absolute top-[12%] left-[18%] w-3 h-3 bg-neon-blue rounded-full particle-float" style={{ animationDelay: '0s' }}></div>
          <div className="absolute top-[28%] right-[22%] w-2.5 h-2.5 bg-neon-cyan rounded-full particle-float" style={{ animationDelay: '1.2s' }}></div>
          <div className="absolute top-[45%] left-[8%] w-2 h-2 bg-neon-green rounded-full particle-float" style={{ animationDelay: '2.4s' }}></div>
          <div className="absolute top-[62%] right-[12%] w-3 h-3 bg-neon-purple rounded-full particle-float" style={{ animationDelay: '3.6s' }}></div>
          <div className="absolute bottom-[18%] left-[25%] w-2.5 h-2.5 bg-neon-orange rounded-full particle-float" style={{ animationDelay: '4.8s' }}></div>
          
          {/* 中等粒子 */}
          <div className="absolute top-[35%] left-[65%] w-1.5 h-1.5 bg-neon-blue rounded-full particle-drift" style={{ animationDelay: '0.8s' }}></div>
          <div className="absolute top-[52%] left-[85%] w-1 h-1 bg-neon-cyan rounded-full particle-drift" style={{ animationDelay: '1.6s' }}></div>
          <div className="absolute top-[78%] left-[12%] w-1.5 h-1.5 bg-neon-green rounded-full particle-drift" style={{ animationDelay: '2.8s' }}></div>
          <div className="absolute bottom-[42%] left-[72%] w-1 h-1 bg-neon-purple rounded-full particle-drift" style={{ animationDelay: '3.2s' }}></div>
          <div className="absolute top-[8%] left-[42%] w-1.5 h-1.5 bg-neon-orange rounded-full particle-drift" style={{ animationDelay: '4.4s' }}></div>
          
          {/* 小粒子群 */}
          <div className="absolute top-[22%] left-[78%] w-1 h-1 bg-white rounded-full particle-twinkle" style={{ animationDelay: '0.4s' }}></div>
          <div className="absolute top-[38%] right-[8%] w-0.5 h-0.5 bg-neon-blue rounded-full particle-twinkle" style={{ animationDelay: '1.8s' }}></div>
          <div className="absolute top-[58%] left-[28%] w-1 h-1 bg-neon-cyan rounded-full particle-twinkle" style={{ animationDelay: '2.2s' }}></div>
          <div className="absolute bottom-[28%] right-[18%] w-0.5 h-0.5 bg-neon-green rounded-full particle-twinkle" style={{ animationDelay: '3.4s' }}></div>
          <div className="absolute bottom-[38%] left-[48%] w-1 h-1 bg-neon-purple rounded-full particle-twinkle" style={{ animationDelay: '4.6s' }}></div>
          <div className="absolute top-[68%] left-[88%] w-0.5 h-0.5 bg-neon-orange rounded-full particle-twinkle" style={{ animationDelay: '5.2s' }}></div>
        </div>

        {/* 数据流线条 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-blue/50 to-transparent"
            style={{ animation: 'dataFlow 6s ease-in-out infinite' }}></div>
          <div className="absolute top-2/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-cyan/50 to-transparent"
            style={{ animation: 'dataFlow 8s ease-in-out infinite', animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-green/50 to-transparent"
            style={{ animation: 'dataFlow 7s ease-in-out infinite', animationDelay: '4s' }}></div>
        </div>

        {/* 星光效果 */}
        <div className="absolute inset-0 opacity-30">
          {/* 大星光 */}
          <div className="absolute top-[15%] left-[20%] w-1 h-1 bg-white rounded-full starlight" style={{ animationDelay: '0s' }}></div>
          <div className="absolute top-[25%] right-[15%] w-1 h-1 bg-neon-blue rounded-full starlight" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-[45%] left-[10%] w-1 h-1 bg-neon-cyan rounded-full starlight" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-[65%] right-[25%] w-1 h-1 bg-white rounded-full starlight" style={{ animationDelay: '3s' }}></div>
          <div className="absolute bottom-[20%] left-[30%] w-1 h-1 bg-neon-blue rounded-full starlight" style={{ animationDelay: '4s' }}></div>
          <div className="absolute bottom-[35%] right-[40%] w-1 h-1 bg-neon-cyan rounded-full starlight" style={{ animationDelay: '5s' }}></div>

          {/* 中等星光 */}
          <div className="absolute top-[35%] left-[60%] w-0.5 h-0.5 bg-white rounded-full starlight-small" style={{ animationDelay: '1.5s' }}></div>
          <div className="absolute top-[55%] left-[80%] w-0.5 h-0.5 bg-neon-green rounded-full starlight-small" style={{ animationDelay: '2.5s' }}></div>
          <div className="absolute top-[75%] left-[15%] w-0.5 h-0.5 bg-neon-purple rounded-full starlight-small" style={{ animationDelay: '3.5s' }}></div>
          <div className="absolute bottom-[45%] left-[70%] w-0.5 h-0.5 bg-white rounded-full starlight-small" style={{ animationDelay: '4.5s' }}></div>
          <div className="absolute top-[10%] left-[45%] w-0.5 h-0.5 bg-neon-cyan rounded-full starlight-small" style={{ animationDelay: '0.5s' }}></div>

          {/* 小星光 */}
          <div className="absolute top-[20%] left-[75%] w-px h-px bg-white rounded-full starlight-tiny" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-[40%] right-[10%] w-px h-px bg-neon-blue rounded-full starlight-tiny" style={{ animationDelay: '3s' }}></div>
          <div className="absolute top-[60%] left-[25%] w-px h-px bg-neon-green rounded-full starlight-tiny" style={{ animationDelay: '4s' }}></div>
          <div className="absolute bottom-[25%] right-[20%] w-px h-px bg-white rounded-full starlight-tiny" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-[40%] left-[50%] w-px h-px bg-neon-cyan rounded-full starlight-tiny" style={{ animationDelay: '5s' }}></div>
          <div className="absolute top-[30%] left-[35%] w-px h-px bg-neon-purple rounded-full starlight-tiny" style={{ animationDelay: '6s' }}></div>
          <div className="absolute bottom-[15%] right-[60%] w-px h-px bg-white rounded-full starlight-tiny" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute top-[50%] right-[35%] w-px h-px bg-neon-orange rounded-full starlight-tiny" style={{ animationDelay: '3.5s' }}></div>
        </div>
      </div>

      <div className="relative z-10 p-4">
        {/* 标题栏 */}
        <header className="relative flex items-center justify-center mb-4">
          {/* 左侧Logo */}
          <div className="absolute left-0 flex items-center space-x-3">
            <div className="w-12 h-12 rounded-lg overflow-hidden neon-glow">
              <img
                src="/src/logo/logo.png"
                alt="Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <div>
              <p className="text-sm text-gray-400">长江期货</p>
            </div>
          </div>

          {/* 居中标题 */}
          <div className="text-center">
            <h1 className="text-3xl font-bold tech-gradient-text">
              IT资源管理系统驾驶舱
            </h1>
          </div>

          {/* 右侧系统信息 */}
          <div className="absolute right-0 flex items-center space-x-3">
            <div className="glass-card rounded-lg px-3 py-1">
              {/* 系统时间 */}
              <div className="text-lg font-mono text-neon-blue">
                {formatTime(currentTime)}
              </div>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-12 gap-4 h-[calc(100vh-120px)]">
          {/* 左侧列 */}
          <div className="col-span-3 flex flex-col space-y-4">
            {/* 监控覆盖率 - 增强轮播显示 */}
            <div className="h-[520px]">
              <MonitoringCoverageCarousel
                metrics={coverageMetrics}
                itemsPerPage={3}
                autoPlayInterval={5000}
                className="h-full"
              />
            </div>

            {/* 设备在线情况 */}
            <div className="h-[420px]">
              <DeviceOnlineCarousel
                metrics={onlineMetrics}
                itemsPerPage={3}
                autoPlayInterval={5000}
                className="h-full"
              />
            </div>
          </div>

          {/* 中央区域 */}
          <div className="col-span-6 flex flex-col space-y-4">
            {/* 设备管理总数和资源状态 */}
            <EnhancedCard className="h-[350px]" animationDelay={400}>
              <div className="text-center mb-4">
                <h2 className="text-lg text-gray-300 mb-2">设备管理总数</h2>
                <div className="text-5xl font-bold tech-gradient-text count-animation">
                  826
                </div>
              </div>

              <div className="flex justify-center items-center space-x-8">
                <div className="text-center glass-card rounded-lg p-4 hover-lift flex-1 max-w-[140px]">
                  <div className="w-14 h-14 bg-gradient-to-r from-neon-blue/20 to-neon-cyan/20 rounded-lg flex items-center justify-center mb-3 neon-glow mx-auto">
                    <Server className="w-5 h-5 text-neon-blue" />
                  </div>
                  <div className="text-lg font-bold text-neon-blue mb-1">23</div>
                  <div className="text-xs text-gray-400 leading-tight">待登记设备数</div>
                </div>

                <div className="text-center glass-card rounded-lg p-4 hover-lift flex-1 max-w-[140px]">
                  <div className="w-14 h-14 bg-gradient-to-r from-neon-cyan/20 to-neon-blue/20 rounded-lg flex items-center justify-center mb-3 neon-glow mx-auto">
                    <HardDrive className="w-5 h-5 text-neon-cyan" />
                  </div>
                  <div className="text-lg font-bold text-neon-cyan mb-1">15</div>
                  <div className="text-xs text-gray-400 leading-tight">待回收资源数</div>
                </div>

                <div className="text-center glass-card rounded-lg p-4 hover-lift flex-1 max-w-[140px]">
                  <div className="w-14 h-14 bg-gradient-to-r from-neon-green/20 to-neon-blue/20 rounded-lg flex items-center justify-center mb-3 neon-glow-green mx-auto">
                    <Database className="w-5 h-5 text-neon-green" />
                  </div>
                  <div className="text-lg font-bold text-neon-green mb-1">+47</div>
                  <div className="text-xs text-gray-400 leading-tight">虚拟化资源30日变化</div>
                </div>
              </div>
            </EnhancedCard>

            {/* 15日内告警排名 */}
            <div className="h-[590px]">
              <AlertRankingCarousel
                systemRankings={systemAlertRankings}
                adminRankings={adminAlertRankings}
                autoPlayInterval={8000}
                className="h-full"
              />
            </div>
          </div>

          {/* 右侧列 */}
          <div className="col-span-3 flex flex-col space-y-4">
            {/* CJM告警统计 */}
            <div className="h-[540px]">
              <EnhancedCard
                title="CJM告警统计"
                className="h-full flex flex-col"
                animationDelay={800}
              >
                <div className="flex justify-around mb-6">
                  <div className="text-center">
                    <TechProgressBar
                      value={75}
                      max={100}
                      label="今日告警数"
                      variant="circular"
                      size={60}
                      glowColor="#3b82f6"
                    />
                    <div className="text-lg font-bold text-neon-blue mt-2">6</div>
                  </div>
                  <div className="text-center">
                    <TechProgressBar
                      value={65}
                      max={100}
                      label="7日内告警数"
                      variant="circular"
                      size={60}
                      glowColor="#06b6d4"
                    />
                    <div className="text-lg font-bold text-neon-cyan mt-2">41</div>
                  </div>
                  <div className="text-center">
                    <TechProgressBar
                      value={85}
                      max={100}
                      label="30日内告警数"
                      variant="circular"
                      size={60}
                      glowColor="#10b981"
                    />
                    <div className="text-lg font-bold text-neon-green mt-2">757</div>
                  </div>
                </div>

                <div className="flex-1 flex flex-col">
                  <h4 className="text-sm text-gray-300 mb-3">30日内CJM告警级别统计</h4>
                  <div className="flex-1 flex flex-col items-center justify-center">
                    <div className="relative flex justify-center mb-4">
                      <svg width="140" height="140" viewBox="0 0 140 140" className="transform">
                        <defs>
                          {/* 渐变定义 */}
                          <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#10b981" />
                            <stop offset="100%" stopColor="#059669" />
                          </linearGradient>
                          <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#f59e0b" />
                            <stop offset="100%" stopColor="#d97706" />
                          </linearGradient>
                          <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#ef4444" />
                            <stop offset="100%" stopColor="#dc2626" />
                          </linearGradient>

                          {/* 发光滤镜 */}
                          <filter id="glowFilter">
                            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                            <feMerge>
                              <feMergeNode in="coloredBlur" />
                              <feMergeNode in="SourceGraphic" />
                            </feMerge>
                          </filter>
                        </defs>

                        {/* 背景圆环 */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="rgba(255, 255, 255, 0.1)"
                          strokeWidth="12"
                        />

                        {/* 一般告警 - 45.2% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#greenGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${45.2 * 2.827} ${(100 - 45.2) * 2.827}`}
                          strokeDashoffset="0"
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                        />

                        {/* 严重告警 - 37.9% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#orangeGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${37.9 * 2.827} ${(100 - 37.9) * 2.827}`}
                          strokeDashoffset={`-${45.2 * 2.827}`}
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                          style={{ animationDelay: '200ms' }}
                        />

                        {/* 紧急告警 - 16.9% */}
                        <circle
                          cx="70"
                          cy="70"
                          r="45"
                          fill="none"
                          stroke="url(#redGradient)"
                          strokeWidth="12"
                          strokeDasharray={`${16.9 * 2.827} ${(100 - 16.9) * 2.827}`}
                          strokeDashoffset={`-${(45.2 + 37.9) * 2.827}`}
                          transform="rotate(-90 70 70)"
                          filter="url(#glowFilter)"
                          className="transition-all duration-1000 ease-out entrance-animation"
                          strokeLinecap="round"
                          style={{ animationDelay: '400ms' }}
                        />

                        {/* 中心文字 */}
                        <text x="70" y="65" textAnchor="middle" className="text-lg font-bold fill-white count-animation">
                          757
                        </text>
                        <text x="70" y="80" textAnchor="middle" className="text-xs fill-gray-400">
                          总告警
                        </text>
                      </svg>
                    </div>
                    <div className="space-y-1 text-xs w-full">
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-green rounded mr-2"></div>
                          一般告警
                        </span>
                        <span>45.2%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-orange rounded mr-2"></div>
                          严重告警
                        </span>
                        <span>37.9%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-neon-red rounded mr-2"></div>
                          紧急告警
                        </span>
                        <span>16.9%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </EnhancedCard>
            </div>

            {/* 当前告警未处理数 */}
            <div className="h-[400px]">
              <AlertWaterLevelChart
                className="h-full"
                animationDelay={1000}
              />
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}

export default App;