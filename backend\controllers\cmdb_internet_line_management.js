/**
 * 互联网线路管理控制器
 */
const { connPG } = require('../db/pg');
const { validateInternetLineData } = require('../services/internet_line_validation');
const { 
  AppError, 
  ValidationError, 
  NotFoundError, 
  ConflictError,
  asyncHandler 
} = require('../middlewares/errorHandler');
const {
  validateRequired,
  validateLength,
  validateFormat,
  validateIP,
  validateCIDR,
  validateBandwidth,
  validateFee,
  validateDateRange,
  createValidator
} = require('../services/validation');
const XLSX = require('xlsx');

/**
 * 记录操作日志的辅助函数
 */
const recordOperationLog = async (req, operationType, description, additionalData = {}) => {
  try {
    const { method, originalUrl, body } = req;
    const username = req.user ? req.user.username : (body.loginUsername || 'anonymous');
    
    // 创建脱敏的请求体
    const sanitizedBody = { 
      ...body,
      description,
      ...additionalData
    };
    
    // 移除敏感信息
    if (sanitizedBody.loginUsername) {
      delete sanitizedBody.loginUsername;
    }

    // 记录日志到数据库
    const logQuery = `
      INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      ON CONFLICT DO NOTHING
    `;

    const logParams = [
      method,
      originalUrl,
      sanitizedBody,
      username,
      operationType
    ];

    await connPG.query(logQuery, logParams);
    

  } catch (error) {

    // 日志记录失败不影响主要业务功能
  }
};

// 获取线路列表
const getInternetLines = async (req, res) => {
  try {
    const {
      lineName,
      provider,
      lineType,
      datacenter,
      bandwidth,
      contractNumber,
      accessMethod,
      minMonthlyFee,
      maxMonthlyFee,
      contractStatus,
      keyword,
      currentPage = 1,
      pageSize = 10,
      sortProp = 'id',
      sortOrder = 'desc'
    } = req.body;

    // 构建查询条件
    const conditions = [];
    const params = [];
    let paramIndex = 1;

    // 线路名称模糊搜索
    if (lineName) {
      conditions.push(`line_name ILIKE $${paramIndex++}`);
      params.push(`%${lineName}%`);
    }

    // 运营商筛选
    if (provider) {
      conditions.push(`provider = $${paramIndex++}`);
      params.push(provider);
    }

    // 线路类型筛选
    if (lineType) {
      conditions.push(`line_type = $${paramIndex++}`);
      params.push(lineType);
    }

    // 机房筛选
    if (datacenter) {
      conditions.push(`datacenter ILIKE $${paramIndex++}`);
      params.push(`%${datacenter}%`);
    }

    // 带宽筛选
    if (bandwidth) {
      conditions.push(`bandwidth ILIKE $${paramIndex++}`);
      params.push(`%${bandwidth}%`);
    }

    // 合同编号筛选
    if (contractNumber) {
      conditions.push(`contract_number ILIKE $${paramIndex++}`);
      params.push(`%${contractNumber}%`);
    }

    // 接入方式筛选
    if (accessMethod) {
      conditions.push(`access_method ILIKE $${paramIndex++}`);
      params.push(`%${accessMethod}%`);
    }

    // 费用范围筛选
    if (minMonthlyFee !== undefined && minMonthlyFee !== null && minMonthlyFee !== '') {
      conditions.push(`monthly_fee >= $${paramIndex++}`);
      params.push(parseFloat(minMonthlyFee));
    }

    if (maxMonthlyFee !== undefined && maxMonthlyFee !== null && maxMonthlyFee !== '') {
      conditions.push(`monthly_fee <= $${paramIndex++}`);
      params.push(parseFloat(maxMonthlyFee));
    }

    // 合同状态筛选
    if (contractStatus) {
      const currentDate = new Date().toISOString().split('T')[0];
      const thirtyDaysLater = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      switch (contractStatus) {
        case 'active':
          conditions.push(`contract_end_date > $${paramIndex++}`);
          params.push(currentDate);
          break;
        case 'expired':
          conditions.push(`contract_end_date < $${paramIndex++}`);
          params.push(currentDate);
          break;
        case 'expiring':
          conditions.push(`contract_end_date BETWEEN $${paramIndex++} AND $${paramIndex++}`);
          params.push(currentDate);
          params.push(thirtyDaysLater);
          break;
      }
    }

    // 关键词搜索
    if (keyword) {
      conditions.push(`(
        line_name ILIKE $${paramIndex++} OR 
        contract_number ILIKE $${paramIndex++} OR 
        line_purpose ILIKE $${paramIndex++} OR 
        remarks ILIKE $${paramIndex++}
      )`);
      const keywordPattern = `%${keyword}%`;
      params.push(keywordPattern, keywordPattern, keywordPattern, keywordPattern);
    }

    // 构建WHERE子句
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')} AND del_flag = '0'` : `WHERE del_flag = '0'`;

    // 查询总记录数
    const countQuery = `
      SELECT COUNT(*) AS total
      FROM cmdb_internet_lines
      ${whereClause}
    `;

    const countResult = await connPG.query(countQuery, params);
    const total = parseInt(countResult.rows[0].total);

    // 计算分页参数
    const offset = (currentPage - 1) * pageSize;

    // 查询分页数据
    const dataQuery = `
      SELECT
        id,
        line_name,
        provider,
        line_type,
        bandwidth,
        ip_range,
        datacenter,
        access_method,
        line_purpose,
        contract_number,
        contract_start_date,
        contract_end_date,
        monthly_fee,
        remarks,
        created_at,
        created_by,
        updated_at,
        updated_by,
        CASE 
          WHEN contract_end_date IS NULL THEN '未设置'
          WHEN contract_end_date < CURRENT_DATE THEN '已过期'
          WHEN contract_end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN '即将过期'
          ELSE '有效'
        END as contract_status,
        CASE 
          WHEN contract_end_date IS NOT NULL THEN 
            (contract_end_date - CURRENT_DATE)
          ELSE NULL
        END as days_to_expiry
      FROM cmdb_internet_lines
      ${whereClause}
      ORDER BY ${sortProp} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    const dataParams = [...params, pageSize, offset];
    const dataResult = await connPG.query(dataQuery, dataParams);

    res.json({
      code: 0,
      msg: dataResult.rows,
      total
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 添加线路
const addInternetLine = asyncHandler(async (req, res) => {
  const {
    lineName,
    provider,
    lineType,
    bandwidth,
    ipRange,
    gatewayIp,
    firewallIp,
    datacenter,
    accessMethod,
    linePurpose,
    contractNumber,
    contractStartDate,
    contractEndDate,
    monthlyFee,
    remarks,
    loginUsername
  } = req.body;

  // 使用新的验证服务进行数据验证
  const validator = createValidator({
    lineName: [
      { validator: (value) => validateRequired(value, '线路名称') },
      { validator: (value) => validateLength(value, 2, 100, '线路名称') },
      { validator: (value) => validateFormat(value, /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/, '线路名称', '只能包含中文、英文、数字、下划线和横线') }
    ],
    provider: [
      { validator: (value) => validateRequired(value, '运营商') }
    ],
    lineType: [
      { validator: (value) => validateRequired(value, '线路类型') }
    ],
    bandwidth: [
      { validator: (value) => validateRequired(value, '带宽') },
      { validator: (value) => validateBandwidth(value, '带宽') }
    ],
    datacenter: [
      { validator: (value) => validateRequired(value, '所属机房') }
    ],
    ipRange: [
      { validator: (value) => value && validateCIDR(value, 'IP段') }
    ],
    monthlyFee: [
      { validator: (value) => validateFee(value, '月费用') }
    ]
  });

  validator({
    lineName,
    provider,
    lineType,
    bandwidth,
    ipRange,
    datacenter,
    monthlyFee
  });

  // 验证日期范围
  if (contractStartDate && contractEndDate) {
    validateDateRange(contractStartDate, contractEndDate, '合同开始日期', '合同结束日期');
  }

  // 检查线路名称是否已存在
  const existsQuery = `
    SELECT COUNT(*) as count 
    FROM cmdb_internet_lines 
    WHERE line_name = $1 AND del_flag = '0'
  `;
  const existsResult = await connPG.query(existsQuery, [lineName]);

  if (parseInt(existsResult.rows[0].count) > 0) {
    throw new ConflictError('线路名称已存在，请使用其他名称');
  }

  const insertQuery = `
    INSERT INTO cmdb_internet_lines (
      line_name,
      provider,
      line_type,
      bandwidth,
      ip_range,
      gateway_ip,
      firewall_ip,
      datacenter,
      access_method,
      line_purpose,
      contract_number,
      contract_start_date,
      contract_end_date,
      monthly_fee,
      remarks,
      created_by,
      updated_by
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
    RETURNING *
  `;

  const params = [
    lineName,
    provider,
    lineType,
    bandwidth,
    ipRange,
    gatewayIp,
    firewallIp,
    datacenter,
    accessMethod,
    linePurpose,
    contractNumber,
    contractStartDate,
    contractEndDate,
    monthlyFee,
    remarks,
    loginUsername || 'admin',
    loginUsername || 'admin'
  ];

  const result = await connPG.query(insertQuery, params);

  // 记录操作日志
  await recordOperationLog(req, 'add', `添加互联网线路: ${lineName}`, {
    lineName,
    provider,
    lineType,
    datacenter,
    bandwidth
  });

  res.json({
    code: 0,
    msg: result.rows[0]
  });
});

// 更新线路
const updateInternetLine = async (req, res) => {
  try {
    const {
      id,
      lineName,
      provider,
      lineType,
      bandwidth,
      ipRange,
      gatewayIp,
      firewallIp,
      datacenter,
      accessMethod,
      linePurpose,
      contractNumber,
      contractStartDate,
      contractEndDate,
      monthlyFee,
      remarks,
      loginUsername
    } = req.body;

    if (!id) {
      return res.status(400).json({ code: 1, msg: '缺少线路ID' });
    }

    // 数据验证
    const validation = validateInternetLineData({
      lineName,
      provider,
      lineType,
      bandwidth,
      ipRange,
      datacenter,
      monthlyFee,
      contractStartDate,
      contractEndDate,
      contractNumber
    });

    if (!validation.valid) {
      return res.status(400).json({
        code: 1,
        msg: validation.message
      });
    }

    // 检查线路名称是否已存在（排除当前记录）
    const existsQuery = `
      SELECT COUNT(*) as count 
      FROM cmdb_internet_lines 
      WHERE line_name = $1 AND id != $2 AND del_flag = '0'
    `;
    const existsResult = await connPG.query(existsQuery, [lineName, id]);

    if (parseInt(existsResult.rows[0].count) > 0) {
      return res.status(400).json({
        code: 1,
        msg: '线路名称已存在，请使用其他名称'
      });
    }

    const updateQuery = `
      UPDATE cmdb_internet_lines
      SET
        line_name = $1,
        provider = $2,
        line_type = $3,
        bandwidth = $4,
        ip_range = $5,
        gateway_ip = $6,
        firewall_ip = $7,
        datacenter = $8,
        access_method = $9,
        line_purpose = $10,
        contract_number = $11,
        contract_start_date = $12,
        contract_end_date = $13,
        monthly_fee = $14,
        remarks = $15,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = $16
      WHERE id = $17 AND del_flag = '0'
      RETURNING *
    `;

    const params = [
      lineName,
      provider,
      lineType,
      bandwidth,
      ipRange,
      gatewayIp,
      firewallIp,
      datacenter,
      accessMethod,
      linePurpose,
      contractNumber,
      contractStartDate,
      contractEndDate,
      monthlyFee,
      remarks,
      loginUsername || 'admin',
      id
    ];

    const result = await connPG.query(updateQuery, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的线路记录' });
    }

    // 记录操作日志
    await recordOperationLog(req, 'update', `更新互联网线路: ${lineName}`, {
      id,
      lineName,
      provider,
      lineType,
      datacenter,
      bandwidth
    });

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 删除线路
const deleteInternetLine = async (req, res) => {
  try {
    const { id, loginUsername } = req.body;

    if (!id) {
      return res.status(400).json({ code: 1, msg: '缺少线路ID' });
    }

    // 检查是否有关联的IP映射记录
    const mappingCheckQuery = `
      SELECT COUNT(*) as count 
      FROM cmdb_ip_mappings 
      WHERE line_id = $1 AND del_flag = '0'
    `;
    const mappingResult = await connPG.query(mappingCheckQuery, [id]);

    if (parseInt(mappingResult.rows[0].count) > 0) {
      return res.status(400).json({
        code: 1,
        msg: '该线路下存在IP映射记录，无法删除。请先删除相关的IP映射记录。'
      });
    }

    const deleteQuery = `
      UPDATE cmdb_internet_lines
      SET
        del_flag = '1',
        updated_at = CURRENT_TIMESTAMP,
        updated_by = $1
      WHERE id = $2 AND del_flag = '0'
      RETURNING *
    `;

    const result = await connPG.query(deleteQuery, [loginUsername || 'admin', id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的线路记录' });
    }

    // 记录操作日志
    const deletedLine = result.rows[0];
    await recordOperationLog(req, 'delete', `删除互联网线路: ${deletedLine.line_name}`, {
      id,
      lineName: deletedLine.line_name,
      provider: deletedLine.provider,
      lineType: deletedLine.line_type,
      datacenter: deletedLine.datacenter
    });

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取费用统计
const getCostStatistics = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      groupBy = 'provider', // provider, lineType, datacenter
      loginUsername
    } = req.body;

    // 验证groupBy参数
    const validGroupBy = ['provider', 'lineType', 'datacenter'];
    if (!validGroupBy.includes(groupBy)) {
      return res.status(400).json({
        code: 1,
        msg: '无效的分组参数，支持的分组方式：provider, lineType, datacenter'
      });
    }

    // 构建查询条件
    const conditions = ['del_flag = \'0\''];
    const params = [];
    let paramIndex = 1;

    // 日期范围筛选（基于合同开始日期）
    if (startDate) {
      conditions.push(`contract_start_date >= $${paramIndex++}`);
      params.push(startDate);
    }

    if (endDate) {
      conditions.push(`contract_start_date <= $${paramIndex++}`);
      params.push(endDate);
    }

    // 构建分组字段映射
    const groupByMapping = {
      provider: 'provider',
      lineType: 'line_type',
      datacenter: 'datacenter'
    };

    const groupByField = groupByMapping[groupBy];
    const whereClause = conditions.join(' AND ');

    // 费用统计查询
    const statisticsQuery = `
      SELECT
        ${groupByField} as group_name,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as total_monthly_fee,
        AVG(COALESCE(monthly_fee, 0)) as avg_monthly_fee,
        MIN(COALESCE(monthly_fee, 0)) as min_monthly_fee,
        MAX(COALESCE(monthly_fee, 0)) as max_monthly_fee,
        SUM(COALESCE(monthly_fee, 0) * 12) as total_annual_fee
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      GROUP BY ${groupByField}
      ORDER BY total_monthly_fee DESC
    `;

    const statisticsResult = await connPG.query(statisticsQuery, params);

    // 总体统计查询
    const totalQuery = `
      SELECT
        COUNT(*) as total_lines,
        SUM(COALESCE(monthly_fee, 0)) as total_monthly_cost,
        AVG(COALESCE(monthly_fee, 0)) as avg_monthly_cost,
        SUM(COALESCE(monthly_fee, 0) * 12) as total_annual_cost,
        COUNT(CASE WHEN contract_end_date < CURRENT_DATE THEN 1 END) as expired_contracts,
        COUNT(CASE WHEN contract_end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN 1 END) as expiring_contracts
      FROM cmdb_internet_lines
      WHERE ${whereClause}
    `;

    const totalResult = await connPG.query(totalQuery, params);

    // 按运营商统计（如果不是按运营商分组）
    let providerStats = [];
    if (groupBy !== 'provider') {
      const providerQuery = `
        SELECT
          provider,
          COUNT(*) as count,
          SUM(COALESCE(monthly_fee, 0)) as monthly_fee
        FROM cmdb_internet_lines
        WHERE ${whereClause}
        GROUP BY provider
        ORDER BY monthly_fee DESC
      `;
      const providerResult = await connPG.query(providerQuery, params);
      providerStats = providerResult.rows;
    }

    res.json({
      code: 0,
      msg: {
        groupBy,
        statistics: statisticsResult.rows,
        total: totalResult.rows[0],
        providerStats: providerStats,
        dateRange: {
          startDate,
          endDate
        }
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取合同到期提醒
const getContractExpiryAlerts = async (req, res) => {
  try {
    const {
      alertDays = 30, // 提前提醒天数，默认30天
      loginUsername
    } = req.body;

    const currentDate = new Date().toISOString().split('T')[0];
    const alertDate = new Date(Date.now() + alertDays * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // 查询即将到期和已过期的合同
    const alertQuery = `
      SELECT
        id,
        line_name,
        provider,
        line_type,
        datacenter,
        contract_number,
        contract_start_date,
        contract_end_date,
        monthly_fee,
        CASE 
          WHEN contract_end_date < CURRENT_DATE THEN '已过期'
          WHEN contract_end_date BETWEEN CURRENT_DATE AND $1 THEN '即将过期'
          ELSE '正常'
        END as alert_type,
        (contract_end_date - CURRENT_DATE) as days_to_expiry,
        COALESCE(monthly_fee, 0) * 12 as annual_fee
      FROM cmdb_internet_lines
      WHERE del_flag = '0'
        AND contract_end_date IS NOT NULL
        AND (
          contract_end_date < CURRENT_DATE 
          OR contract_end_date BETWEEN CURRENT_DATE AND $1
        )
      ORDER BY contract_end_date ASC
    `;

    const alertResult = await connPG.query(alertQuery, [alertDate]);

    // 统计信息
    const expiredCount = alertResult.rows.filter(row => row.alert_type === '已过期').length;
    const expiringCount = alertResult.rows.filter(row => row.alert_type === '即将过期').length;
    const totalAffectedFee = alertResult.rows.reduce((sum, row) => sum + parseFloat(row.monthly_fee || 0), 0);

    res.json({
      code: 0,
      msg: {
        alerts: alertResult.rows,
        summary: {
          expiredCount,
          expiringCount,
          totalCount: alertResult.rows.length,
          totalAffectedMonthlyFee: totalAffectedFee,
          totalAffectedAnnualFee: totalAffectedFee * 12,
          alertDays
        }
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 导出线路数据到Excel
const exportInternetLineData = async (req, res) => {
  try {
    const {
      exportType = 'all', // all, filtered
      filters = {},
      loginUsername
    } = req.body;

    // 构建查询条件
    const conditions = ['del_flag = \'0\''];
    const params = [];
    let paramIndex = 1;

    // 如果是筛选导出，应用筛选条件
    if (exportType === 'filtered') {
      const {
        lineName,
        provider,
        lineType,
        datacenter,
        contractStatus
      } = filters;

      if (lineName) {
        conditions.push(`line_name ILIKE $${paramIndex++}`);
        params.push(`%${lineName}%`);
      }

      if (provider) {
        conditions.push(`provider = $${paramIndex++}`);
        params.push(provider);
      }

      if (lineType) {
        conditions.push(`line_type = $${paramIndex++}`);
        params.push(lineType);
      }

      if (datacenter) {
        conditions.push(`datacenter ILIKE $${paramIndex++}`);
        params.push(`%${datacenter}%`);
      }

      if (contractStatus) {
        const currentDate = new Date().toISOString().split('T')[0];
        const thirtyDaysLater = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        switch (contractStatus) {
          case 'active':
            conditions.push(`contract_end_date > $${paramIndex++}`);
            params.push(currentDate);
            break;
          case 'expired':
            conditions.push(`contract_end_date < $${paramIndex++}`);
            params.push(currentDate);
            break;
          case 'expiring':
            conditions.push(`contract_end_date BETWEEN $${paramIndex++} AND $${paramIndex++}`);
            params.push(currentDate);
            params.push(thirtyDaysLater);
            break;
        }
      }
    }

    const whereClause = conditions.join(' AND ');

    // 查询数据
    const dataQuery = `
      SELECT
        line_name as "线路名称",
        provider as "运营商",
        line_type as "线路类型",
        bandwidth as "带宽",
        ip_range as "IP段",
        datacenter as "所属机房",
        access_method as "接入方式",
        line_purpose as "线路用途",
        contract_number as "合同编号",
        TO_CHAR(contract_start_date, 'YYYY-MM-DD') as "合同开始日期",
        TO_CHAR(contract_end_date, 'YYYY-MM-DD') as "合同结束日期",
        monthly_fee as "月费用",
        COALESCE(monthly_fee, 0) * 12 as "年费用",
        CASE 
          WHEN contract_end_date IS NULL THEN '未设置'
          WHEN contract_end_date < CURRENT_DATE THEN '已过期'
          WHEN contract_end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN '即将过期'
          ELSE '有效'
        END as "合同状态",
        CASE 
          WHEN contract_end_date IS NOT NULL THEN 
            (contract_end_date - CURRENT_DATE)
          ELSE NULL
        END as "距离到期天数",
        remarks as "备注",
        TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') as "创建时间",
        created_by as "创建人",
        TO_CHAR(updated_at, 'YYYY-MM-DD HH24:MI:SS') as "更新时间",
        updated_by as "更新人"
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      ORDER BY id DESC
    `;

    const dataResult = await connPG.query(dataQuery, params);

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 线路数据工作表
    const linesWorksheet = XLSX.utils.json_to_sheet(dataResult.rows);
    XLSX.utils.book_append_sheet(workbook, linesWorksheet, '线路数据');

    // 费用统计工作表
    const statsQuery = `
      SELECT
        provider as "运营商",
        COUNT(*) as "线路数量",
        SUM(COALESCE(monthly_fee, 0)) as "月费用合计",
        AVG(COALESCE(monthly_fee, 0)) as "月费用平均",
        SUM(COALESCE(monthly_fee, 0) * 12) as "年费用合计"
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      GROUP BY provider
      ORDER BY "月费用合计" DESC
    `;

    const statsResult = await connPG.query(statsQuery, params);
    const statsWorksheet = XLSX.utils.json_to_sheet(statsResult.rows);
    XLSX.utils.book_append_sheet(workbook, statsWorksheet, '费用统计');

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // 设置响应头
    const fileName = `互联网线路数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`);

    res.send(excelBuffer);
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 生成线路导入模板
const generateInternetLineImportTemplate = async (req, res) => {
  try {
    const { generateImportTemplate } = require('../utils/excel-import-export');
    
    // 定义模板表头
    const headers = [
      '线路名称',
      '运营商',
      '线路类型', 
      '带宽',
      'IP段',
      '所属机房',
      '接入方式',
      '线路用途',
      '合同编号',
      '合同开始日期',
      '合同结束日期',
      '月费用',
      '备注'
    ];
    
    // 示例数据
    const sampleData = [
      {
        '线路名称': '电信专线示例',
        '运营商': '中国电信',
        '线路类型': '专线',
        '带宽': '100M',
        'IP段': '***********/24',
        '所属机房': '机房A',
        '接入方式': '光纤接入',
        '线路用途': '业务系统访问',
        '合同编号': 'CT2024001',
        '合同开始日期': '2024-01-01',
        '合同结束日期': '2025-12-31',
        '月费用': '5000',
        '备注': '主要用于生产环境'
      },
      {
        '线路名称': '联通宽带示例',
        '运营商': '中国联通',
        '线路类型': '宽带',
        '带宽': '50M',
        'IP段': '********/24',
        '所属机房': '机房B',
        '接入方式': '以太网接入',
        '线路用途': '办公网络',
        '合同编号': 'CU2024002',
        '合同开始日期': '2024-02-01',
        '合同结束日期': '2025-01-31',
        '月费用': '2000',
        '备注': '用于日常办公'
      }
    ];
    
    // 生成模板
    const templateBuffer = generateImportTemplate(headers, sampleData, '线路导入模板');
    
    // 设置响应头
    const fileName = `互联网线路导入模板_${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(fileName)}`);
    
    res.send(templateBuffer);
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 批量导入线路数据
const importInternetLineData = async (req, res) => {
  try {
    const { data, loginUsername } = req.body;

    if (!data || !Array.isArray(data) || data.length === 0) {
      return res.status(400).json({
        code: 1,
        msg: '导入数据不能为空'
      });
    }

    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    // 获取现有线路名称用于重复检查
    const existingLinesQuery = `SELECT line_name FROM cmdb_internet_lines WHERE del_flag = '0'`;
    const existingLinesResult = await connPG.query(existingLinesQuery);
    const existingLineNames = new Set(existingLinesResult.rows.map(row => row.line_name));

    // 逐条处理导入数据
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowIndex = i + 1;

      try {
        // 数据映射和验证
        const lineData = {
          lineName: row['线路名称'] || row['line_name'],
          provider: row['运营商'] || row['provider'],
          lineType: row['线路类型'] || row['line_type'],
          bandwidth: row['带宽'] || row['bandwidth'],
          ipRange: row['IP段'] || row['ip_range'],
          datacenter: row['所属机房'] || row['datacenter'],
          accessMethod: row['接入方式'] || row['access_method'],
          linePurpose: row['线路用途'] || row['line_purpose'],
          contractNumber: row['合同编号'] || row['contract_number'],
          contractStartDate: row['合同开始日期'] || row['contract_start_date'],
          contractEndDate: row['合同结束日期'] || row['contract_end_date'],
          monthlyFee: row['月费用'] || row['monthly_fee'],
          remarks: row['备注'] || row['remarks']
        };

        // 验证必填字段
        if (!lineData.lineName || !lineData.provider || !lineData.lineType || !lineData.bandwidth || !lineData.datacenter) {
          results.errors.push(`第${rowIndex}行: 缺少必填字段（线路名称、运营商、线路类型、带宽、所属机房）`);
          results.failed++;
          continue;
        }

        // 检查线路名称是否已存在
        if (existingLineNames.has(lineData.lineName)) {
          results.errors.push(`第${rowIndex}行: 线路名称"${lineData.lineName}"已存在`);
          results.failed++;
          continue;
        }

        // 数据验证
        const validation = validateInternetLineData(lineData);
        if (!validation.valid) {
          results.errors.push(`第${rowIndex}行: ${validation.message}`);
          results.failed++;
          continue;
        }

        // 处理日期格式
        let processedStartDate = null;
        let processedEndDate = null;

        if (lineData.contractStartDate) {
          try {
            const startDate = new Date(lineData.contractStartDate);
            if (!isNaN(startDate.getTime())) {
              processedStartDate = startDate.toISOString().split('T')[0];
            }
          } catch (e) {
            results.errors.push(`第${rowIndex}行: 合同开始日期格式错误`);
            results.failed++;
            continue;
          }
        }

        if (lineData.contractEndDate) {
          try {
            const endDate = new Date(lineData.contractEndDate);
            if (!isNaN(endDate.getTime())) {
              processedEndDate = endDate.toISOString().split('T')[0];
            }
          } catch (e) {
            results.errors.push(`第${rowIndex}行: 合同结束日期格式错误`);
            results.failed++;
            continue;
          }
        }

        // 处理费用数据
        let processedMonthlyFee = null;
        if (lineData.monthlyFee !== undefined && lineData.monthlyFee !== null && lineData.monthlyFee !== '') {
          const fee = parseFloat(lineData.monthlyFee);
          if (!isNaN(fee) && fee >= 0) {
            processedMonthlyFee = fee;
          } else {
            results.errors.push(`第${rowIndex}行: 月费用格式错误，必须为非负数字`);
            results.failed++;
            continue;
          }
        }

        // 插入数据
        const insertQuery = `
          INSERT INTO cmdb_internet_lines (
            line_name, provider, line_type, bandwidth, ip_range, datacenter,
            access_method, line_purpose, contract_number, contract_start_date,
            contract_end_date, monthly_fee, remarks, created_by, updated_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        `;

        const params = [
          lineData.lineName,
          lineData.provider,
          lineData.lineType,
          lineData.bandwidth,
          lineData.ipRange,
          lineData.datacenter,
          lineData.accessMethod,
          lineData.linePurpose,
          lineData.contractNumber,
          processedStartDate,
          processedEndDate,
          processedMonthlyFee,
          lineData.remarks,
          loginUsername || 'admin',
          loginUsername || 'admin'
        ];

        await connPG.query(insertQuery, params);
        
        // 添加到已存在的线路名称集合中，避免同一批次内的重复
        existingLineNames.add(lineData.lineName);
        results.success++;

      } catch (error) {
        results.errors.push(`第${rowIndex}行: ${error.message}`);
        results.failed++;
      }
    }

    // 记录操作日志
    await recordOperationLog(req, 'import', `批量导入互联网线路数据`, {
      totalRows: data.length,
      successCount: results.success,
      failedCount: results.failed
    });

    res.json({
      code: 0,
      msg: results
    });

  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取线路使用情况统计
const getLineUsageStatistics = async (req, res) => {
  try {
    const { loginUsername } = req.body;

    // 线路使用情况统计
    const usageQuery = `
      SELECT
        l.provider,
        l.line_type,
        l.datacenter,
        COUNT(l.id) as total_lines,
        COUNT(CASE WHEN m.line_id IS NOT NULL THEN 1 END) as used_lines,
        COUNT(CASE WHEN m.line_id IS NULL THEN 1 END) as unused_lines,
        ROUND(
          (COUNT(CASE WHEN m.line_id IS NOT NULL THEN 1 END)::decimal / COUNT(l.id)) * 100, 2
        ) as usage_rate,
        COUNT(DISTINCT m.internet_ip) as used_ip_count,
        COUNT(m.id) as total_mappings
      FROM cmdb_internet_lines l
      LEFT JOIN cmdb_ip_mappings m ON l.id = m.line_id AND m.del_flag = '0'
      WHERE l.del_flag = '0'
      GROUP BY l.provider, l.line_type, l.datacenter
      ORDER BY usage_rate DESC
    `;

    const usageResult = await connPG.query(usageQuery);

    // 总体使用统计
    const totalUsageQuery = `
      SELECT
        COUNT(DISTINCT l.id) as total_lines,
        COUNT(DISTINCT CASE WHEN m.line_id IS NOT NULL THEN l.id END) as used_lines,
        COUNT(DISTINCT CASE WHEN m.line_id IS NULL THEN l.id END) as unused_lines,
        ROUND(
          (COUNT(DISTINCT CASE WHEN m.line_id IS NOT NULL THEN l.id END)::decimal / COUNT(DISTINCT l.id)) * 100, 2
        ) as overall_usage_rate,
        COUNT(m.id) as total_mappings,
        COUNT(DISTINCT m.internet_ip) as total_used_ips
      FROM cmdb_internet_lines l
      LEFT JOIN cmdb_ip_mappings m ON l.id = m.line_id AND m.del_flag = '0'
      WHERE l.del_flag = '0'
    `;

    const totalUsageResult = await connPG.query(totalUsageQuery);

    // 按运营商统计
    const providerUsageQuery = `
      SELECT
        l.provider,
        COUNT(l.id) as total_lines,
        COUNT(CASE WHEN m.line_id IS NOT NULL THEN 1 END) as used_lines,
        ROUND(
          (COUNT(CASE WHEN m.line_id IS NOT NULL THEN 1 END)::decimal / COUNT(l.id)) * 100, 2
        ) as usage_rate
      FROM cmdb_internet_lines l
      LEFT JOIN cmdb_ip_mappings m ON l.id = m.line_id AND m.del_flag = '0'
      WHERE l.del_flag = '0'
      GROUP BY l.provider
      ORDER BY usage_rate DESC
    `;

    const providerUsageResult = await connPG.query(providerUsageQuery);

    res.json({
      code: 0,
      msg: {
        detailedUsage: usageResult.rows,
        totalUsage: totalUsageResult.rows[0],
        providerUsage: providerUsageResult.rows
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取IP资源利用率统计
const getIpResourceUtilization = async (req, res) => {
  try {
    const { loginUsername } = req.body;

    // IP资源利用率统计
    const ipUtilizationQuery = `
      SELECT
        l.line_name,
        l.provider,
        l.line_type,
        l.datacenter,
        l.ip_range,
        COUNT(m.id) as total_mappings,
        COUNT(DISTINCT m.internet_ip) as used_ip_count,
        COUNT(DISTINCT m.protocol) as protocol_types,
        COUNT(CASE WHEN m.protocol = 'TCP' THEN 1 END) as tcp_mappings,
        COUNT(CASE WHEN m.protocol = 'UDP' THEN 1 END) as udp_mappings,
        COUNT(CASE WHEN m.status = 'active' THEN 1 END) as active_mappings,
        COUNT(CASE WHEN m.status = 'inactive' THEN 1 END) as inactive_mappings
      FROM cmdb_internet_lines l
      LEFT JOIN cmdb_ip_mappings m ON l.id = m.line_id AND m.del_flag = '0'
      WHERE l.del_flag = '0'
      GROUP BY l.id, l.line_name, l.provider, l.line_type, l.datacenter, l.ip_range
      ORDER BY total_mappings DESC
    `;

    const ipUtilizationResult = await connPG.query(ipUtilizationQuery);

    // 端口使用统计
    const portUsageQuery = `
      SELECT
        protocol,
        COUNT(*) as port_count,
        COUNT(DISTINCT internet_ip) as ip_count,
        MIN(CAST(mapped_port AS INTEGER)) as min_port,
        MAX(CAST(mapped_port AS INTEGER)) as max_port
      FROM cmdb_ip_mappings
      WHERE del_flag = '0' AND mapped_port ~ '^[0-9]+$'
      GROUP BY protocol
      ORDER BY port_count DESC
    `;

    const portUsageResult = await connPG.query(portUsageQuery);

    // 机房IP资源分布
    const datacenterIpQuery = `
      SELECT
        m.datacenter,
        COUNT(m.id) as total_mappings,
        COUNT(DISTINCT m.internet_ip) as unique_ips,
        COUNT(DISTINCT l.id) as lines_used,
        COUNT(CASE WHEN m.protocol = 'TCP' THEN 1 END) as tcp_count,
        COUNT(CASE WHEN m.protocol = 'UDP' THEN 1 END) as udp_count
      FROM cmdb_ip_mappings m
      LEFT JOIN cmdb_internet_lines l ON m.line_id = l.id AND l.del_flag = '0'
      WHERE m.del_flag = '0'
      GROUP BY m.datacenter
      ORDER BY total_mappings DESC
    `;

    const datacenterIpResult = await connPG.query(datacenterIpQuery);

    // 总体IP统计
    const totalIpQuery = `
      SELECT
        COUNT(*) as total_mappings,
        COUNT(DISTINCT internet_ip) as total_unique_ips,
        COUNT(DISTINCT line_id) as total_lines_used,
        COUNT(CASE WHEN protocol = 'TCP' THEN 1 END) as total_tcp,
        COUNT(CASE WHEN protocol = 'UDP' THEN 1 END) as total_udp,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as total_active,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as total_inactive
      FROM cmdb_ip_mappings
      WHERE del_flag = '0'
    `;

    const totalIpResult = await connPG.query(totalIpQuery);

    res.json({
      code: 0,
      msg: {
        ipUtilization: ipUtilizationResult.rows,
        portUsage: portUsageResult.rows,
        datacenterDistribution: datacenterIpResult.rows,
        totalStats: totalIpResult.rows[0]
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取费用趋势统计
const getCostTrendStatistics = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      groupBy = 'month', // month, quarter, year
      loginUsername
    } = req.body;

    // 验证groupBy参数
    const validGroupBy = ['month', 'quarter', 'year'];
    if (!validGroupBy.includes(groupBy)) {
      return res.status(400).json({
        code: 1,
        msg: '无效的分组参数，支持的分组方式：month, quarter, year'
      });
    }

    // 构建日期分组字段
    const dateGroupMapping = {
      month: "TO_CHAR(contract_start_date, 'YYYY-MM')",
      quarter: "TO_CHAR(contract_start_date, 'YYYY-Q')",
      year: "TO_CHAR(contract_start_date, 'YYYY')"
    };

    const dateGroupField = dateGroupMapping[groupBy];

    // 构建查询条件
    const conditions = ['del_flag = \'0\'', 'contract_start_date IS NOT NULL'];
    const params = [];
    let paramIndex = 1;

    if (startDate) {
      conditions.push(`contract_start_date >= $${paramIndex++}`);
      params.push(startDate);
    }

    if (endDate) {
      conditions.push(`contract_start_date <= $${paramIndex++}`);
      params.push(endDate);
    }

    const whereClause = conditions.join(' AND ');

    // 费用趋势查询
    const trendQuery = `
      SELECT
        ${dateGroupField} as period,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as total_monthly_fee,
        AVG(COALESCE(monthly_fee, 0)) as avg_monthly_fee,
        SUM(COALESCE(monthly_fee, 0) * 12) as total_annual_fee
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      GROUP BY ${dateGroupField}
      ORDER BY period
    `;

    const trendResult = await connPG.query(trendQuery, params);

    // 按运营商的费用趋势
    const providerTrendQuery = `
      SELECT
        ${dateGroupField} as period,
        provider,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as monthly_fee
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      GROUP BY ${dateGroupField}, provider
      ORDER BY period, provider
    `;

    const providerTrendResult = await connPG.query(providerTrendQuery, params);

    // 累计费用统计
    const cumulativeQuery = `
      SELECT
        ${dateGroupField} as period,
        SUM(SUM(COALESCE(monthly_fee, 0))) OVER (ORDER BY ${dateGroupField}) as cumulative_monthly_fee,
        SUM(COUNT(*)) OVER (ORDER BY ${dateGroupField}) as cumulative_line_count
      FROM cmdb_internet_lines
      WHERE ${whereClause}
      GROUP BY ${dateGroupField}
      ORDER BY period
    `;

    const cumulativeResult = await connPG.query(cumulativeQuery, params);

    res.json({
      code: 0,
      msg: {
        groupBy,
        trendData: trendResult.rows,
        providerTrend: providerTrendResult.rows,
        cumulativeData: cumulativeResult.rows,
        dateRange: {
          startDate,
          endDate
        }
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取按机房维度的资源分布统计
const getDatacenterResourceDistribution = async (req, res) => {
  try {
    const { loginUsername } = req.body;

    // 机房资源分布统计
    const distributionQuery = `
      SELECT
        l.datacenter,
        COUNT(l.id) as total_lines,
        COUNT(DISTINCT l.provider) as provider_count,
        COUNT(DISTINCT l.line_type) as line_type_count,
        SUM(COALESCE(l.monthly_fee, 0)) as total_monthly_fee,
        AVG(COALESCE(l.monthly_fee, 0)) as avg_monthly_fee,
        COUNT(m.id) as total_mappings,
        COUNT(DISTINCT m.internet_ip) as unique_ips,
        COUNT(CASE WHEN m.protocol = 'TCP' THEN 1 END) as tcp_mappings,
        COUNT(CASE WHEN m.protocol = 'UDP' THEN 1 END) as udp_mappings,
        COUNT(CASE WHEN l.contract_end_date < CURRENT_DATE THEN 1 END) as expired_contracts,
        COUNT(CASE WHEN l.contract_end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN 1 END) as expiring_contracts
      FROM cmdb_internet_lines l
      LEFT JOIN cmdb_ip_mappings m ON l.id = m.line_id AND m.del_flag = '0'
      WHERE l.del_flag = '0'
      GROUP BY l.datacenter
      ORDER BY total_monthly_fee DESC
    `;

    const distributionResult = await connPG.query(distributionQuery);

    // 机房运营商分布
    const datacenterProviderQuery = `
      SELECT
        datacenter,
        provider,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as monthly_fee
      FROM cmdb_internet_lines
      WHERE del_flag = '0'
      GROUP BY datacenter, provider
      ORDER BY datacenter, monthly_fee DESC
    `;

    const datacenterProviderResult = await connPG.query(datacenterProviderQuery);

    // 机房线路类型分布
    const datacenterLineTypeQuery = `
      SELECT
        datacenter,
        line_type,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as monthly_fee
      FROM cmdb_internet_lines
      WHERE del_flag = '0'
      GROUP BY datacenter, line_type
      ORDER BY datacenter, monthly_fee DESC
    `;

    const datacenterLineTypeResult = await connPG.query(datacenterLineTypeQuery);

    // 机房带宽分布
    const datacenterBandwidthQuery = `
      SELECT
        datacenter,
        bandwidth,
        COUNT(*) as line_count,
        SUM(COALESCE(monthly_fee, 0)) as monthly_fee
      FROM cmdb_internet_lines
      WHERE del_flag = '0'
      GROUP BY datacenter, bandwidth
      ORDER BY datacenter, line_count DESC
    `;

    const datacenterBandwidthResult = await connPG.query(datacenterBandwidthQuery);

    // 总体机房统计
    const totalDatacenterQuery = `
      SELECT
        COUNT(DISTINCT datacenter) as total_datacenters,
        COUNT(*) as total_lines,
        SUM(COALESCE(monthly_fee, 0)) as total_monthly_fee,
        AVG(COALESCE(monthly_fee, 0)) as avg_monthly_fee
      FROM cmdb_internet_lines
      WHERE del_flag = '0'
    `;

    const totalDatacenterResult = await connPG.query(totalDatacenterQuery);

    res.json({
      code: 0,
      msg: {
        distribution: distributionResult.rows,
        providerDistribution: datacenterProviderResult.rows,
        lineTypeDistribution: datacenterLineTypeResult.rows,
        bandwidthDistribution: datacenterBandwidthResult.rows,
        totalStats: totalDatacenterResult.rows[0]
      }
    });
  } catch (error) {
    res.status(500).json({ code: 1, msg: error.message });
  }
};

module.exports = {
  getInternetLines,
  addInternetLine,
  updateInternetLine,
  deleteInternetLine,
  getCostStatistics,
  getContractExpiryAlerts,
  exportInternetLineData,
  importInternetLineData,
  generateInternetLineImportTemplate,
  getLineUsageStatistics,
  getIpResourceUtilization,
  getCostTrendStatistics,
  getDatacenterResourceDistribution
};