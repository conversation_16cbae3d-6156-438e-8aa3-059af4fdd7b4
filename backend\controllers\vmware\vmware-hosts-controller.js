/**
 * VMware虚拟化信息管理控制器
 * 提供VMware虚拟机数据的CRUD操作和同步管理
 */

class VmwareHostsController {
    constructor() {
        this.tableName = 'cmdb_vmware_info_auto_update';
        this.viewName = 'v_cmdb_vmware_info_auto_update';
    }

    /**
     * 获取VMware虚拟机列表
     */
    async getVmwareHostsList(req, res) {
        try {
            const {
                vm_name,
                vcenter_ip,
                esxi_ip,
                vm_ip,
                sync_status,
                currentPage = 1,
                pageSize = 20,
                sortProp = 'last_sync_time',
                sortOrder = 'desc'
            } = req.body;

            const page = parseInt(currentPage);
            const perPage = parseInt(pageSize);
            const offset = (page - 1) * perPage;

            // 构建查询条件
            const conditions = [];
            const queryParams = [];
            let paramIndex = 1;

            if (vm_name) {
                conditions.push(`vm_name ILIKE $${paramIndex}`);
                queryParams.push(`%${vm_name}%`);
                paramIndex++;
            }

            if (vcenter_ip) {
                conditions.push(`vcenter_ip::text ILIKE $${paramIndex}`);
                queryParams.push(`%${vcenter_ip}%`);
                paramIndex++;
            }

            if (esxi_ip) {
                conditions.push(`esxi_ip::text ILIKE $${paramIndex}`);
                queryParams.push(`%${esxi_ip}%`);
                paramIndex++;
            }

            if (vm_ip) {
                conditions.push(`vm_ip::text ILIKE $${paramIndex}`);
                queryParams.push(`%${vm_ip}%`);
                paramIndex++;
            }

            if (sync_status) {
                conditions.push(`sync_status = $${paramIndex}`);
                queryParams.push(sync_status);
                paramIndex++;
            }

            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

            // 验证排序字段
            const allowedSortFields = [
                'id', 'vm_name', 'vcenter_ip', 'esxi_ip', 'vm_ip', 'data_source_time',
                'last_sync_time', 'sync_status', 'created_at', 'updated_at'
            ];

            const validSortProp = allowedSortFields.includes(sortProp) ? sortProp : 'last_sync_time';
            const validSortOrder = ['asc', 'desc'].includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

            // 查询数据
            const dataQuery = `
                SELECT * FROM ${this.viewName}
                ${whereClause}
                ORDER BY ${validSortProp} ${validSortOrder}, id DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            queryParams.push(perPage, offset);

            const dataResult = await req.app.get('connPG').query(dataQuery, queryParams);

            // 查询总数
            const countQuery = `
                SELECT COUNT(*) as total FROM ${this.viewName}
                ${whereClause}
            `;

            const countResult = await req.app.get('connPG').query(countQuery, queryParams.slice(0, -2));

            res.json({
                code: 0,
                msg: dataResult.rows,
                total: parseInt(countResult.rows[0].total)
            });

        } catch (error) {
            console.error('获取VMware虚拟机列表失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 获取VMware虚拟机详情
     */
    async getVmwareHostDetail(req, res) {
        try {
            const { id } = req.body;

            if (!id) {
                return res.status(400).json({
                    code: 1,
                    msg: '虚拟机ID不能为空'
                });
            }

            const query = `SELECT * FROM ${this.viewName} WHERE id = $1`;
            const result = await req.app.get('connPG').query(query, [id]);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    code: 1,
                    msg: '未找到指定的虚拟机记录'
                });
            }

            res.json({
                code: 0,
                msg: result.rows[0]
            });

        } catch (error) {
            console.error('获取VMware虚拟机详情失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 手动触发数据同步
     */
    async triggerSync(req, res) {
        try {
            const vmwareSyncService = req.app.get('vmwareSyncService');
            
            if (!vmwareSyncService) {
                return res.status(500).json({
                    code: 1,
                    msg: 'VMware同步服务未初始化'
                });
            }

            const result = await vmwareSyncService.manualSync();

            res.json({
                code: result.success ? 0 : 1,
                msg: result.message,
                data: result.data
            });

        } catch (error) {
            console.error('手动触发同步失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 获取同步服务状态
     */
    async getSyncStatus(req, res) {
        try {
            const vmwareSyncService = req.app.get('vmwareSyncService');
            
            if (!vmwareSyncService) {
                return res.status(500).json({
                    code: 1,
                    msg: 'VMware同步服务未初始化'
                });
            }

            const status = vmwareSyncService.getStatus();

            res.json({
                code: 0,
                msg: status
            });

        } catch (error) {
            console.error('获取同步状态失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 获取同步历史记录
     */
    async getSyncHistory(req, res) {
        try {
            const { limit = 20 } = req.body;
            
            const vmwareSyncService = req.app.get('vmwareSyncService');
            
            if (!vmwareSyncService) {
                return res.status(500).json({
                    code: 1,
                    msg: 'VMware同步服务未初始化'
                });
            }

            const history = await vmwareSyncService.getSyncHistory(limit);

            res.json({
                code: 0,
                msg: history
            });

        } catch (error) {
            console.error('获取同步历史失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 删除VMware虚拟机记录（软删除）
     */
    async deleteVmwareHost(req, res) {
        try {
            const { id, username } = req.body;

            if (!id) {
                return res.status(400).json({
                    code: 1,
                    msg: '虚拟机ID不能为空'
                });
            }

            const query = `
                UPDATE ${this.tableName}
                SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP, updated_by = $2
                WHERE id = $1 AND is_deleted = FALSE
                RETURNING *
            `;

            const result = await req.app.get('connPG').query(query, [id, username || 'admin']);

            if (result.rowCount === 0) {
                return res.status(404).json({
                    code: 1,
                    msg: '未找到指定的虚拟机记录或记录已被删除'
                });
            }

            res.json({
                code: 0,
                msg: '删除成功',
                data: result.rows[0]
            });

        } catch (error) {
            console.error('删除VMware虚拟机记录失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 批量删除VMware虚拟机记录
     */
    async batchDeleteVmwareHosts(req, res) {
        try {
            const { ids, username } = req.body;

            if (!ids || !Array.isArray(ids) || ids.length === 0) {
                return res.status(400).json({
                    code: 1,
                    msg: '请选择要删除的记录'
                });
            }

            const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
            const query = `
                UPDATE ${this.tableName}
                SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP, updated_by = $${ids.length + 1}
                WHERE id IN (${placeholders}) AND is_deleted = FALSE
                RETURNING id, vm_name, vm_ip
            `;

            const params = [...ids, username || 'admin'];
            const result = await req.app.get('connPG').query(query, params);

            res.json({
                code: 0,
                msg: `成功删除 ${result.rowCount} 条记录`,
                data: result.rows
            });

        } catch (error) {
            console.error('批量删除VMware虚拟机记录失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 获取统计信息
     */
    async getStatistics(req, res) {
        try {
            const statsQuery = `
                SELECT 
                    COUNT(*) as total_vms,
                    COUNT(CASE WHEN vm_ip IS NOT NULL THEN 1 END) as vms_with_ip,
                    COUNT(CASE WHEN vm_ip IS NULL OR vm_ip = '' THEN 1 END) as vms_without_ip,
                    COUNT(CASE WHEN sync_status = 'success' THEN 1 END) as sync_success,
                    COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as sync_failed,
                    COUNT(DISTINCT vcenter_ip) as total_vcenters,
                    COUNT(DISTINCT esxi_ip) as total_esxi_hosts,
                    MAX(last_sync_time) as last_sync_time,
                    MAX(data_source_time) as last_data_source_time
                FROM ${this.tableName}
                WHERE is_deleted = FALSE
            `;

            const result = await req.app.get('connPG').query(statsQuery);
            const stats = result.rows[0];

            // 格式化数据
            const formattedStats = {
                totalVms: parseInt(stats.total_vms) || 0,
                vmsWithIp: parseInt(stats.vms_with_ip) || 0,
                vmsWithoutIp: parseInt(stats.vms_without_ip) || 0,
                syncSuccess: parseInt(stats.sync_success) || 0,
                syncFailed: parseInt(stats.sync_failed) || 0,
                totalVcenters: parseInt(stats.total_vcenters) || 0,
                totalEsxiHosts: parseInt(stats.total_esxi_hosts) || 0,
                lastSyncTime: stats.last_sync_time,
                lastDataSourceTime: stats.last_data_source_time
            };

            res.json({
                code: 0,
                msg: formattedStats
            });

        } catch (error) {
            console.error('获取统计信息失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }

    /**
     * 导出VMware虚拟机数据
     */
    async exportVmwareHosts(req, res) {
        try {
            const query = `
                SELECT 
                    vm_name as "虚拟机名称",
                    vcenter_ip as "vCenter IP",
                    esxi_ip as "ESXi主机IP",
                    vm_ip as "虚拟机IP",
                    data_source_time as "数据源时间",
                    last_sync_time as "最后同步时间",
                    sync_status as "同步状态",
                    created_at_formatted as "创建时间",
                    updated_at_formatted as "更新时间"
                FROM ${this.viewName}
                ORDER BY last_sync_time DESC
            `;

            const result = await req.app.get('connPG').query(query);

            res.json({
                code: 0,
                msg: result.rows
            });

        } catch (error) {
            console.error('导出VMware虚拟机数据失败:', error);
            res.status(500).json({
                code: 1,
                msg: error.message
            });
        }
    }
}

module.exports = new VmwareHostsController();