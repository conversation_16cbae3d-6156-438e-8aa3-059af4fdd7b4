import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Activity, AlertTriangle } from 'lucide-react';

interface LocationData {
  id: string;
  name: string;
  x: number; // 百分比位置
  y: number; // 百分比位置
  value: number;
  status: 'normal' | 'warning' | 'critical';
  type: 'server' | 'network' | 'storage';
  connections?: string[]; // 连接到其他节点的ID
}

interface HolographicMapProps {
  title: string;
  locations: LocationData[];
  className?: string;
  animationDelay?: number;
  showConnections?: boolean;
}

const HolographicMap: React.FC<HolographicMapProps> = ({
  title,
  locations,
  className = '',
  animationDelay = 0,
  showConnections = true
}) => {
  const [activeLocation, setActiveLocation] = useState<string | null>(null);
  const [animatedLocations, setAnimatedLocations] = useState<LocationData[]>([]);
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    // 逐个显示位置点
    locations.forEach((location, index) => {
      setTimeout(() => {
        setAnimatedLocations(prev => [...prev, location]);
      }, animationDelay + index * 200);
    });
  }, [locations, animationDelay]);

  const getStatusColor = (status: LocationData['status']) => {
    switch (status) {
      case 'normal': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
    }
  };

  const getTypeIcon = (type: LocationData['type']) => {
    switch (type) {
      case 'server': return '🖥️';
      case 'network': return '🌐';
      case 'storage': return '💾';
    }
  };

  const renderConnections = () => {
    if (!showConnections) return null;

    return animatedLocations.map(location => {
      if (!location.connections) return null;

      return location.connections.map(targetId => {
        const target = animatedLocations.find(l => l.id === targetId);
        if (!target) return null;

        const isActive = activeLocation === location.id || activeLocation === targetId;
        
        return (
          <line
            key={`${location.id}-${targetId}`}
            x1={`${location.x}%`}
            y1={`${location.y}%`}
            x2={`${target.x}%`}
            y2={`${target.y}%`}
            stroke={isActive ? '#00d4ff' : '#00d4ff40'}
            strokeWidth={isActive ? 2 : 1}
            strokeDasharray="5,5"
            className="transition-all duration-300"
            style={{
              filter: isActive ? 'drop-shadow(0 0 6px #00d4ff)' : 'none',
              animation: isActive ? 'dataFlow 2s linear infinite' : 'none'
            }}
          />
        );
      });
    });
  };

  const renderDataFlows = () => {
    return animatedLocations.map(location => {
      if (!location.connections || activeLocation !== location.id) return null;

      return location.connections.map(targetId => {
        const target = animatedLocations.find(l => l.id === targetId);
        if (!target) return null;

        return (
          <circle
            key={`flow-${location.id}-${targetId}`}
            r="3"
            fill="#00d4ff"
            style={{
              filter: 'drop-shadow(0 0 6px #00d4ff)'
            }}
          >
            <animateMotion
              dur="2s"
              repeatCount="indefinite"
              path={`M ${location.x} ${location.y} L ${target.x} ${target.y}`}
            />
          </circle>
        );
      });
    });
  };

  return (
    <div
      className={`glass-card rounded-xl p-6 hover-lift entrance-animation ${className}`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      {/* 标题 */}
      <div className="flex flex-col items-center mb-4">
        <h3 className="text-lg font-bold tech-gradient-text text-center mb-2">{title}</h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-neon-green rounded-full pulse-glow" />
          <span className="text-xs text-gray-400">实时监控</span>
        </div>
      </div>

      {/* 地图容器 */}
      <div className="relative h-80 rounded-lg overflow-hidden hologram">
        {/* 背景网格 */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        />

        {/* 地图轮廓 */}
        <div className="absolute inset-0 flex items-center justify-center opacity-10">
          <div className="w-4/5 h-4/5 border-2 border-neon-blue rounded-lg" />
        </div>

        {/* SVG 连接线和数据流 */}
        <svg
          ref={svgRef}
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <defs>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {renderConnections()}
          {renderDataFlows()}
        </svg>

        {/* 位置点 */}
        {animatedLocations.map((location, index) => (
          <div
            key={location.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
            style={{
              left: `${location.x}%`,
              top: `${location.y}%`,
              animation: `scaleIn 0.5s ease-out ${index * 0.1}s both`
            }}
            onMouseEnter={() => setActiveLocation(location.id)}
            onMouseLeave={() => setActiveLocation(null)}
          >
            {/* 脉冲圆环 */}
            <div
              className="absolute inset-0 rounded-full animate-ping"
              style={{
                backgroundColor: getStatusColor(location.status),
                width: '24px',
                height: '24px',
                left: '-12px',
                top: '-12px'
              }}
            />
            
            {/* 主要节点 */}
            <div
              className="relative w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold transition-all duration-300 group-hover:scale-125"
              style={{
                backgroundColor: getStatusColor(location.status),
                color: 'white',
                boxShadow: `0 0 20px ${getStatusColor(location.status)}60`,
                border: `2px solid ${getStatusColor(location.status)}`
              }}
            >
              {getTypeIcon(location.type)}
            </div>

            {/* 悬浮信息卡 */}
            {activeLocation === location.id && (
              <div
                className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 entrance-animation"
                style={{ minWidth: '160px' }}
              >
                <div className="glass-card rounded-lg p-3 text-sm">
                  <div className="font-bold text-white mb-1">{location.name}</div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">状态:</span>
                    <span
                      className="font-medium"
                      style={{ color: getStatusColor(location.status) }}
                    >
                      {location.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">类型:</span>
                    <span className="text-white">{location.type}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">负载:</span>
                    <span className="text-neon-blue font-bold">{location.value}%</span>
                  </div>
                  
                  {/* 连接数 */}
                  {location.connections && (
                    <div className="mt-2 pt-2 border-t border-gray-600">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">连接:</span>
                        <span className="text-neon-green">{location.connections.length}</span>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 箭头 */}
                <div
                  className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
                  style={{
                    borderLeft: '6px solid transparent',
                    borderRight: '6px solid transparent',
                    borderTop: '6px solid rgba(255, 255, 255, 0.1)'
                  }}
                />
              </div>
            )}
          </div>
        ))}

        {/* 扫描效果 */}
        <div className="absolute inset-0 pointer-events-none">
          <div
            className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-neon-cyan to-transparent opacity-60"
            style={{
              animation: 'scanMove 4s ease-in-out infinite',
              top: '20%'
            }}
          />
          <div
            className="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-neon-blue to-transparent opacity-40"
            style={{
              animation: 'scanMove 6s ease-in-out infinite',
              animationDelay: '2s',
              top: '60%'
            }}
          />
        </div>
      </div>

      {/* 统计信息 */}
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <div className="w-2 h-2 bg-neon-green rounded-full mr-2" />
            <span className="text-xs text-gray-400">正常</span>
          </div>
          <div className="text-lg font-bold text-neon-green">
            {animatedLocations.filter(l => l.status === 'normal').length}
          </div>
        </div>
        
        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <div className="w-2 h-2 bg-neon-orange rounded-full mr-2" />
            <span className="text-xs text-gray-400">警告</span>
          </div>
          <div className="text-lg font-bold text-neon-orange">
            {animatedLocations.filter(l => l.status === 'warning').length}
          </div>
        </div>
        
        <div className="glass-card rounded-lg p-3">
          <div className="flex items-center justify-center mb-1">
            <div className="w-2 h-2 bg-neon-red rounded-full mr-2" />
            <span className="text-xs text-gray-400">严重</span>
          </div>
          <div className="text-lg font-bold text-neon-red">
            {animatedLocations.filter(l => l.status === 'critical').length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HolographicMap;