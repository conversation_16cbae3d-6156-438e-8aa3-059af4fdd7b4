-- 添加VMware虚拟化信息管理页面权限
-- 版本: 2.2.4.8
-- 创建时间: 2025-08-14

-- 添加VMware虚拟化信息管理页面到cmdb_pages表
DO $$
DECLARE
    discovery_parent_id INTEGER;
    vmware_page_id INTEGER;
BEGIN
    -- 查找自动发现父页面ID
    SELECT id INTO discovery_parent_id 
    FROM public.cmdb_pages 
    WHERE page_code = 'discovery' AND del_flag = '0' 
    LIMIT 1;
    
    -- 如果找不到discovery，尝试查找其他可能的自动发现页面
    IF discovery_parent_id IS NULL THEN
        SELECT id INTO discovery_parent_id 
        FROM public.cmdb_pages 
        WHERE page_path = '/discovery' AND del_flag = '0' 
        LIMIT 1;
    END IF;
    
    -- 如果还是找不到，尝试查找包含"发现"的页面
    IF discovery_parent_id IS NULL THEN
        SELECT id INTO discovery_parent_id 
        FROM public.cmdb_pages 
        WHERE page_name LIKE '%发现%' AND del_flag = '0' 
        LIMIT 1;
    END IF;
    
    -- 检查VMware虚拟化信息管理页面是否已存在
    SELECT id INTO vmware_page_id 
    FROM public.cmdb_pages 
    WHERE page_code = 'cmdb_vmware_hosts_management' AND del_flag = '0';
    
    -- 如果不存在，则添加VMware虚拟化信息管理页面
    IF vmware_page_id IS NULL THEN
        INSERT INTO public.cmdb_pages 
            (page_code, page_name, page_path, page_description, parent_id, sort_order, 
             created_at, created_by, updated_at, updated_by, del_flag)
        VALUES 
            ('cmdb_vmware_hosts_management', '虚拟化信息更新管理', '/cmdb_vmware_hosts_management', 
             'VMware虚拟化信息自动更新管理', discovery_parent_id, 30, 
             CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, 'admin', '0')
        RETURNING id INTO vmware_page_id;
        
        RAISE NOTICE '已添加VMware虚拟化信息管理页面，ID: %, 父页面ID: %', vmware_page_id, discovery_parent_id;
    ELSE
        RAISE NOTICE 'VMware虚拟化信息管理页面已存在，ID: %', vmware_page_id;
    END IF;
    
    -- 为admin用户添加页面权限
    IF vmware_page_id IS NOT NULL THEN
        -- 检查admin用户是否已有该页面权限
        IF NOT EXISTS (
            SELECT 1 
            FROM public.cmdb_user_page_permissions upp
            JOIN public.cmdb_users u ON upp.user_id = u.id
            WHERE u.username = 'admin' 
            AND upp.page_id = vmware_page_id 
            AND upp.del_flag = '0'
            AND u.del_flag = '0'
        ) THEN
            -- 为admin用户添加页面权限
            INSERT INTO public.cmdb_user_page_permissions 
                (user_id, page_id, created_at, created_by, updated_at, updated_by, del_flag)
            SELECT 
                u.id, vmware_page_id, CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, 'admin', '0'
            FROM public.cmdb_users u
            WHERE u.username = 'admin' AND u.del_flag = '0';
            
            RAISE NOTICE '已为admin用户添加VMware虚拟化信息管理页面权限';
        ELSE
            RAISE NOTICE 'admin用户已有VMware虚拟化信息管理页面权限';
        END IF;
    END IF;
    
END $$;

-- 验证结果
SELECT 
    p.id,
    p.page_code,
    p.page_name,
    p.page_path,
    p.page_description,
    p.parent_id,
    parent.page_name as parent_page_name
FROM public.cmdb_pages p
LEFT JOIN public.cmdb_pages parent ON p.parent_id = parent.id
WHERE p.page_code = 'cmdb_vmware_hosts_management' AND p.del_flag = '0';

-- 验证admin用户权限
SELECT 
    u.username,
    p.page_code,
    p.page_name,
    p.page_path
FROM public.cmdb_user_page_permissions upp
JOIN public.cmdb_users u ON upp.user_id = u.id
JOIN public.cmdb_pages p ON upp.page_id = p.id
WHERE u.username = 'admin' 
AND p.page_code = 'cmdb_vmware_hosts_management'
AND upp.del_flag = '0' 
AND u.del_flag = '0' 
AND p.del_flag = '0';
