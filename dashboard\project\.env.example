# CMDB Dashboard 环境配置文件示例
# 复制此文件为 .env 并填入实际配置值

# 数据库连接配置（参考CMDB系统配置）
VITE_DB_HOST=localhost
VITE_DB_PORT=5432
VITE_DB_NAME=cmdb
VITE_DB_USER=cmdb_user
VITE_DB_PASSWORD=your_database_password

# API基础地址（CMDB后端服务地址）
VITE_API_BASE_URL=http://localhost:8000

# 开发环境配置
VITE_NODE_ENV=development

# 数据刷新间隔（毫秒，默认5分钟）
VITE_DATA_REFRESH_INTERVAL=300000

# 缓存过期时间（毫秒，默认5分钟）
VITE_CACHE_EXPIRY=300000

# 调试模式（开启后会在控制台输出详细日志）
VITE_DEBUG=false

# 生产环境配置示例
# VITE_API_BASE_URL=https://your-cmdb-api.com
# VITE_DB_HOST=your-db-host.com
# VITE_DB_PORT=5432
# VITE_NODE_ENV=production