# Project Structure

## Directory Organization

```
project/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── index.ts         # Component exports
│   │   ├── EnhancedCard.tsx # Glassmorphism card wrapper
│   │   ├── TechProgressBar.tsx # Progress indicators
│   │   ├── AnimatedMetric.tsx # Animated number displays
│   │   └── MonitoringCoverageCarousel.tsx # Metric carousel
│   ├── App.tsx              # Main application component
│   ├── main.tsx             # React app entry point
│   ├── index.css            # Global styles and Tailwind imports
│   └── vite-env.d.ts        # Vite type definitions
├── public/                  # Static assets
├── node_modules/            # Dependencies
└── [config files]          # Build and linting configuration
```

## Component Architecture

### Component Naming
- Use PascalCase for component files (`EnhancedCard.tsx`)
- Export components as default exports
- Use descriptive, feature-based names

### Component Organization
- **Reusable Components**: Place in `/src/components/`
- **Component Index**: Export all components from `components/index.ts`
- **Single Responsibility**: Each component handles one specific UI concern

### File Patterns
- `.tsx` for React components with JSX
- `.ts` for utility functions and types
- `index.ts` for barrel exports

## Data Management

### State Structure
- Component-level state using React hooks
- Real-time data updates with `useEffect` timers
- Metric data organized in typed arrays with consistent interfaces

### Data Types
- Device metrics with `name`, `value`, `total`, `covered/online`, `icon`
- Alert data with `name`, `value`, `trend` indicators
- Consistent percentage calculations and formatting

## Styling Conventions

### Class Organization
- Tailwind utility classes for styling
- Custom CSS classes for complex animations
- Glass effect utilities (`glass-card`, `neon-glow`)
- Responsive design with grid layouts (`grid-cols-12`)

### Animation Standards
- Entrance animations with staggered delays
- Hover effects for interactive elements
- Smooth transitions for state changes
- Custom keyframe animations in Tailwind config