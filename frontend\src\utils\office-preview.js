// Office文件纯前端预览工具
import * as XLSX from 'xlsx'

/**
 * Excel文件预览处理
 * @param {ArrayBuffer} arrayBuffer - 文件数据
 * @param {string} containerId - 容器ID
 */
export const previewExcel = async (arrayBuffer, containerId) => {
  try {
    console.log('🚀 开始Excel预览处理:', { 
      containerId, 
      arrayBufferSize: arrayBuffer.byteLength,
      timestamp: new Date().toISOString()
    })
    
    // 检查XLSX库是否可用
    if (typeof XLSX === 'undefined') {
      console.error('❌ XLSX库未加载')
      throw new Error('XLSX库未加载，请检查依赖')
    }
    
    console.log('✅ XLSX库版本:', XLSX.version)
    
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })
    console.log('✅ Excel工作簿解析成功:', { 
      sheetNames: workbook.SheetNames,
      sheetCount: workbook.SheetNames.length
    })
    
    const container = document.getElementById(containerId)
    
    if (!container) {
      console.error('❌ 预览容器未找到:', containerId)
      // 尝试查找所有可能的容器
      const allContainers = document.querySelectorAll('[id*="excel"]')
      console.log('🔍 找到的Excel相关容器:', Array.from(allContainers).map(el => ({ id: el.id, className: el.className })))
      throw new Error(`预览容器未找到: ${containerId}`)
    }
    
    console.log('✅ 找到预览容器:', {
      id: container.id,
      className: container.className,
      offsetWidth: container.offsetWidth,
      offsetHeight: container.offsetHeight,
      isVisible: container.offsetParent !== null
    })
    
    // 清空容器
    container.innerHTML = ''
    
    // 如果没有工作表，显示错误信息
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #f56c6c;">
          <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
          <h3>Excel文件解析失败</h3>
          <p>未找到有效的工作表</p>
        </div>
      `
      return { success: false, message: '未找到有效的工作表' }
    }
    
    // 创建主容器
    const mainContainer = document.createElement('div')
    mainContainer.style.cssText = `
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #fff;
    `
    
    // 创建工作表标签页（如果有多个工作表）
    let tabsContainer = null
    if (workbook.SheetNames.length > 1) {
      tabsContainer = document.createElement('div')
      tabsContainer.className = 'excel-tabs'
      tabsContainer.style.cssText = `
        border-bottom: 1px solid #dcdfe6;
        margin-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        padding: 10px;
        background: #f8f9fa;
      `
      mainContainer.appendChild(tabsContainer)
    }
    
    // 创建内容容器
    const contentContainer = document.createElement('div')
    contentContainer.className = 'excel-content'
    contentContainer.style.cssText = `
      flex: 1;
      overflow: auto;
      padding: 10px;
    `
    mainContainer.appendChild(contentContainer)
    
    container.appendChild(mainContainer)
    
    // 处理每个工作表
    if (workbook.SheetNames.length > 1 && tabsContainer) {
      workbook.SheetNames.forEach((sheetName, index) => {
        // 创建标签页按钮
        const tabButton = document.createElement('button')
        tabButton.textContent = sheetName
        tabButton.className = `excel-tab ${index === 0 ? 'active' : ''}`
        tabButton.style.cssText = `
          padding: 8px 16px;
          border: 1px solid #dcdfe6;
          background: ${index === 0 ? '#409eff' : '#fff'};
          color: ${index === 0 ? '#fff' : '#606266'};
          cursor: pointer;
          border-radius: 4px;
          font-size: 12px;
          transition: all 0.3s;
          margin-right: 5px;
        `
        
        // 鼠标悬停效果
        tabButton.addEventListener('mouseenter', () => {
          if (!tabButton.classList.contains('active')) {
            tabButton.style.background = '#ecf5ff'
            tabButton.style.borderColor = '#409eff'
          }
        })
        
        tabButton.addEventListener('mouseleave', () => {
          if (!tabButton.classList.contains('active')) {
            tabButton.style.background = '#fff'
            tabButton.style.borderColor = '#dcdfe6'
          }
        })
        
        // 标签页点击事件
        tabButton.addEventListener('click', () => {
          console.log('切换到工作表:', sheetName)
          
          // 更新标签页状态
          document.querySelectorAll('.excel-tab').forEach(tab => {
            tab.style.background = '#fff'
            tab.style.color = '#606266'
            tab.style.borderColor = '#dcdfe6'
            tab.classList.remove('active')
          })
          tabButton.style.background = '#409eff'
          tabButton.style.color = '#fff'
          tabButton.style.borderColor = '#409eff'
          tabButton.classList.add('active')
          
          // 显示对应工作表内容
          showSheet(workbook.Sheets[sheetName], contentContainer, sheetName)
        })
        
        tabsContainer.appendChild(tabButton)
      })
    }
    
    // 默认显示第一个工作表
    if (workbook.SheetNames.length > 0) {
      console.log('显示默认工作表:', workbook.SheetNames[0])
      showSheet(workbook.Sheets[workbook.SheetNames[0]], contentContainer, workbook.SheetNames[0])
    }
    
    console.log('Excel预览处理完成')
    
    return {
      success: true,
      sheetCount: workbook.SheetNames.length,
      sheetNames: workbook.SheetNames
    }
  } catch (error) {
    console.error('Excel预览失败:', error)
    
    // 显示错误信息
    const container = document.getElementById(containerId)
    if (container) {
      container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #f56c6c;">
          <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
          <h3>Excel预览失败</h3>
          <p>${error.message}</p>
          <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
            <h4 style="margin-bottom: 10px; color: #f56c6c;">错误详情：</h4>
            <pre style="font-size: 12px; color: #909399; white-space: pre-wrap;">${error.stack || error.message}</pre>
          </div>
        </div>
      `
    }
    
    throw error
  }
}

/**
 * 显示工作表内容
 * @param {Object} worksheet - 工作表对象
 * @param {HTMLElement} container - 容器元素
 * @param {string} sheetName - 工作表名称
 */
function showSheet(worksheet, container, sheetName = '') {
  try {
    console.log('显示工作表内容:', { sheetName, worksheet })
    
    // 检查工作表是否为空
    if (!worksheet || Object.keys(worksheet).length === 0) {
      showEmptySheet(container, sheetName, '工作表为空')
      return
    }
    
    // 检查工作表是否有有效的数据单元格
    const dataCells = Object.keys(worksheet).filter(key => 
      key.match(/^[A-Z]+[0-9]+$/) && worksheet[key] && worksheet[key].v !== undefined
    )
    
    if (dataCells.length === 0) {
      showEmptySheet(container, sheetName, '工作表没有数据')
      return
    }
    
    console.log('找到数据单元格:', dataCells.length, '个')
    
    // 安全地获取工作表范围
    let range = null
    let rangeRef = worksheet['!ref']
    
    try {
      if (rangeRef && typeof rangeRef === 'string' && rangeRef.trim()) {
        range = XLSX.utils.decode_range(rangeRef)
        console.log('工作表范围解析成功:', rangeRef, range)
      } else {
        // 如果没有范围引用，尝试从数据单元格计算范围
        console.log('工作表缺少范围引用，尝试计算范围')
        range = calculateRangeFromCells(dataCells)
        console.log('计算得到的范围:', range)
      }
    } catch (rangeError) {
      console.warn('范围解析失败:', rangeError.message)
      // 尝试从数据单元格计算范围
      range = calculateRangeFromCells(dataCells)
      console.log('使用计算范围:', range)
    }
    
    // 如果范围仍然无效，显示空内容提示
    if (!range || range.e.r < range.s.r || range.e.c < range.s.c) {
      showEmptySheet(container, sheetName, '工作表范围无效')
      return
    }
    
    // 尝试转换为HTML表格
    let html = ''
    try {
      // 首先尝试使用原始方法
      html = XLSX.utils.sheet_to_html(worksheet, {
        id: `excel-table-${Date.now()}`,
        editable: false,
        header: 1
      })
      console.log('HTML表格生成成功，长度:', html.length)
    } catch (htmlError) {
      console.warn('标准HTML转换失败，尝试备用方法:', htmlError.message)
      
      // 备用方法：手动构建表格
      try {
        html = buildTableManually(worksheet, range)
        console.log('手动构建表格成功，长度:', html.length)
      } catch (manualError) {
        console.error('手动构建表格也失败:', manualError.message)
        showErrorSheet(container, sheetName, '表格生成失败', manualError.message)
        return
      }
    }
    
    // 检查生成的HTML是否有效
    if (!html || html.trim().length === 0) {
      showEmptySheet(container, sheetName, '表格内容为空')
      return
    }
    
    // 添加表格样式
    const styledHtml = `
      <style>
        .excel-table, .excel-table-manual {
          border-collapse: collapse;
          width: auto;
          min-width: 100%;
          font-size: 13px;
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          background: #fff;
          table-layout: auto;
        }
        .excel-table td, .excel-table th,
        .excel-table-manual td, .excel-table-manual th {
          border: 1px solid #dcdfe6;
          padding: 8px 12px;
          text-align: left;
          white-space: pre-wrap;
          word-wrap: break-word;
          min-width: 80px;
          max-width: none;
          width: auto;
          overflow: visible;
          vertical-align: top;
          line-height: 1.4;
        }
        .excel-table th, .excel-table-manual th {
          background-color: #f5f7fa;
          font-weight: bold;
          position: sticky;
          top: 0;
          z-index: 10;
        }
        .excel-table tr:nth-child(even),
        .excel-table-manual tr:nth-child(even) {
          background-color: #fafafa;
        }
        .excel-table tr:hover, .excel-table-manual tr:hover {
          background-color: #ecf5ff;
        }
        .excel-table td:hover, .excel-table-manual td:hover {
          background-color: #e6f7ff;
          cursor: pointer;
        }
      </style>
      <div style="overflow: auto; max-height: 100%; border: 1px solid #e4e7ed; border-radius: 4px;">
        ${html.replace(/id="[^"]*"/g, 'class="excel-table"')}
      </div>
    `
    
    container.innerHTML = styledHtml
    
    // 添加单元格点击事件（显示完整内容）
    const table = container.querySelector('.excel-table, .excel-table-manual')
    if (table) {
      // 智能调整列宽
      adjustColumnWidths(table, worksheet, range)
      
      table.addEventListener('click', (e) => {
        const cell = e.target.closest('td')
        if (cell && cell.textContent.trim()) {
          // 创建一个简单的提示框显示完整内容
          const tooltip = document.createElement('div')
          tooltip.style.cssText = `
            position: fixed;
            background: #303133;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 9999;
            pointer-events: none;
          `
          tooltip.textContent = cell.textContent
          document.body.appendChild(tooltip)
          
          // 定位提示框
          const rect = cell.getBoundingClientRect()
          tooltip.style.left = `${rect.left}px`
          tooltip.style.top = `${rect.bottom + 5}px`
          
          // 3秒后移除提示框
          setTimeout(() => {
            if (tooltip.parentNode) {
              tooltip.parentNode.removeChild(tooltip)
            }
          }, 3000)
        }
      })
    }
    
    console.log('工作表内容显示完成')
    
  } catch (error) {
    console.error('显示工作表内容失败:', error)
    container.innerHTML = `
      <div style="text-align: center; padding: 40px; color: #f56c6c;">
        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
        <h3>工作表显示失败</h3>
        <p style="margin-bottom: 10px;">工作表 "${sheetName}" 解析出错</p>
        <p style="font-size: 12px; color: #909399;">${error.message}</p>
        <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
          <h4 style="margin-bottom: 10px; color: #f56c6c;">错误详情：</h4>
          <pre style="font-size: 11px; color: #909399; white-space: pre-wrap; max-height: 100px; overflow: auto;">${error.stack || error.message}</pre>
        </div>
      </div>
    `
  }
}

/**
 * Word文件预览处理（简化版本）
 * @param {ArrayBuffer} arrayBuffer - 文件数据
 * @param {string} containerId - 容器ID
 */
export const previewWord = async (arrayBuffer, containerId) => {
  try {
    const container = document.getElementById(containerId)
    
    if (!container) {
      throw new Error('预览容器未找到')
    }
    
    // 由于纯前端解析Word文档比较复杂，这里提供一个简化的实现
    // 实际项目中可以使用 mammoth.js 或其他专业库
    
    container.innerHTML = `
      <div style="text-align: center; padding: 40px; color: #909399;">
        <div style="font-size: 48px; margin-bottom: 20px;">📄</div>
        <h3 style="margin-bottom: 10px; color: #606266;">Word文档预览</h3>
        <p style="margin-bottom: 20px;">纯前端Word预览功能正在开发中</p>
        <p style="font-size: 14px; color: #909399;">
          建议使用LibreOffice Online或下载文件查看完整内容
        </p>
        <div style="margin-top: 30px; padding: 15px; background: #f0f9ff; border-radius: 4px; text-align: left;">
          <h4 style="margin-bottom: 10px; color: #409eff;">💡 替代方案：</h4>
          <ul style="margin: 0; padding-left: 20px; color: #606266;">
            <li>使用"LibreOffice Online"预览方案</li>
            <li>使用"ONLYOFFICE"预览方案</li>
            <li>下载文件后使用本地Office软件查看</li>
          </ul>
        </div>
      </div>
    `
    
    return {
      success: true,
      message: 'Word文档预览功能开发中'
    }
  } catch (error) {
    console.error('Word预览失败:', error)
    throw error
  }
}

/**
 * 获取文件的ArrayBuffer
 * @param {string} fileUrl - 文件URL
 */
export const getFileArrayBuffer = async (fileUrl) => {
  try {
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error(`文件下载失败: ${response.status}`)
    }
    return await response.arrayBuffer()
  } catch (error) {
    console.error('获取文件数据失败:', error)
    throw error
  }
}

/**
 * 检查文件类型是否支持纯前端预览
 * @param {string} fileExtension - 文件扩展名
 */
export const isSupportedClientPreview = (fileExtension) => {
  const supportedTypes = ['.xlsx', '.xls']
  return supportedTypes.includes(fileExtension.toLowerCase())
}

/**
 * 显示空工作表
 * @param {HTMLElement} container - 容器元素
 * @param {string} sheetName - 工作表名称
 * @param {string} message - 提示信息
 */
function showEmptySheet(container, sheetName, message) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px; color: #909399;">
      <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
      <h3>${message}</h3>
      <p>工作表 "${sheetName}" 没有可显示的内容</p>
      <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; color: #606266;">
        <p style="margin: 0; font-size: 14px;">💡 这可能是因为：</p>
        <ul style="margin: 10px 0 0 0; padding-left: 20px; text-align: left; display: inline-block;">
          <li>工作表确实为空</li>
          <li>数据格式特殊无法解析</li>
          <li>包含仅格式化信息</li>
        </ul>
      </div>
    </div>
  `
}

/**
 * 显示错误工作表
 * @param {HTMLElement} container - 容器元素
 * @param {string} sheetName - 工作表名称
 * @param {string} title - 错误标题
 * @param {string} errorMessage - 错误信息
 */
function showErrorSheet(container, sheetName, title, errorMessage) {
  container.innerHTML = `
    <div style="text-align: center; padding: 40px; color: #f56c6c;">
      <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
      <h3>${title}</h3>
      <p style="margin-bottom: 10px;">工作表 "${sheetName}" 解析出错</p>
      <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
        <h4 style="margin-bottom: 10px; color: #f56c6c;">错误信息：</h4>
        <p style="font-size: 12px; color: #909399; word-wrap: break-word;">${errorMessage}</p>
        <div style="margin-top: 15px; padding: 10px; background: #fff; border-radius: 4px; border: 1px solid #f5c6cb;">
          <p style="margin: 0; font-size: 14px; color: #721c24;">💡 建议解决方案：</p>
          <ul style="margin: 10px 0 0 0; padding-left: 20px; color: #721c24;">
            <li>尝试重新保存Excel文件</li>
            <li>检查文件是否损坏</li>
            <li>使用其他预览方案</li>
            <li>直接下载文件查看</li>
          </ul>
        </div>
      </div>
    </div>
  `
}

/**
 * 从数据单元格计算工作表范围
 * @param {Array} dataCells - 数据单元格数组
 * @returns {Object} 范围对象
 */
function calculateRangeFromCells(dataCells) {
  if (!dataCells || dataCells.length === 0) {
    return { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }
  }
  
  let minRow = Infinity, maxRow = -1
  let minCol = Infinity, maxCol = -1
  
  dataCells.forEach(cellRef => {
    try {
      const decoded = XLSX.utils.decode_cell(cellRef)
      minRow = Math.min(minRow, decoded.r)
      maxRow = Math.max(maxRow, decoded.r)
      minCol = Math.min(minCol, decoded.c)
      maxCol = Math.max(maxCol, decoded.c)
    } catch (e) {
      console.warn('无法解析单元格引用:', cellRef)
    }
  })
  
  // 如果没有有效的单元格，返回默认范围
  if (minRow === Infinity || maxRow === -1 || minCol === Infinity || maxCol === -1) {
    return { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } }
  }
  
  return {
    s: { r: minRow, c: minCol },
    e: { r: maxRow, c: maxCol }
  }
}

/**
 * 手动构建HTML表格
 * @param {Object} worksheet - 工作表对象
 * @param {Object} range - 范围对象
 * @returns {string} HTML字符串
 */
function buildTableManually(worksheet, range) {
  try {
    console.log('开始手动构建表格，范围:', range)
    
    let html = '<table class="excel-table-manual">'
    
    // 构建表格行
    for (let row = range.s.r; row <= range.e.r; row++) {
      html += '<tr>'
      
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
        const cell = worksheet[cellRef]
        
        // 获取单元格值
        let cellValue = ''
        if (cell) {
          if (cell.v !== undefined) {
            cellValue = String(cell.v)
          } else if (cell.w !== undefined) {
            cellValue = String(cell.w)
          } else if (cell.t === 's' && cell.r) {
            cellValue = cell.r
          }
        }
        
        // 转义HTML特殊字符
        cellValue = cellValue
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;')
        
        // 第一行作为表头
        const tag = row === range.s.r ? 'th' : 'td'
        html += `<${tag}>${cellValue || ''}</${tag}>`
      }
      
      html += '</tr>'
    }
    
    html += '</table>'
    
    console.log('手动构建表格完成')
    return html
    
  } catch (error) {
    console.error('手动构建表格失败:', error)
    throw new Error(`手动构建表格失败: ${error.message}`)
  }
}

/**
 * 智能调整表格列宽
 * @param {HTMLElement} table - 表格元素
 * @param {Object} worksheet - 工作表对象
 * @param {Object} range - 范围对象
 */
function adjustColumnWidths(table, worksheet, range) {
  try {
    console.log('开始智能调整列宽')
    
    if (!table || !worksheet || !range) {
      console.warn('调整列宽参数不完整')
      return
    }
    
    // 获取所有列
    const rows = table.querySelectorAll('tr')
    if (rows.length === 0) {
      console.warn('表格没有行数据')
      return
    }
    
    const colCount = range.e.c - range.s.c + 1
    const columnWidths = new Array(colCount).fill(0)
    
    // 计算每列的最大内容宽度
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 80 // 最小宽度
      const colIndex = col - range.s.c
      
      // 遍历该列的所有单元格
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
        const cell = worksheet[cellRef]
        
        if (cell) {
          let cellValue = ''
          if (cell.v !== undefined) {
            cellValue = String(cell.v)
          } else if (cell.w !== undefined) {
            cellValue = String(cell.w)
          } else if (cell.t === 's' && cell.r) {
            cellValue = cell.r
          }
          
          // 处理换行符，按最长行计算宽度
          const lines = cellValue.split(/\r?\n/)
          let maxLineWidth = 0
          
          lines.forEach(line => {
            let estimatedWidth = 0
            for (let i = 0; i < line.length; i++) {
              const char = line.charAt(i)
              // 简单判断是否为中文字符
              if (char.match(/[\u4e00-\u9fa5]/)) {
                estimatedWidth += 14 // 中文字符稍微增加宽度
              } else if (char.match(/[A-Z]/)) {
                estimatedWidth += 9  // 大写字母稍宽
              } else {
                estimatedWidth += 8  // 普通字符
              }
            }
            maxLineWidth = Math.max(maxLineWidth, estimatedWidth)
          })
          
          // 加上内边距
          maxLineWidth += 24
          
          maxWidth = Math.max(maxWidth, maxLineWidth)
        }
      }
      
      // 设置合理的最大宽度，但允许更宽的内容
      maxWidth = Math.min(maxWidth, 500)
      columnWidths[colIndex] = maxWidth
    }
    
    console.log('计算得到的列宽:', columnWidths)
    
    // 应用列宽到表格
    rows.forEach((row, rowIndex) => {
      const cells = row.querySelectorAll('td, th')
      cells.forEach((cell, cellIndex) => {
        if (cellIndex < columnWidths.length) {
          // 不设置固定宽度，让内容自然展开
          cell.style.minWidth = `${Math.max(80, columnWidths[cellIndex])}px`
          cell.style.width = 'auto'
          cell.style.maxWidth = 'none'
          
          // 确保文本完整显示
          cell.style.whiteSpace = 'pre-wrap'
          cell.style.wordWrap = 'break-word'
          cell.style.overflow = 'visible'
        }
      })
    })
    
    // 让表格自适应内容宽度
    table.style.width = 'auto'
    table.style.minWidth = '100%'
    table.style.tableLayout = 'auto'
    
    console.log('列宽调整完成')
    
  } catch (error) {
    console.error('调整列宽失败:', error)
    // 如果调整失败，使用默认样式
    const rows = table.querySelectorAll('tr')
    rows.forEach(row => {
      const cells = row.querySelectorAll('td, th')
      cells.forEach(cell => {
        cell.style.width = 'auto'
        cell.style.minWidth = '80px'
        cell.style.maxWidth = 'none'
        cell.style.whiteSpace = 'pre-wrap'
        cell.style.wordWrap = 'break-word'
        cell.style.overflow = 'visible'
      })
    })
  }
}