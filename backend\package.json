{"name": "pack", "version": "*******", "description": "CMDB", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "update-version": "node scripts/update-version.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "docx": "^9.5.0", "dotenv": "^16.4.7", "express": "^4.18.2", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "ldapjs": "^3.0.7", "multer": "^2.0.0", "node-cas": "^1.0.1", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "pg": "^8.13.3", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.9"}}