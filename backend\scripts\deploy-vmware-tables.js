/**
 * VMware虚拟化信息表部署脚本
 * 执行数据库表和视图的创建
 */

const fs = require('fs');
const path = require('path');
const { connPG } = require('../db/pg');

async function deployVmwareTables() {
    try {
        console.log('开始部署VMware虚拟化信息表...');

        // 读取SQL脚本文件
        const sqlFilePath = path.join(__dirname, '../../sql/*******/create_vmware_hosts_table.sql');
        
        if (!fs.existsSync(sqlFilePath)) {
            throw new Error(`SQL脚本文件不存在: ${sqlFilePath}`);
        }

        const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
        
        // 执行SQL脚本
        console.log('执行SQL脚本...');
        await connPG.query(sqlScript);
        
        console.log('VMware虚拟化信息表部署成功！');
        
        // 验证表是否创建成功
        const checkTableQuery = `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('cmdb_vmware_info_auto_update', 'cmdb_vmware_sync_logs')
        `;
        
        const result = await connPG.query(checkTableQuery);
        console.log('已创建的表:', result.rows.map(row => row.table_name));
        
        // 验证视图是否创建成功
        const checkViewQuery = `
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public' 
            AND table_name = 'v_cmdb_vmware_info_auto_update'
        `;
        
        const viewResult = await connPG.query(checkViewQuery);
        console.log('已创建的视图:', viewResult.rows.map(row => row.table_name));
        
        console.log('部署验证完成！');
        
    } catch (error) {
        console.error('部署VMware虚拟化信息表失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    deployVmwareTables()
        .then(() => {
            console.log('部署完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('部署失败:', error);
            process.exit(1);
        });
}

module.exports = { deployVmwareTables };