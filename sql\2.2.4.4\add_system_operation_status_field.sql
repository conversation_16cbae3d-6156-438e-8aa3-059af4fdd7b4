-- =====================================================
-- 版本: *******
-- 功能: 为应用系统信息视图添加系统运行状态字段
-- 日期: 2025-08-04
-- 描述: 通过归属业务系统关联 cmdb_system_admin_responsibility_company 表的运行状态字段
-- =====================================================

-- 重建 v_cmdb_application_system_info 视图，添加系统运行状态字段
DROP VIEW IF EXISTS public.v_cmdb_application_system_info CASCADE;

CREATE VIEW public.v_cmdb_application_system_info AS
 SELECT t.id,
    t.management_ip,
    COALESCE(t6.hostname, t5.hostname, ''::text) AS hostname,
    COALESCE(t6.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    COALESCE(t6.admin1, t5.admin1, ''::text) AS server_admin1,
    COALESCE(t6.admin2, t5.admin2, ''::text) AS server_admin2,
    COALESCE(t6.data_center, t5.data_center, ''::text) AS data_center,
        CASE
            WHEN (EXISTS ( SELECT 1
               FROM public.v_cmdb_discovery_results dr
              WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
            ELSE '离线'::text
        END AS machine_usage_status,
    t.remarks,
    t.business_system_name,
    COALESCE(( SELECT u1.real_name
           FROM public.cmdb_users u1
          WHERE (((u1.username)::text = t8.main_admin) AND ((u1.del_flag)::text = '0'::text) AND (u1.real_name IS NOT NULL) AND ((u1.real_name)::text <> ''::text))), ( SELECT u2.real_name
           FROM public.cmdb_users u2
          WHERE (((u2.real_name)::text = t8.main_admin) AND ((u2.del_flag)::text = '0'::text) AND (u2.real_name IS NOT NULL) AND ((u2.real_name)::text <> ''::text))), (t8.main_admin)::character varying) AS system_administrator,
    t8.system_level AS system_classification,
    -- 新增：系统运行状态字段，从cmdb_system_admin_responsibility_company表获取
    COALESCE(t8.system_operation_status, ''::text) AS system_operation_status,
        CASE
            WHEN (t.monitoring_requirement = true) THEN '是'::text
            WHEN (t.monitoring_requirement = false) THEN '否'::text
            ELSE '否'::text
        END AS monitoring_requirement,
    t.monitoring_requirement_description,
        CASE
            WHEN (t9.ip_address IS NOT NULL) THEN '是'::text
            ELSE '否'::text
        END AS is_monitored,
    t.deployed_applications,
    COALESCE(t2.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t3.dict_name, t.master_slave_role) AS master_slave_role,
    COALESCE(t4.dict_name, t.backup_mode) AS backup_mode,
    t.internet_ip,
    t.internet_port,
    t.related_master_slave_ips,
    COALESCE(t6.operating_system, t5.operating_system, ''::text) AS operating_system,
    -- 设备生命周期字段：优先从虚拟机表获取，其次从服务器表获取
    COALESCE(t6.operation_status, t5.operation_status, ''::text) AS operation_status,
    COALESCE(t.has_antivirus_software, '是'::character varying) AS has_antivirus_software,
    COALESCE(t.patch_update_configured, '是'::character varying) AS patch_update_configured,
        CASE
            WHEN (t8.system_level = '一级'::text) THEN '有一级系统管理员'::text
            WHEN (t8.system_level = '二级'::text) THEN '有二级系统管理员'::text
            ELSE ''::text
        END AS has_system_administrator,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
   FROM (((((((public.cmdb_application_system_info t
     LEFT JOIN public.cmdb_data_dictionary t2 ON ((((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.production_attributes)::text))))
     LEFT JOIN public.cmdb_data_dictionary t3 ON ((((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.master_slave_role)::text))))
     LEFT JOIN public.cmdb_data_dictionary t4 ON ((((t4.del_flag)::text = '0'::text) AND ((t4.dict_code)::text = (t.backup_mode)::text))))
     LEFT JOIN ( SELECT t5_1.management_ip,
            max((t5_1.hostname)::text) AS hostname,
            max((t5_1.function_purpose)::text) AS function_purpose,
            max((t5_1.admin1)::text) AS admin1,
            max((t5_1.admin2)::text) AS admin2,
            max((t5_1.data_center)::text) AS data_center,
            max((t5_1.operating_system)::text) AS operating_system,
            -- 从服务器管理表获取设备生命周期字段
            max((t5_1.operation_status)::text) AS operation_status
           FROM public.v_cmdb_server_management t5_1
          GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t6_1.management_ip,
            max((t6_1.hostname)::text) AS hostname,
            max((t6_1.function_purpose)::text) AS function_purpose,
            max((t6_1.admin1)::text) AS admin1,
            max((t6_1.admin2)::text) AS admin2,
            max((t6_1.data_center1)::text) AS data_center,
            max((t6_1.operating_system)::text) AS operating_system,
            -- 从虚拟机登记表获取设备生命周期字段
            max((t6_1.operation_status)::text) AS operation_status
           FROM public.v_cmdb_vm_registry t6_1
          GROUP BY t6_1.management_ip) t6 ON (((t6.management_ip)::text = (t.management_ip)::text)))
     LEFT JOIN ( SELECT t8_1.system_abbreviation,
            max((t8_1.main_admin)::text) AS main_admin,
            max((COALESCE(t82.dict_name, t8_1.system_level))::text) AS system_level,
            -- 新增：从cmdb_system_admin_responsibility_company表获取系统运行状态
            max((COALESCE(t83.dict_name, t8_1.operation_status))::text) AS system_operation_status
           FROM ((public.cmdb_system_admin_responsibility_company t8_1
             LEFT JOIN public.cmdb_data_dictionary t82 ON ((((t82.del_flag)::text = '0'::text) AND ((t82.dict_code)::text = (t8_1.system_level)::text))))
             LEFT JOIN public.cmdb_data_dictionary t83 ON ((((t83.del_flag)::text = '0'::text) AND ((t83.dict_code)::text = (t8_1.operation_status)::text))))
          WHERE ((t8_1.del_flag)::text = '0'::text)
          GROUP BY t8_1.system_abbreviation) t8 ON (((t8.system_abbreviation)::text = (t.business_system_name)::text)))
     LEFT JOIN ( SELECT t9_1.ip_address
           FROM public.cmdb_monitored_ip_list t9_1
          WHERE ((t9_1.del_flag)::text = '0'::text)
          GROUP BY t9_1.ip_address) t9 ON (((t9.ip_address)::text = (t.management_ip)::text)))
  WHERE ((t.del_flag)::text = '0'::text);

-- 设置视图所有者
ALTER VIEW public.v_cmdb_application_system_info OWNER TO postgres;

-- 添加视图注释
COMMENT ON VIEW public.v_cmdb_application_system_info IS '应用系统信息视图 - 包含设备生命周期字段和系统运行状态字段';

-- 授予权限
GRANT SELECT ON TABLE public.v_cmdb_application_system_info TO cjmonitor;

-- 输出成功信息
DO $$
BEGIN
    RAISE NOTICE '应用系统信息视图系统运行状态字段添加完成！';
    RAISE NOTICE '版本: *******';
    RAISE NOTICE '功能: 通过归属业务系统关联cmdb_system_admin_responsibility_company表的运行状态字段';
    RAISE NOTICE '新增字段: system_operation_status (系统运行状态)';
    RAISE NOTICE '关联逻辑: 通过business_system_name关联system_abbreviation';
    RAISE NOTICE '视图更新: v_cmdb_application_system_info 包含 system_operation_status 字段';
END $$;