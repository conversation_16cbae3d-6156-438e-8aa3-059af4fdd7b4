/**
 * VMware虚拟化主机管理API路由
 * 提供VMware主机数据的API接口
 */

const express = require('express');
const router = express.Router();
const vmwareHostsController = require('../controllers/vmware/vmware-hosts-controller');

// VMware主机数据管理API
router.post('/get_vmware_hosts', vmwareHostsController.getVmwareHostsList);
router.post('/get_vmware_host_detail', vmwareHostsController.getVmwareHostDetail);
router.post('/delete_vmware_host', vmwareHostsController.deleteVmwareHost);
router.post('/batch_delete_vmware_hosts', vmwareHostsController.batchDeleteVmwareHosts);
router.post('/export_vmware_hosts', vmwareHostsController.exportVmwareHosts);

// VMware数据同步管理API
router.post('/trigger_vmware_sync', vmwareHostsController.triggerSync);
router.post('/get_vmware_sync_status', vmwareHostsController.getSyncStatus);
router.post('/get_vmware_sync_history', vmwareHostsController.getSyncHistory);

// VMware统计信息API
router.post('/get_vmware_statistics', vmwareHostsController.getStatistics);

module.exports = router;