/**
 * VMware虚拟化主机管理API路由
 * 提供VMware主机数据的API接口
 */

const express = require('express');
const router = express.Router();
const vmwareHostsController = require('../controllers/vmware/vmware-hosts-controller');

// VMware主机数据管理API
router.post('/get_vmware_hosts', (req, res) => vmwareHostsController.getVmwareHostsList(req, res));
router.post('/get_vmware_host_detail', (req, res) => vmwareHostsController.getVmwareHostDetail(req, res));
router.post('/delete_vmware_host', (req, res) => vmwareHostsController.deleteVmwareHost(req, res));
router.post('/batch_delete_vmware_hosts', (req, res) => vmwareHostsController.batchDeleteVmwareHosts(req, res));
router.post('/export_vmware_hosts', (req, res) => vmwareHostsController.exportVmwareHosts(req, res));

// VMware数据同步管理API
router.post('/trigger_vmware_sync', (req, res) => vmwareHostsController.triggerSync(req, res));
router.post('/get_vmware_sync_status', (req, res) => vmwareHostsController.getSyncStatus(req, res));
router.post('/get_vmware_sync_history', (req, res) => vmwareHostsController.getSyncHistory(req, res));

// VMware统计信息API
router.post('/get_vmware_statistics', (req, res) => vmwareHostsController.getStatistics(req, res));

module.exports = router;