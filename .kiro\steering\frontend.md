---
description: 
globs: *.vue/*.js/
alwaysApply: false
---
# CMDB v2.0 前端开发规则

## 项目概述
这是一个CMDB（配置管理数据库）v2.0项目的前端部分，采用Vue.js 3.x + Element Plus + Vite技术栈。

## 技术栈
- **框架**: Vue.js 3.3.4
- **UI库**: Element Plus 2.9.6 (中文版)
- **路由**: Vue Router 4.2.5
- **HTTP客户端**: Axios 1.6.8
- **构建工具**: Vite 6.2.2
- **样式预处理**: Sass 1.85.1
- **文件操作**: file-saver 2.0.5, xlsx 0.18.5
- **工具库**: uuid 11.1.0

## 项目结构

### 核心文件
- **入口文件**: [main.js](mdc:frontend/src/main.js) - 应用程序入口
- **根组件**: [App.vue](mdc:frontend/src/App.vue) - 根Vue组件
- **构建配置**: [vite.config.js](mdc:frontend/vite.config.js) - Vite配置
- **依赖管理**: [package.json](mdc:frontend/package.json) - 项目依赖

### 目录结构
```
frontend/src/
├── api/              # API接口定义
├── assets/           # 静态资源
├── components/       # 公共组件
├── config/           # 配置文件
├── router/           # 路由配置
├── utils/            # 工具函数
├── views/            # 页面组件
├── main.js           # 应用入口
└── App.vue           # 根组件
```

### 环境配置
- **开发环境**: [.env.development](mdc:frontend/.env.development)
- **生产环境**: [.env.production](mdc:frontend/.env.production)
- **通用配置**: [.env](mdc:frontend/.env)

## 页面组织规范

### 页面命名规范
- **资产管理页面**: `cmdb_` 前缀
  - 例：`cmdb_server_management.vue`、`cmdb_device_management.vue`
- **AI平台页面**: `AI_` 前缀
  - 例：`ai_platform.vue`、`ai_document_editor.vue`
- **运维管理页面**: `ops_` 前缀
  - 例：`ops_calendar.vue`、`ops_event_management/`
- **报表页面**: `report_` 前缀
  - 例：`report_server_age.vue`、`report_network_device_age.vue`

### 页面文件结构
```vue
<template>
  <!-- 页面模板 -->
</template>

<script>
export default {
  name: 'ComponentName',
  // 组件逻辑
}
</script>

<style lang="scss" scoped>
/* 页面样式 */
</style>
```

## Vue 3 开发规范

### 组合式API（Composition API）
推荐使用组合式API进行开发：
```javascript
<script setup>
import { ref, reactive, computed, onMounted } from 'vue'

// 响应式数据
const data = reactive({
  list: [],
  loading: false
})

// 计算属性
const filteredList = computed(() => {
  return data.list.filter(item => item.active)
})

// 生命周期
onMounted(() => {
  fetchData()
})
</script>
```

### 组件定义规范
```javascript
// 组件名使用PascalCase
export default {
  name: 'CmdbServerManagement',
  components: {
    // 局部组件注册
  },
  props: {
    // 属性定义
    serverId: {
      type: Number,
      required: true
    }
  },
  emits: ['update', 'delete'],
  setup(props, { emit }) {
    // 组合式API逻辑
  }
}
```

## Element Plus 使用规范

### 基础组件使用
```vue
<template>
  <!-- 表单组件 -->
  <el-form :model="form" :rules="rules" ref="formRef">
    <el-form-item label="服务器名称" prop="hostname">
      <el-input 
        v-model="form.hostname" 
        placeholder="请输入服务器名称"
        clearable
      />
    </el-form-item>
    
    <!-- 下拉选择 -->
    <el-form-item label="服务器类型" prop="serverType">
      <el-select 
        v-model="form.serverType" 
        placeholder="请选择服务器类型"
        clearable
        filterable
      >
        <el-option 
          v-for="item in serverTypes" 
          :key="item.value"
          :label="item.label" 
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
  
  <!-- 数据表格 -->
  <el-table :data="tableData" stripe border>
    <el-table-column prop="hostname" label="主机名" />
    <el-table-column prop="ipAddress" label="IP地址" />
    <el-table-column label="操作" width="200">
      <template #default="{ row }">
        <el-button type="primary" size="small" @click="editRow(row)">
          编辑
        </el-button>
        <el-button type="danger" size="small" @click="deleteRow(row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
```

### 统一UI规范
```scss
// 统一按钮风格
.page-buttons {
  .el-button {
    margin-right: 10px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 统一表格风格
.page-table {
  .el-table {
    border: 1px solid #dcdfe6;
    
    .el-table__header-wrapper {
      background-color: #f5f7fa;
    }
  }
}

// 统一表单风格
.page-form {
  .el-form-item {
    margin-bottom: 22px;
    
    .el-form-item__label {
      font-weight: 500;
    }
  }
}
```

## 组件开发规范

### 公共组件
公共组件放在[components/](mdc:frontend/src/components)目录：
```javascript
// components/CommonTable.vue
<template>
  <el-table 
    :data="data" 
    :loading="loading"
    stripe 
    border
    style="width: 100%"
  >
    <slot></slot>
  </el-table>
</template>

<script setup>
defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
</script>
```

### 组件通信
```javascript
// 父子组件通信
// 父组件
<ChildComponent 
  :prop-data="data" 
  @child-event="handleChildEvent"
/>

// 子组件
const emit = defineEmits(['child-event'])
const props = defineProps(['propData'])

emit('child-event', eventData)
```

## API调用规范

### API服务封装
在[api/](mdc:frontend/src/api)目录中按模块组织API：
```javascript
// api/server.js
import axios from '@/utils/request'

export const serverApi = {
  // 获取服务器列表
  getServerList(params) {
    return axios.get('/api/cmdb/servers', { params })
  },
  
  // 创建服务器
  createServer(data) {
    return axios.post('/api/cmdb/servers', data)
  },
  
  // 更新服务器
  updateServer(id, data) {
    return axios.put(`/api/cmdb/servers/${id}`, data)
  },
  
  // 删除服务器
  deleteServer(id) {
    return axios.delete(`/api/cmdb/servers/${id}`)
  }
}
```

### Axios配置
```javascript
// utils/request.js
import axios from 'axios'
import { ElMessage } from 'element-plus'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    if (data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(data)
    }
  },
  error => {
    ElMessage.error(error.message || '网络错误')
    return Promise.reject(error)
  }
)

export default request
```

## 路由管理

### 路由配置
```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/home.vue'),
    meta: { 
      title: '首页',
      requiresAuth: true 
    }
  },
  {
    path: '/cmdb/servers',
    name: 'CmdbServerManagement',
    component: () => import('@/views/cmdb_server_management.vue'),
    meta: { 
      title: '服务器管理',
      requiresAuth: true 
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next('/login')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
```

## 状态管理

### 全局状态
```javascript
// utils/store.js
import { reactive } from 'vue'

export const globalStore = reactive({
  user: {
    id: null,
    username: '',
    role: ''
  },
  
  setUser(userData) {
    this.user = { ...userData }
  },
  
  clearUser() {
    this.user = {
      id: null,
      username: '',
      role: ''
    }
  }
})
```

## 工具函数

### 日期时间处理
```javascript
// utils/datetime.js
export const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

export const formatDateTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const dateStr = formatDate(date)
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${dateStr} ${hours}:${minutes}:${seconds}`
}
```

### 文件操作
```javascript
// utils/file.js
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'

// 导出Excel
export const exportToExcel = (data, filename = 'export.xlsx') => {
  const ws = XLSX.utils.json_to_sheet(data)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
  const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
  const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
  saveAs(blob, filename)
}

// 文件上传
export const uploadFile = (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return axios.post('/api/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: onProgress
  })
}
```

## 样式规范

### 全局样式
```scss
// assets/styles/global.scss
// 统一页面布局
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #303133;
  }
}

.page-content {
  background-color: white;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .page-content {
    padding: 15px;
  }
}
```

### 组件样式隔离
```vue
<style lang="scss" scoped>
.server-management {
  .search-form {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .action-buttons {
    margin-bottom: 15px;
    
    .el-button {
      margin-right: 10px;
    }
  }
  
  .data-table {
    .status-tag {
      &.active {
        background-color: #67c23a;
      }
      
      &.inactive {
        background-color: #f56c6c;
      }
    }
  }
}
</style>
```

## 性能优化

### 路由懒加载
```javascript
const routes = [
  {
    path: '/cmdb/servers',
    component: () => import('@/views/cmdb_server_management.vue')
  }
]
```

### 组件懒加载
```javascript
import { defineAsyncComponent } from 'vue'

const AsyncComponent = defineAsyncComponent(() =>
  import('./components/HeavyComponent.vue')
)
```

### 列表虚拟化
```vue
<template>
  <el-table-v2
    :columns="columns"
    :data="data"
    :width="700"
    :height="400"
    fixed
  />
</template>
```

## 错误处理

### 全局错误处理
```javascript
// main.js
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err, info)
  ElMessage.error('系统错误，请稍后重试')
}
```

### 组件错误边界
```vue
<script setup>
import { onErrorCaptured } from 'vue'

onErrorCaptured((err, instance, info) => {
  console.error('Component error:', err, info)
  return false // 阻止错误继续传播
})
</script>
```

## 开发调试

### 开发环境配置
```javascript
// vite.config.js
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

### 登录功能
- 支持本地用户密码验证登录
- 支持本地用户使用远程ldap服务器验证登录
- 使用本地用户权限，ldap服务只验证登录

### 导出文件格式
word 格式标准
- 主标题："事件管理报告" - 改为黑体三号黑色
- 二级标题："基本信息"、"事件描述及经过"等 - 改为黑体四号黑色
- 表格内容 - 全部改为宋体小四黑色，并添加1.0倍行间距
- 正文段落 - 宋体小四黑色，1.5倍行间距
- 页脚 - 宋体五号灰色，1.5倍行间距

### 调试工具
- Vue DevTools
- 浏览器开发者工具
- Vite热更新
- Console日志调试




