<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span v-if="mode === 'new'">新增事件</span>
          <span v-else-if="mode === 'edit'">编辑事件</span>
          <span v-else>事件详情</span>
          <div class="header-buttons">
            <el-button type="primary" @click="handleCancel">返回列表</el-button>
            <el-button 
              v-if="mode !== 'view'"
              type="success" 
              @click="handleSubmit"
              :loading="submitLoading"
            >
              {{ mode === 'new' ? '创建' : '保存' }}
            </el-button>
            <el-button 
              v-if="mode !== 'new' && formData.id"
              type="warning" 
              @click="exportToWord"
              :icon="Document"
            >
              导出事件记录
            </el-button>
          </div>
        </div>
      </template>

      <!-- 主要内容区域 - 左右分栏布局 -->
      <div class="main-content">
        <!-- 左侧：表单填写区域 -->
        <div class="left-panel">
          <!-- 事件编号显示 -->
          <div class="event-id-section">
            <span class="event-id-text">
              事件编号：
              <span v-if="mode === 'new'" class="auto-generate-text">系统自动生成</span>
              <span v-else>{{ formData.event_id || '系统自动生成' }}</span>
            </span>
          </div>

          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="140px"
            v-loading="formLoading"
            class="event-form"
          >
            <!-- 基本信息区域 -->
            <div class="form-section">
              <div class="section-title">
                <el-icon><InfoFilled /></el-icon>
                <span>基本信息</span>
              </div>
              
              <el-form-item label="事件标题" prop="title" class="required-field">
                <el-input
                  v-model="formData.title"
                  :disabled="mode === 'view'"
                  placeholder="请输入事件标题"
                  size="default"
                />
              </el-form-item>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="影响系统" prop="system" class="required-field">
                    <el-select
                      v-model="formData.system"
                      :disabled="mode === 'view'"
                      placeholder="请选择影响系统"
                      style="width: 100%"
                      filterable
                      clearable
                      size="default"
                    >
                      <el-option
                        v-for="item in systemOptions"
                        :key="item.system_abbreviation"
                        :label="item.system_abbreviation"
                        :value="item.system_abbreviation"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="事件级别" prop="priority" class="required-field">
                    <el-select
                      v-model="formData.priority"
                      :disabled="mode === 'view'"
                      placeholder="请选择事件级别"
                      style="width: 100%"
                      filterable
                      clearable
                      size="default"
                    >
                      <el-option
                        v-for="item in priorityOptions"
                        :key="item.dict_code"
                        :label="item.dict_name"
                        :value="item.dict_code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="首次发生时间" prop="report_time" class="required-field">
                    <el-date-picker
                      v-model="formData.report_time"
                      :disabled="mode === 'view'"
                      type="datetime"
                      placeholder="选择报告时间"
                      style="width: 100%"
                      format="YYYY-MM-DD HH:mm:ss"
                      size="default"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="值班经理" prop="reporter" class="required-field">
                    <el-select
                      v-model="formData.reporter"
                      :disabled="mode === 'view'"
                      placeholder="请选择值班经理"
                      style="width: 100%"
                      filterable
                      clearable
                      size="default"
                    >
                      <el-option
                        v-for="item in userOptions"
                        :key="item.username"
                        :label="item.real_name"
                        :value="item.username"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="处理人" prop="assignee" class="required-field">
                <el-select
                  v-model="formData.assignee"
                  :disabled="mode === 'view'"
                  placeholder="请选择处理人（可多选）"
                  style="width: 100%"
                  filterable
                  clearable
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  size="default"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.username"
                    :label="item.real_name"
                    :value="item.username"
                  />
                </el-select>
                <!-- 处理人标签展示 -->
                <div v-if="formData.assignee && formData.assignee.length > 0" class="tag-display">
                  <el-tag
                    v-for="username in formData.assignee"
                    :key="username"
                    class="display-tag"
                    type="primary"
                    effect="light"
                    size="small"
                  >
                    {{ getUserRealName(username) }}
                  </el-tag>
                </div>
              </el-form-item>
            </div>

            <!-- 详细描述区域 -->
            <div class="form-section">
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>详细描述</span>
              </div>
                <el-form-item label="事件简要经过" prop="description" class="required-field">
                <el-input
                  v-model="formData.description"
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="8"
                  placeholder="请详细描述事件的具体情况、影响范围等"
                  show-word-limit
                  maxlength="2000"
                />
                <div v-if="mode !== 'view'" class="auto-fill-button-container">
                  <el-button 
                    size="small" 
                    type="info" 
                    plain 
                    @click="fillTemplateContent('description')"
                    class="auto-fill-button"
                  >
                    自动填充
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="事件影响范围、影响程度、影响人数、直接资金损失情况">
                <el-input
                  v-model="formData.process"
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="8"
                  placeholder="请详细描述事件影响范围、影响程度、影响人数、直接资金损失情况"
                  show-word-limit
                  maxlength="2000"
                />
                <div v-if="mode !== 'view'" class="auto-fill-button-container">
                  <el-button 
                    size="small" 
                    type="info" 
                    plain 
                    @click="fillTemplateContent('process')"
                    class="auto-fill-button"
                  >
                    自动填充
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="事件导致的后果、发生原因和事件性质判断">
                <el-input
                  v-model="formData.consequence_cause_analysis"
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="8"
                  placeholder="请详细描述事件导致的后果、发生原因和事件性质判断"
                  show-word-limit
                  maxlength="2000"
                />
                <div v-if="mode !== 'view'" class="auto-fill-button-container">
                  <el-button 
                    size="small" 
                    type="info" 
                    plain 
                    @click="fillTemplateContent('consequence')"
                    class="auto-fill-button"
                  >
                    自动填充
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="已采取的措施及效果">
                <el-input
                  v-model="formData.solution"
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="8"
                  placeholder="请详细记录采取的应急措施、处理步骤及实际效果"
                  show-word-limit
                  maxlength="2000"
                />
                <div v-if="mode !== 'view'" class="auto-fill-button-container">
                  <el-button 
                    size="small" 
                    type="info" 
                    plain 
                    @click="fillTemplateContent('solution')"
                    class="auto-fill-button"
                  >
                    自动填充
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="总结及知识库">
                <el-input
                  v-model="formData.improvement_plan"
                  :disabled="mode === 'view'"
                  type="textarea"
                  :rows="8"
                  placeholder="请总结事件处理经验、教训及形成的知识库内容"
                  show-word-limit
                  maxlength="2000"
                />
                <div v-if="mode !== 'view'" class="auto-fill-button-container">
                  <el-button 
                    size="small" 
                    type="info" 
                    plain 
                    @click="fillTemplateContent('improvement')"
                    class="auto-fill-button"
                  >
                    自动填充
                  </el-button>
                </div>
              </el-form-item>

            </div>

            <!-- 文件附件组件 - 暂时注释，保留代码便于后期使用 -->
            <!-- 
            <FileAttachments
              v-if="formData.id"
              :event-id="formData.id"
              :disabled="mode === 'view'"
              @files-updated="handleFilesUpdated"
            />
            -->
          </el-form>

          <!-- 删除按钮区域 -->
          <div v-if="formData.id && hasDeletePermission" class="delete-section">
            <el-divider />
            <div class="delete-button-container">
              <el-button
                type="danger"
                :loading="deleteLoading"
                @click="handleDelete"
                class="delete-button"
              >
                <el-icon><Delete /></el-icon>
                删除事件记录
              </el-button>
            </div>
          </div>
        </div>

        <!-- 右侧：模板样例展示区域 -->
        <div class="right-panel">

          <!-- 分隔线区域 -->
          <div class="template-separator">
            <el-divider content-position="center">
              <el-icon><Document /></el-icon>
              <span style="margin-left: 8px;">事件记录模板样例</span>
            </el-divider>
          </div>
          
          <div class="template-content">
            <!-- 竖向平铺展示所有模板内容 -->
            
            <!-- 模板1：事件简要经过模板 -->
            <div class="template-section">
              <el-card shadow="hover" class="template-card">
                <template #header>
                  <div class="template-card-header">
                    <el-icon><Edit /></el-icon>
                    <span>事件简要经过模板</span>
                  </div>
                </template>
                  <div class="template-example">
                    <pre>{{ eventTemplates.description.content }}</pre>
                  </div>
              </el-card>
            </div>
            
            <!-- 模板2：事件影响范围、程度、人数、资金损失情况模板 -->
            <div class="template-section">
              <el-card shadow="hover" class="template-card">
                <template #header>
                  <div class="template-card-header">
                    <el-icon><CircleCheck /></el-icon>
                    <span>事件影响范围、程度、人数、资金损失情况模板</span>
                  </div>
                </template>
                  <div class="template-example">
                    <pre>{{ eventTemplates.process.content }}</pre>
                  </div>
              </el-card>
            </div>

            <!-- 模板3：事件后果、原因和性质判断模板 -->
            <div class="template-section">
              <el-card shadow="hover" class="template-card">
                <template #header>
                  <div class="template-card-header">
                    <el-icon><Search /></el-icon>
                    <span>事件后果、原因和性质判断模板</span>
                  </div>
                </template>
                  <div class="template-example">
                    <pre>{{ eventTemplates.consequence.content }}</pre>
                  </div>
              </el-card>
            </div>
            
            <!-- 模板4：已采取的措施及效果模板 -->
            <div class="template-section">
              <el-card shadow="hover" class="template-card">
                <template #header>
                  <div class="template-card-header">
                    <el-icon><Setting /></el-icon>
                    <span>已采取的措施及效果模板</span>
                  </div>
                </template>
                  <div class="template-example">
                    <pre>{{ eventTemplates.solution.content }}</pre>
                  </div>
              </el-card>
            </div>
            
            <!-- 模板5：总结及知识库模板 -->
            <div class="template-section">
              <el-card shadow="hover" class="template-card">
                <template #header>
                  <div class="template-card-header">
                    <el-icon><FolderOpened /></el-icon>
                    <span>总结及知识库模板</span>
                  </div>
                </template>
                  <div class="template-example">
                    <pre>{{ eventTemplates.improvement.content }}</pre>
                  </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部附件管理区域 -->
      <div class="bottom-attachment-section">
        <el-divider content-position="center">
          <el-icon><Document /></el-icon>
          <span style="margin-left: 8px;">事件分析报告管理</span>
        </el-divider>
        
        <el-row :gutter="24">
          <!-- 左侧：事件分析报告附件上传 -->
          <el-col :span="12">
            <div class="attachment-upload-panel">
              <div class="panel-title">
                <el-icon><UploadFilled /></el-icon>
                <span>事件分析报告附件上传</span>
              </div>
              
              <!-- 事件未保存时的提示 -->
              <div v-if="mode === 'new' || !formData.id || !formData.event_id || formData.event_id === '系统自动生成'" class="upload-disabled-tip">
                <el-alert
                  title="请先保存事件记录"
                  description="需要先保存事件并生成事件编号后，才能上传分析报告文件"
                  type="info"
                  :closable="false"
                  show-icon>
                </el-alert>
              </div>
              
              <div v-else-if="mode !== 'view'" class="upload-area">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :multiple="false"
                  :show-file-list="true"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :before-upload="beforeUpload"
                  accept=".doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.txt"
                  drag
                  class="analysis-upload"
                >
                  <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                  <div class="el-upload__text">
                    将分析报告文件拖到此处，或<em>点击上传</em>
                  </div>
                  <div class="el-upload__tip">
                    支持 doc/docx/pdf/xls/xlsx/ppt/pptx/txt 格式，文件大小不超过10MB
                  </div>
                </el-upload>
                
                <div v-if="uploadedFiles.length > 0" class="uploaded-files">
                  <div class="file-list-header">已上传的文件：</div>
                  <div 
                    v-for="file in uploadedFiles" 
                    :key="file.name"
                    class="file-item"
                  >
                    <el-icon><Document /></el-icon>
                    <span class="file-name">{{ file.name }}</span>
                    <div class="file-actions">
                      <el-button 
                        size="small" 
                        type="primary" 
                        text 
                        @click="downloadUploadedFile(file)"
                        :icon="Download"
                      >
                        下载
                      </el-button>
                      <el-button 
                        v-if="mode !== 'view'"
                        size="small" 
                        type="danger" 
                        text 
                        @click="removeUploadedFile(file)"
                        :icon="Delete"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
                
                <div class="upload-actions" v-if="pendingFiles.length > 0">
                  <el-button 
                    type="success" 
                    size="small"
                    @click="submitUpload"
                    :loading="uploadLoading"
                  >
                    确认上传
                  </el-button>
                  <el-button 
                    size="small"
                    @click="clearPendingFiles"
                  >
                    清空
                  </el-button>
                </div>
              </div>
              
              <!-- 查看模式下显示已上传文件 -->
              <div v-if="mode === 'view' && uploadedFiles.length > 0" class="uploaded-files">
                <div class="file-list-header">已上传的文件：</div>
                <div 
                  v-for="file in uploadedFiles" 
                  :key="file.name"
                  class="file-item"
                >
                  <el-icon><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <div class="file-actions">
                    <el-button 
                      size="small" 
                      type="primary" 
                      text 
                      @click="downloadUploadedFile(file)"
                      :icon="Download"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          
          <!-- 右侧：模板下载 -->
          <el-col :span="12">
            <div class="template-download-panel">
              <div class="panel-title">
                <el-icon><Download /></el-icon>
                <span>下载模板</span>
              </div>
              
              <div class="download-content">
                <div class="download-description">
                  <p>下载事件分析报告模板，用于编写标准化的事件分析报告。</p>
                </div>
                
                <div class="download-buttons">
                  <el-button 
                    size="default" 
                    type="primary" 
                    @click="downloadAnalysisTemplate"
                    :icon="Document"
                    class="download-button"
                  >
                    下载事件分析报告模板
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Document, InfoFilled, User, UploadFilled, Download, Edit, CircleCheck, Search, Setting, FolderOpened } from '@element-plus/icons-vue'
import request from '@/utils/request'
import { safeFormatDateOnly } from '@/utils/dateUtils'
import { downloadFromApi } from '@/utils/downloadUtils'
// import FileAttachments from './components/FileAttachments.vue' // 暂时注释，保留代码便于后期使用

export default {
  name: 'EventManagementDetail',
  components: {
    // FileAttachments, // 暂时注释，保留代码便于后期使用
    Delete,
    Document,
    InfoFilled,
    User,
    UploadFilled,
    Download,
    Edit,
    CircleCheck,
    Search,
    Setting,
    FolderOpened
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)

    // 计算当前模式
    const mode = computed(() => {
      if (route.params.id === 'new') {
        return 'new'
      } else if (route.query.mode === 'view') {
        return 'view'
      } else {
        return 'edit'
      }
    })

    // 表单数据
    const formData = reactive({
      id: null,
      event_id: '',
      title: '',
      priority: 'U00002',
      system: '',
      reporter: '',
      assignee: [], // 改为数组，支持多选
      report_time: '',
      description: '',
      process: '',
      solution: '',
      improvement_plan: '',
      consequence_cause_analysis: ''
    })

    // 表单验证规则
    const formRules = {
      title: [
        { required: true, message: '请输入事件标题', trigger: 'blur' }
      ],
      priority: [
        { required: true, message: '请选择事件级别', trigger: 'change' }
      ],
      system: [
        { required: true, message: '请选择影响系统', trigger: 'change' }
      ],
      reporter: [
        { required: true, message: '请选择报告人', trigger: 'change' }
      ],
      assignee: [
        { required: true, message: '请选择处理人', trigger: 'change' }
      ],
      report_time: [
        { required: true, message: '请选择报告时间', trigger: 'change' }
      ],
      description: [
        { required: true, message: '请输入事件描述', trigger: 'blur' }
      ]
    }

    // 选项数据
    const priorityOptions = ref([])
    const systemOptions = ref([])
    const userOptions = ref([])

    // 状态控制
    const formLoading = ref(false)
    const submitLoading = ref(false)
    const deleteLoading = ref(false)

    // 文件上传相关
    const uploadRef = ref(null)
    const uploadLoading = ref(false)
    const pendingFiles = ref([])
    const uploadedFiles = ref([])

    // 模板相关（移除了标签页的activeTemplateTab，改为竖向平铺显示）

    // 权限控制
    const hasDeletePermission = computed(() => {
      const roleCode = localStorage.getItem('role_code')
      const username = localStorage.getItem('loginUsername')
      return username === 'admin' || (roleCode && roleCode.includes('D'))
    })

    // 检查是否为admin用户
    const isAdminUser = computed(() => {
      const username = localStorage.getItem('loginUsername')
      return username === 'admin'
    })

    // 获取事件级别选项
    const getPriorityOptions = async () => {
      try {
        const response = await request({
          url: '/api/get_cmdb_data_dictionary',
          method: 'post',
          data: { dict_type: 'U' }
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          priorityOptions.value = response.msg.filter(item => 
            item && item.dict_code !== null && item.dict_name !== null
          )
        }
      } catch (error) {

      }
    }

    // 获取系统列表
    const getSystemList = async () => {
      try {
        const response = await request({
          url: '/api/get_system_list',
          method: 'post'
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          systemOptions.value = response.msg.filter(item => 
            item && item.system_abbreviation !== null && item.system_abbreviation !== undefined
          )
        }
      } catch (error) {

      }
    }

    // 获取用户列表
    const getUserList = async () => {
      try {
        const response = await request({
          url: '/api/get_user_list',
          method: 'post'
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          userOptions.value = response.msg.filter(item => 
            item && item.username !== null && item.username !== undefined &&
            item.real_name !== null && item.real_name !== undefined
          )
        }
      } catch (error) {

      }
    }

    // 获取用户真实姓名
    const getUserRealName = (username) => {
      if (!username) return '未知用户'
      const user = userOptions.value.find(user => user.username === username)
      return user ? user.real_name : username
    }

    // 安全地将时间字符串转换为Date对象，避免时区问题
    const safeParseDateTime = (timeStr) => {
      if (!timeStr) return null
      
      try {
        // 如果是标准的 YYYY-MM-DD HH:mm:ss 格式
        if (typeof timeStr === 'string' && timeStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
          // 直接构造本地时间，避免时区转换
          const [datePart, timePart] = timeStr.split(' ')
          const [year, month, day] = datePart.split('-').map(Number)
          const [hour, minute, second] = timePart.split(':').map(Number)
          return new Date(year, month - 1, day, hour, minute, second)
        }
        
        // 其他格式尝试直接解析
        return new Date(timeStr)
      } catch (error) {

        return null
      }
    }

    // 将Date对象格式化为数据库存储格式的字符串
    const formatDateTimeForSubmit = (date) => {
      if (!date) return ''
      if (!(date instanceof Date)) return date
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    // 获取事件详情
    const getEventDetail = async (id) => {
      formLoading.value = true
      try {
        const response = await request({
          url: '/api/get_ops_event_management_detail',
          method: 'post',
          data: { id }
        })

        if (response.code === 0) {
          const data = response.msg
          Object.keys(formData).forEach(key => {
            if (data[key] !== undefined) {
              if (key === 'assignee') {
                // 处理多选处理人：将逗号分隔的字符串转换为数组
                if (data[key] && typeof data[key] === 'string') {
                  formData[key] = data[key].includes(',') 
                    ? data[key].split(',').map(item => item.trim()).filter(item => item)
                    : [data[key]]
                } else if (Array.isArray(data[key])) {
                  formData[key] = data[key]
                } else {
                  formData[key] = data[key] ? [data[key]] : []
                }
              } else if (key === 'report_time') {
                // 特殊处理时间字段，避免时区问题
                formData[key] = safeParseDateTime(data[key])

              } else {
                formData[key] = data[key]
              }
            }
          })

          // 初始化已上传的文件列表
          if (data.supplementary_material) {
            // 从原始路径中提取文件名（包含扩展名）
            const originalFileName = getFileNameFromPath(data.supplementary_material)
            
            // 检查原始文件名是否已经包含事件编号，避免重复添加
            let displayName = originalFileName || '分析报告.pdf'
            if (data.event_id && originalFileName && !originalFileName.startsWith(data.event_id)) {
              displayName = `${data.event_id}_${originalFileName}`
            }
            
            uploadedFiles.value = [{
              id: data.id,
              name: displayName,
              originalName: originalFileName, // 保存原始文件名
              path: data.supplementary_material,
              eventNumber: data.event_id
            }]
          } else {
            uploadedFiles.value = []
          }
        } else {
          ElMessage.error(response.msg || '获取事件详情失败')
          router.push('/ops_event_management')
        }
      } catch (error) {

        ElMessage.error('获取事件详情失败')
        router.push('/ops_event_management')
      } finally {
        formLoading.value = false
      }
    }

    // 处理提交
    const handleSubmit = () => {
      formRef.value.validate(async (valid) => {
        if (!valid) return

        submitLoading.value = true
        try {
          const api = mode.value === 'new' ? '/api/add_ops_event_management' : '/api/update_ops_event_management'
          const data = { ...formData }
          data.username = localStorage.getItem('loginUsername') || 'admin'

          // 处理时间字段，确保格式正确
          if (data.report_time) {
            data.report_time = formatDateTimeForSubmit(data.report_time)
          }

          console.log('提交数据:', data)

          const response = await request({
            url: api,
            method: 'post',
            data
          })

          if (response.code === 0) {
            ElMessage.success(mode.value === 'new' ? '事件创建成功' : '事件更新成功')
            router.push('/ops_event_management')
          } else {
            ElMessage.error(response.msg || `${mode.value === 'new' ? '创建' : '更新'}失败`)
          }
        } catch (error) {
          console.error('提交失败:', error)
          ElMessage.error(`${mode.value === 'new' ? '创建' : '更新'}失败`)
        } finally {
          submitLoading.value = false
        }
      })
    }

    // 处理删除
    const handleDelete = () => {
      ElMessageBox.confirm(
        `确定要删除事件 "${formData.event_id}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        deleteLoading.value = true
        try {
          const response = await request({
            url: '/api/del_ops_event_management',
            method: 'post',
            data: {
              id: formData.id,
              username: localStorage.getItem('loginUsername') || 'admin'
            }
          })

          if (response.code === 0) {
            ElMessage.success('删除成功')
            router.push('/ops_event_management')
          } else {
            ElMessage.error(`删除失败: ${response.msg}`)
          }
        } catch (error) {
          console.error('删除事件失败:', error)
          ElMessage.error('删除事件失败')
        } finally {
          deleteLoading.value = false
        }
      }).catch(() => {
        console.log('用户取消删除操作')
      })
    }

    // 处理取消
    const handleCancel = () => {
      router.push('/ops_event_management')
    }

    // 统一的模板内容定义（用于模板展示和自动填充）
    const eventTemplates = reactive({
      description: {
        title: '事件简要经过记录格式',
        content: `事件简要经过：
• 15时28分，态势感知平台发出告警:SIP风险事件，官网服务器************** 遭到病毒攻击，官网系统管理员立即联系珮金技术进行杀毒处理，网络安全管理员将疑似攻击IP加入黑名单，随后告警解除。
• 16时58分，态势感知平台再次发出告警:SIP风险事件，珮金中继服务器************* 遭到病毒攻击，网络安全管理员将疑似攻击IP加入黑名单，告警解除， 随后联系eset防病毒厂家协助安装杀毒软件。`
      },
      
      process: {
        title: '事件影响范围、影响程度、影响人数、直接资金损失情况记录格式',
        content: `事件影响：
• 长江期货APP上海联通（珮金）站点关闭，暂时对客户无影响`
      },
      
      consequence: {
        title: '事件导致的后果、发生原因和事件性质判断记录格式',
        content: `事件性质判断：
• 此次事件是互联网上的病毒攻击。`
      },

      solution: {
        title: '已采取的措施及效果记录格式',
        content: `已采取的措施及效果：
官网：
• 已完成************** 杀毒  ；
• 官网系统管理员确定官网只需要访问研报的域名，其他地址可以禁用，网络安全管理员已将其余互联网权限关闭；
• 在************** 服务器上删除DNS配置。
珮金：
• 系统管理员将受攻击的珮金中继*************站点暂时关机，以防病毒扩散，已通知客户服务中心，珮金一个站点例行维护；
• 将受影响网段另3台服务器*************、*************、*************也安装eset杀毒软件。`
      },
      
      improvement: {
        title: '总结及知识库记录格式',
        content: `经验教训：
• 最小化开放互联网策略：不管是互联网映射还是服务器上网,都要明确IP与端口,进行安全扫描并走完审批流程后再开放。对于已经开放的但限制限少的上网策略,请对应系统运维同事确认上网的具体需求后重新填写服务器上网需求表并走审批流程,然后再调整上网策略。以后再申请互联网权限均以最小化权限开放。 
• 加强安全扫描：深信服工程师表示目前的安全扫描偏系统漏洞扫描,对WEB漏扫描能力不强。已要求深信服在以后扫描时都加上专门的WEB漏洞扫描。
• 所有互联网linux服务部署杀毒软件：目前只有WINDOWS服务器安装了杀毒软件,后续将在所有开放互联网服务或上网的linux服务器上安装杀毒软件。但由于linux许可证不足,需向供应商加购linux许可。

知识库内容：
• `
      }
    })

    // 快速填充模板内容
    const fillTemplateContent = (type) => {
      console.log('fillTemplateContent called with type:', type)
      console.log('Current formData:', formData)

      const fieldMap = {
        description: 'description',
        process: 'process',
        solution: 'solution',
        improvement: 'improvement_plan',
        consequence: 'consequence_cause_analysis'
      }

      const field = fieldMap[type]
      const template = eventTemplates[type]
      
      console.log('Field mapping:', { type, field, hasTemplate: !!template })
      
      if (field && template) {
        console.log('Current field value:', formData[field])
        console.log('Template content:', template.content)
        
        // 如果字段已有内容，询问是否覆盖
        if (formData[field] && formData[field].trim()) {
          ElMessageBox.confirm(
            `${template.title.replace('记录格式', '')}字段已有内容，是否覆盖？`,
            '确认操作',
            {
              confirmButtonText: '覆盖',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            formData[field] = template.content
            console.log('Template filled successfully, new value:', formData[field])
            ElMessage.success('模板内容已填充')
          }).catch(() => {
            console.log('User cancelled template fill')
          })
        } else {
          formData[field] = template.content
          console.log('Template filled successfully, new value:', formData[field])
          ElMessage.success('模板内容已填充')
        }
      } else {
        console.error('Invalid field or template:', { field, hasTemplate: !!template })
        ElMessage.error('模板填充失败：无效的字段或模板')
      }
    }

    // 从文件路径中提取文件名
    const getFileNameFromPath = (filePath) => {
      if (!filePath) return ''
      const parts = filePath.split('/')
      return parts[parts.length - 1] || filePath
    }

    // 文件上传相关方法
    const handleFileChange = (file) => {
      console.log('文件改变:', file)
      
      // 检查是否为新建模式或事件未保存
      if (mode.value === 'new' || !formData.id || !formData.event_id || formData.event_id === '系统自动生成') {
        ElMessage.warning('请先保存事件记录，生成事件编号后再选择分析报告文件')
        return false
      }
      
      pendingFiles.value = [file]
    }

    const handleFileRemove = (file) => {
      console.log('移除文件:', file)
      pendingFiles.value = pendingFiles.value.filter(f => f.uid !== file.uid)
    }

    const beforeUpload = (file) => {
      const isValidType = /\.(doc|docx|pdf|xls|xlsx|ppt|pptx|txt)$/i.test(file.name)
      if (!isValidType) {
        ElMessage.error('只能上传 doc/docx/pdf/xls/xlsx/ppt/pptx/txt 格式的文件!')
        return false
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过 10MB!')
        return false
      }
      
      return true
    }

    const submitUpload = async () => {
      if (pendingFiles.value.length === 0) {
        ElMessage.warning('请先选择文件')
        return
      }

      // 检查是否为新建模式或事件未保存
      if (mode.value === 'new' || !formData.id || !formData.event_id || formData.event_id === '系统自动生成') {
        ElMessage.warning('请先保存事件记录，生成事件编号后再上传分析报告')
        return
      }

      uploadLoading.value = true
      try {
        // 使用URL查询参数传递事件ID和用户名，参考变更管理的逻辑
        const username = localStorage.getItem('loginUsername') || 'admin'
        const queryParams = new URLSearchParams({
          event_id: formData.id,
          username: username
        }).toString()

        const formDataUpload = new FormData()
        formDataUpload.append('file', pendingFiles.value[0].raw)

        const response = await fetch(`/api/upload_event_attachment?${queryParams}`, {
          method: 'POST',
          body: formDataUpload
        })

        const data = await response.json()

        if (response.ok && data.code === 0) {
          ElMessage.success('文件上传成功')
          // 使用服务器返回的格式化文件名
          const fileName = data.data?.filename || `${formData.event_id || data.data?.eventNumber}_分析报告`
          uploadedFiles.value.push({
            name: fileName,
            id: formData.id,
            path: data.data?.path,
            eventNumber: formData.event_id || data.data?.eventNumber
          })
          clearPendingFiles()
        } else {
          ElMessage.error(data.msg || '文件上传失败')
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        ElMessage.error('文件上传失败')
      } finally {
        uploadLoading.value = false
      }
    }

    const clearPendingFiles = () => {
      pendingFiles.value = []
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
    }

    const removeUploadedFile = async (file) => {
      // 确认删除对话框
      try {
        await ElMessageBox.confirm(
          `确定要删除文件 "${file.name}" 吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 用户确认后执行删除
        const response = await request({
          url: '/api/delete_event_attachment',
          method: 'post',
          data: {
            id: file.id,
            filePath: file.path, // 传递文件路径给后端用于删除物理文件
            username: localStorage.getItem('loginUsername') || 'admin'
          }
        })

        if (response.code === 0) {
          ElMessage.success('文件删除成功')
          uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== file.id)
        } else {
          ElMessage.error(response.msg || '文件删除失败')
        }
      } catch (error) {
        if (error === 'cancel') {
          console.log('用户取消删除操作')
        } else {
          console.error('文件删除失败:', error)
          ElMessage.error('文件删除失败')
        }
      }
    }

    // 下载已上传的文件
    const downloadUploadedFile = async (file) => {
      try {
        if (!file.path) {
          ElMessage.error('文件路径不存在')
          return
        }

        // 使用POST请求下载文件
        const response = await request({
          url: '/api/download_ops_event_file',
          method: 'post',
          data: {
            event_id: formData.id,
            file_type: 'supplementary_material'
          },
          responseType: 'blob'
        })

        // 创建下载链接
        const blob = new Blob([response])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // 使用原始文件名或显示名称，不要强制添加扩展名
        const downloadName = file.originalName || file.name || '分析报告'
        link.download = downloadName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success('文件下载成功')
      } catch (error) {
        console.error('文件下载失败:', error)
        ElMessage.error('文件下载失败')
      }
    }

    // 下载分析报告模板
    const downloadAnalysisTemplate = async () => {
      const filename = '分析报告-模板.docx'
      const url = `/api/download_ops_event_template/${encodeURIComponent(filename)}`
      
      await downloadFromApi(
        url,
        filename,
        {},
        () => ElMessage.success('事件分析报告模板下载成功'),
        (error) => {
          console.error('下载事件分析报告模板失败:', error)
          ElMessage.error('下载事件分析报告模板失败')
        }
      )
    }

    // 导出事件为Word文档
    const exportToWord = async () => {
      if (mode.value === 'new' || !formData.id) {
        ElMessage.warning('请先保存事件后再导出')
        return
      }

      ElMessage.info('正在生成Word文档，请稍候...')

      const url = `/api/export_ops_event_word/${formData.id}`
      // 使用事件编号+事件标题的格式命名，并清理特殊字符
      const sanitizedTitle = formData.title ? formData.title.replace(/[\\/:*?"<>|]/g, '_') : ''
      const filename = `${formData.event_id || 'unknown'}_${sanitizedTitle}.docx`
      
      await downloadFromApi(
        url,
        filename,
        {},
        () => ElMessage.success('事件记录导出成功'),
        (error) => {
          console.error('导出事件记录失败:', error)
          ElMessage.error('导出事件记录失败')
        }
      )
    }

    // 页面初始化
    onMounted(async () => {
      // 获取选项数据
      await Promise.all([
        getPriorityOptions(),
        getSystemList(),
        getUserList()
      ])

      // 根据模式进行不同处理
      if (mode.value === 'new') {
        // 新增模式：设置默认值
        formData.reporter = localStorage.getItem('loginUsername') || 'admin'
        formData.report_time = new Date()  // 设置为当前时间的Date对象
        formData.event_id = '系统自动生成'
      } else {
        // 编辑/查看模式：获取详情
        const id = route.params.id
        if (id && id !== 'new') {
          await getEventDetail(id)
        }
      }
    })

    return {
      mode,
      formRef,
      formData,
      formRules,
      priorityOptions,
      systemOptions,
      userOptions,
      formLoading,
      submitLoading,
      deleteLoading,
      hasDeletePermission,
      isAdminUser,
      // 统一模板内容
      eventTemplates,
      // 文件上传相关
      uploadRef,
      uploadLoading,
      pendingFiles,
      uploadedFiles,
      handleFileChange,
      handleFileRemove,
      beforeUpload,
      submitUpload,
      clearPendingFiles,
      removeUploadedFile,
      downloadUploadedFile,
      getFileNameFromPath,
      // 其他方法
      handleSubmit,
      handleDelete,
      handleCancel,
      getUserRealName,
      fillTemplateContent,
      downloadAnalysisTemplate,
      exportToWord,
      // 添加图标组件，使其在模板中可用
      Document,
      Download,
      Delete
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  
  .el-button {
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 主要内容区域 - 左右分栏布局 */
.main-content {
  display: flex;
  gap: 20px;
  min-height: 600px;
}

/* 左侧表单区域 */
.left-panel {
  flex: 1;
  min-width: 0; /* 防止 flex 项目溢出 */
}

/* 表单样式优化 */
.event-form {
  .el-form-item {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      line-height: 32px;
    }
    
    .el-form-item__content {
      line-height: 32px;
    }
  }
  
  /* 必填字段标识 */
  .required-field {
    .el-form-item__label::before {
      content: '*';
      color: #f56c6c;
      margin-right: 4px;
    }
  }
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  
  .el-icon {
    font-size: 18px;
    color: #409eff;
  }
  
  span {
    position: relative;
  }
}

/* 右侧模板区域 */
.right-panel {
  width: 450px;
  flex-shrink: 0;
  border-left: 1px solid #e4e7ed;
  padding-left: 20px;
}

.template-content {
  .el-tabs {
    --el-tabs-header-height: 40px;
  }
  
  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }
  
  :deep(.el-tabs__item) {
    padding: 0 12px;
    font-size: 13px;
  }
}

.template-section {
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #606266;
  }
}

.template-example {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  font-size: 13px;
  line-height: 1.6;
  color: #606266;
  max-height: 600px;
  overflow-y: auto;
  
  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: #303133;
      font-weight: 600;
    }
  }
  
  pre {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

/* 自动填充按钮样式 */
.auto-fill-button-container {
  margin-top: 8px;
  display: flex;
  justify-content: flex-start;
}

.auto-fill-button {
  padding: 4px 12px;
  font-size: 12px;
  height: 28px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.quick-fill-section {
  margin-top: 20px;
}

/* 事件编号显示 */
.event-id-section {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.event-id-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 文件上传区域样式 */
.file-upload-section {
  margin-bottom: 20px;
  
  .upload-disabled-tip {
    margin-bottom: 16px;
  }
  
  /* 统一分隔线标题样式 */
  :deep(.el-divider) {
    margin: 20px 0;
  }
  
  :deep(.el-divider__text) {
    background-color: #f5f7fa;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
  }
  
  :deep(.el-divider--horizontal) {
    border-top: 2px solid #e4e7ed;
  }
  
  .upload-area {
    .analysis-upload {
      :deep(.el-upload) {
        width: 100%;
      }
      
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 150px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: visible;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 16px;
        
        &:hover {
          border-color: #409eff;
        }
      }
      
      :deep(.el-icon--upload) {
        font-size: 28px;
        color: #8c939d;
        margin-bottom: 8px;
      }
      
      :deep(.el-upload__text) {
        color: #606266;
        font-size: 14px;
        text-align: center;
        line-height: 1.4;
        margin-bottom: 6px;
        
        em {
          color: #409eff;
          font-style: normal;
        }
      }
      
      :deep(.el-upload__tip) {
        color: #8c939d;
        font-size: 12px;
        text-align: center;
        line-height: 1.3;
        white-space: nowrap;
        overflow: visible;
      }
    }
    
    .uploaded-files {
      margin-top: 12px;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 6px;
      border: 1px solid #bfdbfe;
      
      .file-list-header {
        font-size: 14px;
        font-weight: 600;
        color: #1e40af;
        margin-bottom: 8px;
        padding: 4px 0;
        border-bottom: 2px solid #3b82f6;
        display: inline-block;
      }
      
      .file-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 12px;
        margin-bottom: 8px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        transition: all 0.2s ease;
        position: relative;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
          border-color: #3b82f6;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 8px 0 0 8px;
        }
        
        .el-icon {
          color: #3b82f6;
          font-size: 18px;
          flex-shrink: 0;
          margin-left: 6px;
          background: #dbeafe;
          border-radius: 50%;
          padding: 4px;
          width: 26px;
          height: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .file-name {
          flex: 1;
          font-size: 13px;
          font-weight: 500;
          color: #1e293b;
          margin-right: 8px;
          line-height: 1.4;
        }
        
        .file-actions {
          display: flex;
          gap: 6px;
          flex-shrink: 0;
          
          .el-button {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 4px;
            font-weight: 500;
            
            &.el-button--primary {
              background: #3b82f6;
              border-color: #3b82f6;
              
              &:hover {
                background: #2563eb;
                border-color: #2563eb;
              }
            }
            
            &.el-button--danger {
              &:hover {
                background: #ef4444;
                border-color: #ef4444;
              }
            }
          }
        }
      }
    }
    
    .upload-actions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
      justify-content: flex-end;
    }
  }
}

/* 模板下载区域样式 */
.template-download-section {
  margin-top: 20px;
  padding-top: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  
  /* 统一分隔线标题样式 */
  :deep(.el-divider) {
    margin: 20px 0;
  }
  
  :deep(.el-divider__text) {
    background-color: #f5f7fa;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
  }
  
  :deep(.el-divider--horizontal) {
    border-top: 2px solid #e4e7ed;
  }
}

/* 模板分隔线区域样式 */
.template-separator {
  margin: 40px 0 20px 0;
  
  :deep(.el-divider) {
    margin: 24px 0;
  }
  
  :deep(.el-divider__text) {
    background-color: #f5f7fa;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
  }
  
  :deep(.el-divider--horizontal) {
    border-top: 2px solid #e4e7ed;
  }
}

.download-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
  
  .download-button {
    min-width: 180px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

/* 删除按钮区域样式 */
.delete-section {
  margin-top: 20px;
}

.delete-button-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  padding: 16px 0;
}

.delete-button {
  min-width: 140px;
}

/* 标签展示样式 */
.tag-display {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 24px;
  padding: 8px 0;
}

.display-tag {
  font-size: 12px;
  border-radius: 6px;
  padding: 4px 8px;
  border: 1px solid #d9ecff;
  background: #ecf5ff;
  color: #409eff;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
  }

  .right-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e4e7ed;
    padding-left: 0;
    padding-top: 20px;
    margin-top: 16px;
  }
  

}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-buttons {
    width: 100%;
    justify-content: flex-end;
  }

  .delete-button-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .app-container {
    padding: 12px;
  }
  
  .right-panel {
    padding: 16px;
  }
  
  .template-example {
    padding: 12px;
    font-size: 12px;
  }
  
  .auto-fill-button {
    font-size: 11px;
    padding: 3px 8px;
    height: 24px;
  }
  
  .file-upload-section {
    .upload-area {
      .analysis-upload {
        :deep(.el-upload-dragger) {
          height: 100px;
        }
        
        :deep(.el-icon--upload) {
          font-size: 24px;
        }
        
        :deep(.el-upload__text) {
          font-size: 13px;
        }
        
        :deep(.el-upload__tip) {
          font-size: 11px;
        }
      }
      
      .uploaded-files {
        padding: 8px;
        
        .file-item {
          padding: 6px 0;
          
          .file-name {
            font-size: 12px;
          }
        }
      }
    }
  }

}

@media (max-width: 576px) {
  .event-id-section {
    padding: 10px 12px;
  }

  .event-id-text {
    font-size: 13px;
  }
}

/* 底部附件管理区域样式 */
.bottom-attachment-section {
  margin-top: 30px;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
  
  :deep(.el-divider) {
    margin: 0 0 20px 0;
  }
  
  :deep(.el-divider__text) {
    background-color: #f5f7fa;
    padding: 6px 14px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
  }
  
  :deep(.el-divider--horizontal) {
    border-top: 2px solid #e4e7ed;
  }
}

/* 附件上传面板样式 */
.attachment-upload-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
  min-height: 250px;
  
  .panel-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #303133;
    font-weight: 600;
    font-size: 15px;
    
    .el-icon {
      color: #409eff;
      margin-right: 6px;
      font-size: 16px;
    }
  }
  
  .upload-disabled-tip {
    margin-bottom: 12px;
  }
  
  .upload-area {
    .analysis-upload {
      :deep(.el-upload) {
        width: 100%;
      }
      
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: visible;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 16px 12px;
        
        &:hover {
          border-color: #409eff;
        }
      }
      
      :deep(.el-icon--upload) {
        font-size: 24px;
        color: #8c939d;
        margin-bottom: 6px;
      }
      
      :deep(.el-upload__text) {
        color: #606266;
        font-size: 13px;
        text-align: center;
        line-height: 1.4;
        margin-bottom: 4px;
        
        em {
          color: #409eff;
          font-style: normal;
        }
      }
      
      :deep(.el-upload__tip) {
        color: #8c939d;
        font-size: 11px;
        text-align: center;
        line-height: 1.3;
        white-space: nowrap;
        overflow: visible;
      }
    }
    
    .uploaded-files {
      margin-top: 12px;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 8px;
      
      .file-list-header {
        font-size: 14px;
        font-weight: 600;
        color: #1e40af;
        margin-bottom: 10px;
        border-bottom: 2px solid #3b82f6;
        padding-bottom: 6px;
        text-decoration: underline;
        text-underline-offset: 3px;
      }
      
      .file-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 12px;
        margin-bottom: 8px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 6px;
        border-left: 4px solid #3b82f6;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-icon {
          color: #ffffff;
          background: #3b82f6;
          border-radius: 50%;
          padding: 4px;
          font-size: 18px;
          flex-shrink: 0;
        }
        
        .file-name {
          flex: 1;
          font-size: 13px;
          color: #1e40af;
          font-weight: 600;
          margin-right: 8px;
        }
        
        .file-actions {
          display: flex;
          gap: 6px;
          flex-shrink: 0;
          
          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            transition: all 0.3s ease;
            
            &.el-button--primary {
              background: linear-gradient(135deg, #22c55e, #16a34a);
              border: none;
              color: #ffffff;
              
              &:hover {
                background: linear-gradient(135deg, #16a34a, #15803d);
                transform: translateY(-1px);
                color: #ffffff;
              }
            }
          }
        }
      }
    }
    
    .upload-actions {
      display: flex;
      gap: 6px;
      margin-top: 10px;
      justify-content: flex-end;
    }
  }
}

/* 新的模板卡片样式 */
.template-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.template-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 15px;
  color: #303133;
  
  .el-icon {
    color: #409eff;
    font-size: 16px;
  }
}

.template-card-content {
  padding: 4px 0;
  
  h4 {
    color: #606266;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.template-example {
  line-height: 1.6;
  
  p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #606266;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: #303133;
      font-weight: 600;
      font-size: 14px;
      display: block;
      margin-bottom: 4px;
      margin-top: 12px;
      
      &:first-child {
        margin-top: 0;
      }
    }
  }
  
  /* 列表样式优化 */
  p:not(:has(strong)) {
    padding-left: 8px;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 3px;
      background-color: #409eff;
      border-radius: 50%;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-card {
    margin-bottom: 12px;
  }
  
  .template-card-header {
    font-size: 14px;
    
    .el-icon {
      font-size: 14px;
    }
  }
  
  .template-card-content {
    h4 {
      font-size: 13px;
    }
  }
  
  .template-example {
    p {
      font-size: 12px;
      
      strong {
        font-size: 13px;
      }
    }
  }
}

/* 模板下载面板样式 */
.template-download-panel {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
  min-height: 250px;
  
  .panel-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #303133;
    font-weight: 600;
    font-size: 15px;
    
    .el-icon {
      color: #67c23a;
      margin-right: 6px;
      font-size: 16px;
    }
  }
  
  .download-content {
    .download-description {
      margin-bottom: 16px;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 6px;
      // border-left: 3px solid #67c23a;
      
      p {
        color: #606266;
        font-size: 13px;
        line-height: 1.5;
        margin: 0;
      }
    }
    
    .download-buttons {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      
      .download-button {
        min-width: 200px;
        padding: 10px 20px;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .bottom-attachment-section {
    .el-row {
      flex-direction: column;
    }
    
    .attachment-upload-panel,
    .template-download-panel {
      margin-bottom: 12px;
      min-height: 200px;
      padding: 12px;
    }
    
    .attachment-upload-panel .upload-area .analysis-upload :deep(.el-upload-dragger) {
      height: 100px;
      padding: 12px 8px;
    }
    
    .template-download-panel .download-content .download-buttons .download-button {
      min-width: 160px;
      padding: 8px 16px;
      font-size: 12px;
    }
  }
}

</style> 