import React, { useEffect, useState } from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface AnimatedMetricProps {
  value: number;
  previousValue?: number;
  label: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'stable';
  format?: 'number' | 'percentage' | 'currency';
  animationDuration?: number;
  glowEffect?: boolean;
}

const AnimatedMetric: React.FC<AnimatedMetricProps> = ({
  value,
  previousValue,
  label,
  icon,
  trend,
  format = 'number',
  animationDuration = 1000,
  glowEffect = false
}) => {
  const [displayValue, setDisplayValue] = useState(previousValue || 0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (displayValue !== value) {
      setIsAnimating(true);
      const startValue = displayValue;
      const endValue = value;
      const startTime = Date.now();

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);
        
        // 使用easeOutCubic缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const currentValue = startValue + (endValue - startValue) * easeProgress;
        
        setDisplayValue(currentValue);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          setIsAnimating(false);
        }
      };

      requestAnimationFrame(animate);
    }
  }, [value, displayValue, animationDuration]);

  const formatValue = (val: number): string => {
    switch (format) {
      case 'percentage':
        return `${val.toFixed(2)}%`;
      case 'currency':
        return `¥${val.toLocaleString()}`;
      default:
        return Math.round(val).toLocaleString();
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-neon-green" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-neon-red" />;
      case 'stable':
        return <Minus className="w-4 h-4 text-gray-400" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-neon-green';
      case 'down':
        return 'text-neon-red';
      default:
        return 'text-white';
    }
  };

  const glowClass = glowEffect ? 'neon-glow' : '';

  return (
    <div className={`glass-card rounded-lg p-4 smooth-transition hover-lift ${glowClass}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="text-neon-blue">
            {icon}
          </div>
          <span className="text-sm text-gray-300">{label}</span>
        </div>
        {getTrendIcon()}
      </div>
      
      <div className="flex items-end justify-between">
        <div className={`text-2xl font-bold ${getTrendColor()} ${isAnimating ? 'count-animation' : ''}`}>
          {formatValue(displayValue)}
        </div>
        
        {previousValue !== undefined && (
          <div className="text-xs text-gray-400">
            {trend === 'up' && '+'}
            {formatValue(value - previousValue)}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnimatedMetric;