// 数据中心分布数据服务

interface DataCenterLocation {
  id: string;
  name: string;
  location: string;
  networkDevices: number;
  physicalServers: number;
  virtualServers: number;
  totalDevices: number;
  status: 'normal' | 'warning' | 'critical';
  coordinates: {
    x: number;
    y: number;
  };
  lastUpdate?: Date;
}

interface DataCenterStats {
  totalLocations: number;
  totalDevices: number;
  totalNetworkDevices: number;
  totalPhysicalServers: number;
  totalVirtualServers: number;
  normalLocations: number;
  warningLocations: number;
  criticalLocations: number;
}

class DataCenterService {
  private cache: DataCenterLocation[] = [];
  private cacheExpiry: number = 5 * 60 * 1000; // 5分钟缓存
  private lastCacheTime: number = 0;

  /**
   * 获取数据中心分布数据
   */
  async getDataCenterDistribution(forceRefresh: boolean = false): Promise<DataCenterLocation[]> {
    // 检查缓存是否有效
    const now = Date.now();
    if (!forceRefresh && this.cache.length > 0 && (now - this.lastCacheTime) < this.cacheExpiry) {
      return this.cache;
    }

    try {
      console.log('正在获取数据中心分布数据...');
      
      // 模拟异步数据获取
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 直接使用测试数据
      const locations = this.getTestData();

      // 更新缓存
      this.cache = locations;
      this.lastCacheTime = now;

      console.log(`成功获取 ${locations.length} 个数据中心的分布数据`);
      return locations;

    } catch (error) {
      console.error('获取数据中心分布数据异常:', error);
      
      // 如果有缓存数据，返回缓存
      if (this.cache.length > 0) {
        return this.cache;
      }
      
      // 否则返回默认数据
      return this.getTestData();
    }
  }

  /**
   * 获取数据中心统计信息
   */
  async getDataCenterStats(): Promise<DataCenterStats> {
    const locations = await this.getDataCenterDistribution();

    return {
      totalLocations: locations.length,
      totalDevices: locations.reduce((sum, loc) => sum + loc.totalDevices, 0),
      totalNetworkDevices: locations.reduce((sum, loc) => sum + loc.networkDevices, 0),
      totalPhysicalServers: locations.reduce((sum, loc) => sum + loc.physicalServers, 0),
      totalVirtualServers: locations.reduce((sum, loc) => sum + loc.virtualServers, 0),
      normalLocations: locations.filter(loc => loc.status === 'normal').length,
      warningLocations: locations.filter(loc => loc.status === 'warning').length,
      criticalLocations: locations.filter(loc => loc.status === 'critical').length
    };
  }

  /**
   * 根据机房名称获取详细信息
   */
  async getLocationDetails(locationName: string): Promise<DataCenterLocation | null> {
    const locations = await this.getDataCenterDistribution();
    return locations.find(loc => loc.location === locationName) || null;
  }

  /**
   * 刷新数据中心分布数据
   */
  async refreshData(): Promise<DataCenterLocation[]> {
    return this.getDataCenterDistribution(true);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = [];
    this.lastCacheTime = 0;
  }



  /**
   * 获取测试数据（用于演示效果）
   * 按设备数量排序，显示数量最多的10个地理位置
   */
  private getTestData(): DataCenterLocation[] {
    return [
      {
        id: 'shanghai_idc',
        name: '上海数据中心',
        location: '上海数据中心',
        networkDevices: 85,
        physicalServers: 62,
        virtualServers: 178,
        totalDevices: 325,
        status: 'normal',
        coordinates: { x: 75, y: 35 },
        lastUpdate: new Date()
      },
      {
        id: 'beijing_idc',
        name: '北京数据中心',
        location: '北京数据中心',
        networkDevices: 72,
        physicalServers: 54,
        virtualServers: 142,
        totalDevices: 268,
        status: 'normal',
        coordinates: { x: 65, y: 20 },
        lastUpdate: new Date()
      },
      {
        id: 'shenzhen_idc',
        name: '深圳数据中心',
        location: '深圳数据中心',
        networkDevices: 58,
        physicalServers: 42,
        virtualServers: 118,
        totalDevices: 218,
        status: 'normal',
        coordinates: { x: 70, y: 60 },
        lastUpdate: new Date()
      },
      {
        id: 'guangzhou_idc',
        name: '广州数据中心',
        location: '广州数据中心',
        networkDevices: 45,
        physicalServers: 35,
        virtualServers: 95,
        totalDevices: 175,
        status: 'normal',
        coordinates: { x: 68, y: 65 },
        lastUpdate: new Date()
      },
      {
        id: 'wuhan_idc',
        name: '武汉数据中心',
        location: '武汉数据中心',
        networkDevices: 38,
        physicalServers: 28,
        virtualServers: 82,
        totalDevices: 148,
        status: 'normal',
        coordinates: { x: 67, y: 42 },
        lastUpdate: new Date()
      },
      {
        id: 'zhengzhou_idc',
        name: '郑州数据中心',
        location: '郑州数据中心',
        networkDevices: 32,
        physicalServers: 25,
        virtualServers: 68,
        totalDevices: 125,
        status: 'normal',
        coordinates: { x: 64, y: 38 },
        lastUpdate: new Date()
      },
      {
        id: 'dalian_idc',
        name: '大连数据中心',
        location: '大连数据中心',
        networkDevices: 28,
        physicalServers: 22,
        virtualServers: 58,
        totalDevices: 108,
        status: 'normal',
        coordinates: { x: 71, y: 18 },
        lastUpdate: new Date()
      },
      {
        id: 'hangzhou_idc',
        name: '杭州数据中心',
        location: '杭州数据中心',
        networkDevices: 25,
        physicalServers: 18,
        virtualServers: 52,
        totalDevices: 95,
        status: 'normal',
        coordinates: { x: 72, y: 40 },
        lastUpdate: new Date()
      },
      {
        id: 'nanjing_idc',
        name: '南京数据中心',
        location: '南京数据中心',
        networkDevices: 22,
        physicalServers: 16,
        virtualServers: 45,
        totalDevices: 83,
        status: 'normal',
        coordinates: { x: 73, y: 32 },
        lastUpdate: new Date()
      },
      {
        id: 'chengdu_idc',
        name: '成都数据中心',
        location: '成都数据中心',
        networkDevices: 18,
        physicalServers: 14,
        virtualServers: 38,
        totalDevices: 70,
        status: 'warning',
        coordinates: { x: 58, y: 45 },
        lastUpdate: new Date()
      }
    ];
  }


  /**
   * 获取缓存状态
   */
  getCacheStatus(): { 
    hasCachedData: boolean; 
    cacheAge: number; 
    isExpired: boolean;
    itemCount: number;
  } {
    const now = Date.now();
    const cacheAge = now - this.lastCacheTime;
    
    return {
      hasCachedData: this.cache.length > 0,
      cacheAge,
      isExpired: cacheAge > this.cacheExpiry,
      itemCount: this.cache.length
    };
  }
}

export const dataCenterService = new DataCenterService();
export type { DataCenterLocation, DataCenterStats };