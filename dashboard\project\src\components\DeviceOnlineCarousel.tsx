import React, { useState, useEffect } from 'react';
import { Wifi } from 'lucide-react';
import TechProgressBar from './TechProgressBar';
import EnhancedCard from './EnhancedCard';

interface OnlineMetric {
  name: string;
  value: number;
  total: number;
  online: number;
  icon: React.ReactNode;
}

interface DeviceOnlineCarouselProps {
  metrics: OnlineMetric[];
  itemsPerPage?: number;
  autoPlayInterval?: number;
  className?: string;
}

const DeviceOnlineCarousel: React.FC<DeviceOnlineCarouselProps> = ({
  metrics,
  itemsPerPage = 3,
  autoPlayInterval = 5000,
  className = ""
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayedMetrics, setDisplayedMetrics] = useState<OnlineMetric[]>([]);

  const totalPages = Math.ceil(metrics.length / itemsPerPage);
  const needsCarousel = metrics.length > itemsPerPage;

  // 初始化显示的指标
  useEffect(() => {
    const startIndex = currentIndex * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, metrics.length);
    const currentMetrics = metrics.slice(startIndex, endIndex);
    
    // 如果不足3个，用空占位符填充以保持布局一致
    const paddedMetrics = [...currentMetrics];
    while (paddedMetrics.length < itemsPerPage) {
      paddedMetrics.push(null as any);
    }
    
    setDisplayedMetrics(paddedMetrics);
  }, [currentIndex, metrics, itemsPerPage]);

  // 自动轮播（只有在需要轮播时才启用）
  useEffect(() => {
    if (!needsCarousel || totalPages <= 1) return;

    const interval = setInterval(() => {
      handleNext();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentIndex, totalPages, autoPlayInterval, needsCarousel]);

  const handleNext = () => {
    if (isTransitioning || !needsCarousel) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % totalPages);
      setIsTransitioning(false);
    }, 300);
  };

  const handlePrevious = () => {
    if (isTransitioning || !needsCarousel) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev - 1 + totalPages) % totalPages);
      setIsTransitioning(false);
    }, 300);
  };

  const goToPage = (pageIndex: number) => {
    if (isTransitioning || pageIndex === currentIndex || !needsCarousel) return;
    
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex(pageIndex);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <EnhancedCard 
      title="设备在线情况" 
      icon={<Wifi className="w-5 h-5" />}
      className={`h-full flex flex-col ${className}`}
      animationDelay={200}
    >
      {/* 页面指示器和控制按钮（只有在需要轮播时显示） */}
      {needsCarousel && (
        <div className="flex justify-between items-center mb-4">
          <button
            onClick={handlePrevious}
            disabled={isTransitioning}
            className="w-8 h-8 rounded-full glass-card flex items-center justify-center hover:bg-neon-green/20 transition-all disabled:opacity-50"
          >
            <svg className="w-4 h-4 text-neon-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div className="flex space-x-2">
            {Array.from({ length: totalPages }).map((_, i) => (
              <button
                key={i}
                onClick={() => goToPage(i)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  i === currentIndex 
                    ? 'bg-neon-green shadow-lg shadow-neon-green/50' 
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>

          <button
            onClick={handleNext}
            disabled={isTransitioning}
            className="w-8 h-8 rounded-full glass-card flex items-center justify-center hover:bg-neon-green/20 transition-all disabled:opacity-50"
          >
            <svg className="w-4 h-4 text-neon-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}

      {/* 指标卡片容器 */}
      <div className="relative overflow-hidden flex-1">
        <div 
          className={`transition-all duration-500 ease-in-out h-full ${
            isTransitioning ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
          }`}
        >
          <div className="space-y-3 h-full flex flex-col justify-start">
            {displayedMetrics.map((metric, index) => (
              metric ? (
                <OnlineMetricCard 
                  key={`${metric.name}-${currentIndex}-${index}`}
                  metric={metric}
                  animationDelay={index * 100}
                />
              ) : (
                <div key={`placeholder-${index}`} className="h-16 opacity-0" />
              )
            ))}
          </div>
        </div>
      </div>
    </EnhancedCard>
  );
};

interface OnlineMetricCardProps {
  metric: OnlineMetric;
  animationDelay: number;
}

const OnlineMetricCard: React.FC<OnlineMetricCardProps> = ({ metric, animationDelay }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, animationDelay);
    return () => clearTimeout(timer);
  }, [animationDelay]);

  return (
    <div 
      className={`flex items-center justify-between p-4 glass-card rounded-lg transition-all duration-500 hover:bg-white/5 hover:shadow-lg hover:shadow-neon-green/20 smooth-transition ${
        isVisible ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'
      }`}
    >
      <div className="flex items-center space-x-3 flex-1">
        <div className="text-neon-green p-2 rounded-lg bg-neon-green/10">
          {metric.icon}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-200 mb-1">{metric.name}</div>
          <div className="text-xs text-gray-400">
            {metric.online}/{metric.total} 设备
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        <div className="w-16">
          <TechProgressBar 
            value={metric.value}
            max={100}
            label=""
            variant="linear"
            glowColor="#10b981"
            animationDuration={800}
          />
        </div>
        
        <div className="text-right min-w-[3rem]">
          <div className="text-sm font-bold text-white">
            {metric.value.toFixed(1)}%
          </div>
        </div>
        
        {/* 状态指示器 */}
        <div className="w-2 h-2 rounded-full bg-neon-green shadow-lg shadow-neon-green/50 pulse-glow" />
      </div>
    </div>
  );
};

export default DeviceOnlineCarousel;