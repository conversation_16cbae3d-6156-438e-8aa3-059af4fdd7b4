<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" ref="searchFormRef" label-width="100px" label-position="right">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="关键字">
              <el-input
                v-model="searchForm.keyword"
                placeholder="事件编号或标题"
                clearable
                class="form-control"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="事件级别">
              <el-select
                v-model="searchForm.priority"
                placeholder="请选择"
                clearable
                class="form-control"
              >
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.dict_code"
                  :label="item.dict_name"
                  :value="item.dict_code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="首次发生时间">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                class="form-control"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="值班经理">
              <el-select
                v-model="searchForm.reporter"
                placeholder="请选择"
                clearable
                filterable
                class="form-control"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.username"
                  :label="item.real_name"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="处理人">
              <el-select
                v-model="searchForm.assignee"
                placeholder="请选择"
                clearable
                filterable
                class="form-control"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.username"
                  :label="item.real_name"
                  :value="item.username"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="影响系统">
              <el-select
                v-model="searchForm.system"
                placeholder="请选择"
                clearable
                filterable
                class="form-control"
              >
                <el-option
                  v-for="item in systemOptions"
                  :key="item.system_abbreviation"
                  :label="item.system_abbreviation"
                  :value="item.system_abbreviation"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="24" class="search-buttons-col">
            <el-form-item>
              <div class="button-container">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" @click="handleAdd">
          <el-icon><Plus /></el-icon>新增事件
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        table-layout="auto"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="event_id" label="事件编号" min-width="120" sortable />
        <el-table-column prop="title" label="事件标题" min-width="200" show-overflow-tooltip sortable />
        
        <el-table-column label="事件级别" min-width="100" align="center" sortable>
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ scope.row.priority_name_display }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="system" label="影响系统" min-width="120" sortable />
        <el-table-column prop="reporter_name" label="值班经理" min-width="120" sortable />
        <el-table-column prop="assignees_name_display" label="处理人" min-width="150" sortable show-overflow-tooltip />
        <el-table-column prop="formatted_report_time" label="首次发生时间" min-width="160" sortable />

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button 
                size="small" 
                type="primary" 
                @click="handleEdit(scope.row)"
                class="action-btn"
              >
                详情
              </el-button>
              <el-button 
                size="small" 
                type="warning" 
                @click="handleExport(scope.row)"
                :icon="Document"
                class="action-btn"
              >
                导出
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Document } from '@element-plus/icons-vue'
import request from '@/utils/request'
import { downloadFromApi } from '@/utils/downloadUtils'
// 移除不需要的 formatDateTime 导入

export default {
  name: 'EventManagementIndex',
  components: {
    Plus,
    Search,
    Document
  },
  setup() {
    const router = useRouter()
    const searchFormRef = ref(null)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      priority: '',
      reporter: '',
      assignee: '',
      system: '',
      startDate: '',
      endDate: ''
    })

    // 日期范围
    const dateRange = ref([])

    // 表格数据
    const tableData = ref([])
    const tableLoading = ref(false)

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 20,
      total: 0
    })

    // 选项数据
    const eventTypeOptions = ref([])
    const priorityOptions = ref([])
    const systemOptions = ref([])
    const userOptions = ref([])

    // 权限控制
    const hasDeletePermission = ref(false)

    // 检查删除权限
    const checkDeletePermission = () => {
      const roleCode = localStorage.getItem('role_code')
      const username = localStorage.getItem('loginUsername')
      hasDeletePermission.value = username === 'admin' || (roleCode && roleCode.includes('D'))
    }

    // 获取事件类型选项
    const getEventTypeOptions = async () => {
      try {
        const response = await request({
          url: '/api/get_cmdb_data_dictionary',
          method: 'post',
          data: { dict_type: 'T' }
        })
        if (response.code === 0) {
          eventTypeOptions.value = response.msg
        }
      } catch (error) {

      }
    }

    // 获取事件级别选项
    const getPriorityOptions = async () => {
      try {
        const response = await request({
          url: '/api/get_cmdb_data_dictionary',
          method: 'post',
          data: { dict_type: 'U' }
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          priorityOptions.value = response.msg.filter(item => 
            item && item.dict_code !== null && item.dict_name !== null
          )
        }
      } catch (error) {

      }
    }

    // 获取系统列表
    const getSystemList = async () => {
      try {
        const response = await request({
          url: '/api/get_system_list',
          method: 'post'
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          systemOptions.value = response.msg.filter(item => 
            item && item.system_abbreviation !== null && item.system_abbreviation !== undefined
          )
        }
      } catch (error) {

      }
    }

    // 获取用户列表
    const getUserList = async () => {
      try {
        const response = await request({
          url: '/api/get_user_list',
          method: 'post'
        })
        if (response.code === 0) {
          // 过滤掉包含null值的选项
          userOptions.value = response.msg.filter(item => 
            item && item.username !== null && item.username !== undefined &&
            item.real_name !== null && item.real_name !== undefined
          )
        }
      } catch (error) {

      }
    }

    // 获取事件列表
    const getEventList = async () => {
      tableLoading.value = true
      try {
        const params = {
          currentPage: pagination.currentPage,
          pageSize: pagination.pageSize,
          ...searchForm
        }

        const response = await request({
          url: '/api/get_ops_event_management',
          method: 'post',
          data: params
        })

        if (response.code === 0) {
          tableData.value = response.msg || []
          // 新增：格式化首次发生时间
          tableData.value.forEach(row => {
            row.formatted_report_time = formatDateTime(row.report_time)
          })
          pagination.total = response.total || 0
        } else {
          ElMessage.error(response.msg || '获取事件列表失败')
        }
      } catch (error) {

        ElMessage.error('获取事件列表失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 处理日期范围变化
    const handleDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        searchForm.startDate = dates[0]
        searchForm.endDate = dates[1]
      } else {
        searchForm.startDate = ''
        searchForm.endDate = ''
      }
    }

    // 搜索
    const handleSearch = () => {
      pagination.currentPage = 1
      getEventList()
    }

    // 重置搜索
    const resetSearch = () => {
      if (searchFormRef.value) {
        searchFormRef.value.resetFields()
      }
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      dateRange.value = []
      pagination.currentPage = 1
      getEventList()
    }

    // 分页处理
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      pagination.currentPage = 1
      getEventList()
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      getEventList()
    }

    // 排序处理
    const handleSortChange = () => {
      // 可以在这里添加排序逻辑
      getEventList()
    }

    // 操作处理
    const handleAdd = () => {
      router.push('/ops_event_management/new')
    }

    const handleView = (row) => {
      router.push(`/ops_event_management/${row.id}?mode=view`)
    }

    const handleEdit = (row) => {
      router.push(`/ops_event_management/${row.id}`)
    }

    const handleDelete = (row) => {
      if (!hasDeletePermission.value) {
        ElMessage.error('您没有删除权限')
        return
      }

      ElMessageBox.confirm(
        `确定要删除事件 "${row.event_id}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await request({
            url: '/api/del_ops_event_management',
            method: 'post',
            data: {
              id: row.id,
              username: localStorage.getItem('loginUsername') || 'admin'
            }
          })

          if (response.code === 0) {
            ElMessage.success('删除成功')
            getEventList()
          } else {
            ElMessage.error(`删除失败: ${response.msg}`)
          }
        } catch (error) {
          ElMessage.error('删除事件失败')
        }
      }).catch(() => {

      })
    }

    // 导出事件报告
    const handleExport = async (row) => {
      if (!row.id) {
        ElMessage.warning('无效的事件记录')
        return
      }

      ElMessage.info('正在生成Word文档，请稍候...')

      const url = `/api/export_ops_event_word/${row.id}`
      const filename = `事件报告_${row.event_id || 'unknown'}.docx`
      
      await downloadFromApi(
        url,
        filename,
        {},
        () => ElMessage.success('事件报告导出成功'),
        (error) => {

          ElMessage.error('导出事件报告失败')
        }
      )
    }

    // 获取事件级别样式
    const getPriorityType = (priority) => {
      const priorityMap = {
        'U00001': 'danger',   // 严重：红色
        'U00002': 'warning',  // 中度：黄色  
        'U00003': 'success',  // 轻度：绿色
        'U00004': 'info'      // 潜在：蓝色
      }
      return priorityMap[priority] || ''
    }

    // 格式化日期时间
    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return '';

      try {
        const date = new Date(dateTimeStr);

        // 检查是否为有效日期
        if (isNaN(date.getTime())) {
          return dateTimeStr;
        }

        // 格式化函数
        const padZero = (num) => String(num).padStart(2, '0');

        const year = date.getFullYear();
        const month = padZero(date.getMonth() + 1);
        const day = padZero(date.getDate());
        const hours = padZero(date.getHours());
        const minutes = padZero(date.getMinutes());
        const seconds = padZero(date.getSeconds());

        // 如果时间部分都是0，则只显示日期部分
        if (date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0) {
          return `${year}-${month}-${day}`;
        } else {
          // 否则显示完整的日期时间
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateTimeStr;
      }
    };

    // 页面加载时执行
    onMounted(async () => {
      checkDeletePermission()
      await Promise.all([
        getEventTypeOptions(),
        getPriorityOptions(),
        getSystemList(),
        getUserList()
      ])
      getEventList()
    })

    return {
      searchFormRef,
      searchForm,
      dateRange,
      tableData,
      tableLoading,
      pagination,
      eventTypeOptions,
      priorityOptions,
      systemOptions,
      userOptions,
      hasDeletePermission,
      formatDateTime,
      handleDateRangeChange,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleView,
      handleEdit,
      handleDelete,
      handleExport,
      getPriorityType,
      // 添加图标组件，使其在模板中可用
      Document
    }
  }
}
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 10px;
  height: auto;
}

.table-card {
  margin-bottom: 20px;
}

.form-control {
  width: 100%;
  min-width: 190px;
}

.search-buttons-col {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
}

.button-container {
  display: flex;
  gap: 10px;
}

.unified-action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .action-bar-left {
    display: flex;
    gap: 10px;
  }

  .action-bar-right {
    display: flex;
    gap: 10px;
  }
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.action-btn {
  flex: 1;
  min-width: 60px;
  max-width: 75px;
  font-size: 12px;
  padding: 4px 6px;
  white-space: nowrap;
}

.action-btn + .action-btn {
  margin-left: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-buttons-col {
    margin-top: 10px;
  }

  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-bar-left, .action-bar-right {
    margin-bottom: 8px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-bar-right {
    justify-content: flex-end;
  }
}
</style> 