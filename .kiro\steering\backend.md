---
description: 
globs: *.js/
alwaysApply: false
---
# CMDB v2.0 后端开发规则

## 项目概述
这是一个CMDB（配置管理数据库）v2.0项目的后端部分，采用Node.js + Express.js + PostgreSQL技术栈。

## 技术栈
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: PostgreSQL
- **认证**: JWT + bcryptjs
- **环境管理**: dotenv
- **主入口**: [index.js](mdc:backend/index.js)
- **配置文件**: [.env](mdc:backend/.env)

## 项目结构

### 核心目录
- `api/` - API路由定义
- `controllers/` - 业务逻辑控制器
- `services/` - 业务服务层
- `db/` - 数据库相关
- `sql/` - SQL脚本
- `middlewares/` - 中间件
- `utils/` - 工具函数
- `scripts/` - 脚本文件

### 配置文件
- [package.json](mdc:backend/package.json) - 项目依赖配置
- [.env](mdc:backend/.env) - 环境变量配置
- [start.sh](mdc:backend/start.sh) - 启动脚本
- [stop.sh](mdc:backend/stop.sh) - 停止脚本

## 开发规范

### API设计规范
- 使用RESTful API设计风格
- 统一返回JSON格式
- 错误处理统一格式
- 支持分页查询

### 数据库规范
- **资产表命名**: `cmdb_表名`
- **视图命名**: `v_cmdb_表名`
- **序列命名**: `表名_id_seq`
- **报表表命名**: `report_表名`
- **报表视图命名**: `v_report_表名`

### 认证与安全
- 本地用户密码使用bcryptjs加密
- JWT认证机制
- 支持LDAP远程验证
- 默认超级用户：admin

### 日期时间格式
- **日期格式**: yyyy-MM-dd
- **时间格式**: yyyy-MM-dd HH:mm:ss
- 前后端统一使用此格式

### 环境变量管理
1. 统一的数据库连接池文件
2. 通过[.env](mdc:backend/.env)文件管理
  - 数据库连接信息
  - JWT密钥
  - 服务端口
  - LDAP配置

## 模块命名规范

### 页面模块前缀
- **资产管理**: `cmdb_` 前缀
- **AI平台**: `AI_` 前缀  
- **运维管理**: `ops_` 前缀

## 开发注意事项

### 代码组织
- 采用模块化设计
- 分离业务逻辑和数据访问
- 统一错误处理
- 代码注释规范

### 性能考虑
- 数据库查询优化
- 分页处理
- 缓存策略
- 异步处理

### 安全考虑
- 输入验证
- SQL注入防护
- XSS防护
- 权限控制

### 数据流程
当字段使用数据字典时应该注意回显和后端更新数据的流程，如机房名称：
- 新增时：前端选择机房名称 → 提交字典代码 → 后端存储字典代码 
- 查询时：后端返回显示名称和原始代码 → 前端显示名称 
- 编辑时：前端使用原始代码回显 → 表单正确显示对应选项 → 提交字典代码


## 部署相关
- 使用[start.sh](mdc:backend/start.sh)启动服务
- 使用[stop.sh](mdc:backend/stop.sh)停止服务
- 支持环境变量配置
- 日志记录和监控
