import React, { useState, useEffect } from 'react';
import { LucideIcon } from 'lucide-react';

interface MetricCard3DProps {
  title: string;
  value: number;
  unit?: string;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  icon: LucideIcon;
  color?: string;
  className?: string;
  animationDelay?: number;
  size?: 'sm' | 'md' | 'lg';
}

const MetricCard3D: React.FC<MetricCard3DProps> = ({
  title,
  value,
  unit = '',
  change,
  trend = 'stable',
  icon: Icon,
  color = '#00d4ff',
  className = '',
  animationDelay = 0,
  size = 'md'
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      const duration = 1500;
      const steps = 60;
      const increment = value / steps;
      let current = 0;
      
      const counter = setInterval(() => {
        current += increment;
        if (current >= value) {
          setAnimatedValue(value);
          clearInterval(counter);
        } else {
          setAnimatedValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(counter);
    }, animationDelay);

    return () => clearTimeout(timer);
  }, [value, animationDelay]);

  const sizeClasses = {
    sm: 'p-4 min-h-[120px]',
    md: 'p-6 min-h-[160px]',
    lg: 'p-8 min-h-[200px]'
  };

  const iconSizes = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const valueSizes = {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl'
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return '#10b981';
      case 'down': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getTrendSymbol = () => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      default: return '→';
    }
  };

  return (
    <div
      className={`
        relative group cursor-pointer transition-all duration-500 ease-out
        ${sizeClasses[size]} ${className}
      `}
      style={{
        animationDelay: `${animationDelay}ms`,
        perspective: '1000px'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 3D卡片容器 */}
      <div
        className={`
          relative w-full h-full rounded-xl transition-all duration-500 ease-out
          ${isHovered ? 'transform-gpu rotate-y-12 rotate-x-6' : ''}
        `}
        style={{
          transformStyle: 'preserve-3d',
          transform: isHovered 
            ? 'perspective(1000px) rotateY(12deg) rotateX(6deg) translateZ(20px)' 
            : 'perspective(1000px) rotateY(0deg) rotateX(0deg) translateZ(0px)'
        }}
      >
        {/* 主卡片面 */}
        <div
          className="
            absolute inset-0 rounded-xl overflow-hidden
            bg-gradient-to-br from-tech-surface/80 to-tech-darker/60
            backdrop-blur-md border border-white/10
            hover:border-white/20 transition-all duration-300
          "
          style={{
            background: `
              linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                rgba(255, 255, 255, 0.05) 50%,
                transparent 100%
              ),
              linear-gradient(135deg, 
                ${color}10 0%, 
                ${color}05 100%
              )
            `,
            boxShadow: `
              0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1),
              0 0 0 1px ${color}20
            `
          }}
        >
          {/* 发光边框动画 */}
          <div
            className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            style={{
              background: `linear-gradient(90deg, transparent, ${color}40, transparent)`,
              animation: isHovered ? 'borderRotate 2s linear infinite' : 'none'
            }}
          />

          {/* 内容区域 */}
          <div className="relative z-10 h-full flex flex-col justify-between p-1">
            {/* 顶部：图标和趋势 */}
            <div className="flex items-start justify-between">
              <div
                className={`
                  ${iconSizes[size]} rounded-lg flex items-center justify-center
                  transition-all duration-300 group-hover:scale-110
                `}
                style={{
                  background: `linear-gradient(135deg, ${color}20, ${color}10)`,
                  color: color,
                  boxShadow: `0 0 20px ${color}30`
                }}
              >
                <Icon className="w-full h-full p-1" />
              </div>

              {change !== undefined && (
                <div
                  className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: `${getTrendColor()}20`,
                    color: getTrendColor()
                  }}
                >
                  <span>{getTrendSymbol()}</span>
                  <span>{Math.abs(change)}%</span>
                </div>
              )}
            </div>

            {/* 中部：数值 */}
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div
                  className={`${valueSizes[size]} font-bold transition-all duration-300 group-hover:scale-105`}
                  style={{
                    background: `linear-gradient(135deg, ${color}, ${color}80)`,
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    filter: 'drop-shadow(0 0 10px rgba(0, 212, 255, 0.3))'
                  }}
                >
                  {animatedValue.toLocaleString()}
                  {unit && <span className="text-sm ml-1 opacity-80">{unit}</span>}
                </div>
              </div>
            </div>

            {/* 底部：标题 */}
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-300 group-hover:text-white transition-colors duration-300">
                {title}
              </h3>
            </div>
          </div>

          {/* 粒子效果 */}
          <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 rounded-full opacity-60"
                style={{
                  backgroundColor: color,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `particleTwinkle ${2 + Math.random() * 2}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 2}s`
                }}
              />
            ))}
          </div>

          {/* 扫描线效果 */}
          <div
            className={`
              absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-500
              pointer-events-none
            `}
            style={{
              background: `
                repeating-linear-gradient(
                  90deg,
                  transparent 0px,
                  ${color}10 1px,
                  transparent 2px
                )
              `
            }}
          />
        </div>

        {/* 3D阴影层 */}
        <div
          className="
            absolute inset-0 rounded-xl -z-10
            bg-black/20 blur-sm
            transition-all duration-500
          "
          style={{
            transform: 'translateZ(-10px) translateY(4px)',
            opacity: isHovered ? 0.6 : 0.3
          }}
        />
      </div>

      {/* 悬浮时的额外发光效果 */}
      {isHovered && (
        <div
          className="absolute inset-0 rounded-xl -z-20 transition-opacity duration-500"
          style={{
            background: `radial-gradient(circle at center, ${color}20 0%, transparent 70%)`,
            filter: 'blur(20px)'
          }}
        />
      )}
    </div>
  );
};

export default MetricCard3D;