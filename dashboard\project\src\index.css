/* 
  Tailwind CSS directives - These are processed by Tailwind CSS
  If you see "Unknown at rule" warnings, install the Tailwind CSS IntelliSense extension
*/
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS变量系统 - 支持动态主题切换 */
:root {
  /* 科技感色彩变量 - 增强版 */
  --tech-dark: #0a0e27;
  --tech-darker: #1a1a2e;
  --tech-surface: #16213e;
  --tech-border: #2d3748;
  --tech-accent: #1e293b;
  --tech-highlight: #334155;

  /* 霓虹色系变量 - 扩展版 */
  --neon-blue: #00d4ff;
  --neon-cyan: #00ffff;
  --neon-purple: #8b5cf6;
  --neon-green: #10b981;
  --neon-orange: #f59e0b;
  --neon-red: #ef4444;
  --neon-pink: #ec4899;
  --neon-yellow: #eab308;
  --neon-indigo: #6366f1;

  /* 玻璃态效果变量 - 增强版 */
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-bg-light: rgba(255, 255, 255, 0.05);
  --glass-bg-medium: rgba(255, 255, 255, 0.08);
  --glass-bg-strong: rgba(255, 255, 255, 0.12);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-border-light: rgba(255, 255, 255, 0.05);
  --glass-border-strong: rgba(255, 255, 255, 0.15);
  --glass-hover: rgba(255, 255, 255, 0.06);
  
  /* 玻璃态背景渐变 */
  --glass-gradient-subtle: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  --glass-gradient-light: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  --glass-gradient-medium: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  
  /* 彩色玻璃效果 */
  --glass-blue: linear-gradient(135deg, rgba(0, 212, 255, 0.03) 0%, rgba(0, 212, 255, 0.01) 100%);
  --glass-cyan: linear-gradient(135deg, rgba(0, 255, 255, 0.03) 0%, rgba(0, 255, 255, 0.01) 100%);
  --glass-green: linear-gradient(135deg, rgba(16, 185, 129, 0.03) 0%, rgba(16, 185, 129, 0.01) 100%);
  --glass-purple: linear-gradient(135deg, rgba(139, 92, 246, 0.03) 0%, rgba(139, 92, 246, 0.01) 100%);

  /* 动画时长变量 */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.6s;
  --duration-extra-slow: 1.2s;

  /* 发光效果强度 - 分级 */
  --glow-intensity-low: 0.2;
  --glow-intensity: 0.4;
  --glow-intensity-high: 0.6;
  --glow-radius: 20px;
  --glow-radius-large: 40px;

  /* 数据可视化色彩 */
  --data-primary: #3b82f6;
  --data-secondary: #06b6d4;
  --data-success: #10b981;
  --data-warning: #f59e0b;
  --data-danger: #ef4444;
  --data-info: #8b5cf6;

  /* 渐变色定义 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 玻璃态效果工具类 */
@layer utilities {
  .glass-card {
    background: var(--glass-gradient-subtle);
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border-light);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
  }

  .glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
    transition: left 0.5s;
  }

  .glass-card:hover {
    background: var(--glass-gradient-light);
    border-color: var(--glass-border);
    transform: translateY(-2px);
    backdrop-filter: blur(12px);
  }

  .glass-card:hover::before {
    left: 100%;
  }

  /* 超透明玻璃效果 - 让背景更明显 */
  .glass-ultra-transparent {
    background: rgba(255, 255, 255, 0.01);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.02);
  }

  .glass-ultra-transparent:hover {
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(6px);
  }

  /* 轻度玻璃效果 */
  .glass-light {
    background: var(--glass-gradient-light);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow:
      0 6px 24px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
  }

  /* 中度玻璃效果 */
  .glass-medium {
    background: var(--glass-gradient-medium);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border-strong);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.12);
  }

  /* 彩色玻璃效果 */
  .glass-blue {
    background: var(--glass-blue);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 212, 255, 0.1);
    box-shadow:
      0 8px 32px rgba(0, 212, 255, 0.1),
      inset 0 1px 0 rgba(0, 212, 255, 0.05);
  }

  .glass-cyan {
    background: var(--glass-cyan);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 255, 255, 0.1);
    box-shadow:
      0 8px 32px rgba(0, 255, 255, 0.1),
      inset 0 1px 0 rgba(0, 255, 255, 0.05);
  }

  .glass-green {
    background: var(--glass-green);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    box-shadow:
      0 8px 32px rgba(16, 185, 129, 0.1),
      inset 0 1px 0 rgba(16, 185, 129, 0.05);
  }

  .glass-purple {
    background: var(--glass-purple);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(139, 92, 246, 0.1);
    box-shadow:
      0 8px 32px rgba(139, 92, 246, 0.1),
      inset 0 1px 0 rgba(139, 92, 246, 0.05);
  }

  /* 渐变玻璃背景 */
  .glass-gradient-bg {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.02) 0%,
      rgba(0, 212, 255, 0.01) 25%,
      rgba(0, 255, 255, 0.01) 50%,
      rgba(139, 92, 246, 0.01) 75%,
      rgba(255, 255, 255, 0.02) 100%
    );
    backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.04);
  }

  /* 增强发光效果系列 */
  .neon-glow {
    box-shadow:
      0 0 10px rgba(0, 212, 255, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(0, 212, 255, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(0, 212, 255, calc(var(--glow-intensity) * 0.6));
  }

  .neon-glow-green {
    box-shadow:
      0 0 10px rgba(16, 185, 129, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(16, 185, 129, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(16, 185, 129, calc(var(--glow-intensity) * 0.6));
  }

  .neon-glow-orange {
    box-shadow:
      0 0 10px rgba(245, 158, 11, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(245, 158, 11, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(245, 158, 11, calc(var(--glow-intensity) * 0.6));
  }

  .neon-glow-red {
    box-shadow:
      0 0 10px rgba(239, 68, 68, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(239, 68, 68, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(239, 68, 68, calc(var(--glow-intensity) * 0.6));
  }

  .neon-glow-purple {
    box-shadow:
      0 0 10px rgba(139, 92, 246, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(139, 92, 246, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(139, 92, 246, calc(var(--glow-intensity) * 0.6));
  }

  .neon-glow-pink {
    box-shadow:
      0 0 10px rgba(236, 72, 153, var(--glow-intensity)),
      0 0 var(--glow-radius) rgba(236, 72, 153, calc(var(--glow-intensity) * 0.8)),
      0 0 40px rgba(236, 72, 153, calc(var(--glow-intensity) * 0.6));
  }

  /* 渐变文字效果 */
  .tech-gradient-text {
    background: linear-gradient(90deg, var(--neon-blue) 0%, var(--neon-cyan) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
  }

  .tech-gradient-text-alt {
    background: linear-gradient(90deg, var(--neon-purple) 0%, var(--neon-pink) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .tech-gradient-text-success {
    background: linear-gradient(90deg, var(--neon-green) 0%, var(--neon-cyan) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .tech-gradient-text-warning {
    background: linear-gradient(90deg, var(--neon-orange) 0%, var(--neon-yellow) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 边框效果 */
  .tech-border {
    border: 1px solid;
    border-image: linear-gradient(90deg, var(--neon-blue), var(--neon-cyan)) 1;
  }

  .tech-border-animated {
    position: relative;
    border: 1px solid transparent;
    background: linear-gradient(var(--tech-surface), var(--tech-surface)) padding-box,
                linear-gradient(90deg, var(--neon-blue), var(--neon-cyan), var(--neon-blue)) border-box;
    animation: borderRotate 3s linear infinite;
  }

  /* 动画效果 */
  .smooth-transition {
    transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  }

  .entrance-animation {
    animation: fadeIn var(--duration-slow) ease-out forwards;
    opacity: 0;
  }

  .entrance-slide-up {
    animation: slideUp var(--duration-slow) ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .entrance-scale {
    animation: scaleIn var(--duration-slow) ease-out forwards;
    opacity: 0;
    transform: scale(0.8);
  }

  .hover-lift {
    transition: transform var(--duration-fast) ease-out, box-shadow var(--duration-fast) ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .hover-glow:hover {
    box-shadow: 
      0 0 20px rgba(0, 212, 255, 0.6),
      0 0 40px rgba(0, 212, 255, 0.4),
      0 0 60px rgba(0, 212, 255, 0.2);
  }

  /* 脉冲和浮动效果 */
  .pulse-glow {
    animation: glowPulse 2s ease-in-out infinite;
  }

  .pulse-scale {
    animation: pulseScale 2s ease-in-out infinite;
  }

  .floating {
    animation: float 3s ease-in-out infinite;
  }

  .floating-slow {
    animation: float 6s ease-in-out infinite;
  }

  /* 数据流动效果 */
  .data-stream {
    position: relative;
    overflow: hidden;
  }

  .data-stream::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
    animation: dataStream 2s linear infinite;
  }

  /* 粒子效果 */
  .particle-float {
    animation: particleFloat 8s ease-in-out infinite;
  }

  .particle-drift {
    animation: particleDrift 12s linear infinite;
  }

  .particle-twinkle {
    animation: particleTwinkle 3s ease-in-out infinite;
  }

  /* 扫描线效果 */
  .scan-line {
    position: relative;
    overflow: hidden;
  }

  .scan-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
    animation: scanMove 3s ease-in-out infinite;
  }

  /* 全息效果 */
  .hologram {
    position: relative;
    background: linear-gradient(135deg, 
      rgba(0, 212, 255, 0.1) 0%, 
      rgba(0, 255, 255, 0.05) 50%, 
      rgba(139, 92, 246, 0.1) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .hologram::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 212, 255, 0.03) 2px,
      rgba(0, 212, 255, 0.03) 4px
    );
    pointer-events: none;
  }

  /* 矩阵雨效果 */
  .matrix-bg {
    position: relative;
    overflow: hidden;
  }

  .matrix-bg::after {
    content: '';
    position: absolute;
    top: -100%;
    left: 0;
    right: 0;
    height: 200%;
    background: repeating-linear-gradient(
      0deg,
      transparent 0px,
      rgba(0, 255, 0, 0.03) 1px,
      transparent 2px
    );
    animation: matrixRain 20s linear infinite;
  }

  /* 驾驶舱窗框装饰效果 */
  .cockpit-frame {
    position: relative;
  }

  .cockpit-frame::before,
  .cockpit-frame::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 200px;
    height: 80px;
    border: 2px solid var(--neon-blue);
    border-radius: 20px;
    background: linear-gradient(135deg, 
      rgba(0, 212, 255, 0.05) 0%, 
      rgba(0, 212, 255, 0.02) 50%,
      rgba(0, 212, 255, 0.05) 100%
    );
    backdrop-filter: blur(4px);
    box-shadow: 
      0 0 20px rgba(0, 212, 255, 0.3),
      inset 0 0 20px rgba(0, 212, 255, 0.1);
  }

  .cockpit-frame::before {
    left: -220px;
    clip-path: polygon(0 0, 85% 0, 100% 50%, 85% 100%, 0 100%);
    animation: cockpitGlow 3s ease-in-out infinite;
  }

  .cockpit-frame::after {
    right: -220px;
    clip-path: polygon(15% 0, 100% 0, 100% 100%, 15% 100%, 0 50%);
    animation: cockpitGlow 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }

  /* 驾驶舱HUD元素 */
  .hud-element {
    position: relative;
    border: 1px solid var(--neon-cyan);
    background: linear-gradient(135deg, 
      rgba(0, 255, 255, 0.03) 0%, 
      rgba(0, 255, 255, 0.01) 100%
    );
    clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0 100%);
  }

  .hud-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      transparent 0px,
      rgba(0, 255, 255, 0.02) 1px,
      transparent 2px
    );
    pointer-events: none;
  }

  /* 驾驶舱仪表盘边框 */
  .dashboard-border {
    position: relative;
    border: 2px solid transparent;
    background: linear-gradient(var(--tech-surface), var(--tech-surface)) padding-box,
                linear-gradient(45deg, var(--neon-blue), var(--neon-cyan), var(--neon-green), var(--neon-blue)) border-box;
    border-radius: 12px;
  }

  .dashboard-border::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-cyan), var(--neon-green), var(--neon-blue));
    border-radius: 16px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
    animation: borderRotate 4s linear infinite;
  }

  /* 驾驶舱角落装饰 */
  .cockpit-corner {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid var(--neon-orange);
    background: radial-gradient(circle at center, 
      rgba(245, 158, 11, 0.1) 0%, 
      transparent 70%
    );
  }

  .cockpit-corner-tl {
    top: 20px;
    left: 20px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 20px;
  }

  .cockpit-corner-tr {
    top: 20px;
    right: 20px;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 20px;
  }

  .cockpit-corner-bl {
    bottom: 20px;
    left: 20px;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 20px;
  }

  .cockpit-corner-br {
    bottom: 20px;
    right: 20px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 20px;
  }

  /* 驾驶舱状态指示器 */
  .status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, 
      rgba(16, 185, 129, 0.1) 0%, 
      rgba(16, 185, 129, 0.05) 100%
    );
    border: 1px solid var(--neon-green);
    border-radius: 20px;
    backdrop-filter: blur(4px);
  }

  .status-indicator::before {
    content: '';
    position: absolute;
    left: 8px;
    width: 8px;
    height: 8px;
    background: var(--neon-green);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--neon-green);
    animation: statusPulse 2s ease-in-out infinite;
  }

  /* 驾驶舱数据流指示器 */
  .data-flow-indicator {
    position: relative;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      var(--neon-blue) 50%, 
      transparent 100%
    );
    overflow: hidden;
  }

  .data-flow-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.8) 50%, 
      transparent 100%
    );
    animation: dataFlowPulse 2s linear infinite;
  }

  /* 驾驶舱网格背景 */
  .cockpit-grid {
    background-image: 
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 2px, transparent 2px);
    background-size: 40px 40px, 40px 40px, 80px 80px;
    animation: cockpitGridMove 30s linear infinite;
  }
}

/* 滚动条样式 */
.scrollbar-tech {
  scrollbar-width: thin;
  scrollbar-color: var(--neon-blue) var(--tech-surface);
}

.scrollbar-tech::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-tech::-webkit-scrollbar-track {
  background: var(--tech-surface);
  border-radius: 3px;
}

.scrollbar-tech::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--neon-blue), var(--neon-cyan));
  border-radius: 3px;
}

.scrollbar-tech::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--neon-cyan), var(--neon-blue));
}

/* 关键帧动画定义 */
@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowPulse {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
  }

  50% {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes wave {

  0%,
  100% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(100%);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes scanLine {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes scanLineVertical {
  0% {
    transform: translateX(-100vw);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

@keyframes dataFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes starlight {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
    box-shadow: 0 0 6px currentColor;
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 12px currentColor, 0 0 24px currentColor;
  }
}

@keyframes starlight-small {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
    box-shadow: 0 0 4px currentColor;
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
    box-shadow: 0 0 8px currentColor, 0 0 16px currentColor;
  }
}

@keyframes starlight-tiny {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
    box-shadow: 0 0 2px currentColor;
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);
    box-shadow: 0 0 4px currentColor, 0 0 8px currentColor;
  }
}

/* 新增动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes borderRotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes dataStream {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes scanMove {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes matrixRain {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.6;
  }
}

@keyframes particleDrift {
  0% {
    transform: translateX(-10px) translateY(0px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 10px)) translateY(-20px);
    opacity: 0;
  }
}

@keyframes particleTwinkle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.count-animation {
  animation: countUp 1s ease-out;
}

/* 驾驶舱相关动画 */
@keyframes cockpitGlow {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(0, 212, 255, 0.3),
      inset 0 0 20px rgba(0, 212, 255, 0.1);
    border-color: var(--neon-blue);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(0, 212, 255, 0.5),
      inset 0 0 30px rgba(0, 212, 255, 0.2);
    border-color: var(--neon-cyan);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes dataFlowPulse {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes cockpitGridMove {
  0% {
    background-position: 0 0, 0 0, 0 0;
  }
  100% {
    background-position: 40px 40px, 40px 40px, 80px 80px;
  }
}

@keyframes radarSweep {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(360deg);
  }
}

/* 响应式字体大小 */
@media (min-width: 1920px) {
  :root {
    font-size: 18px;
  }
}

@media (max-width: 1440px) {
  :root {
    font-size: 14px;
  }
}

@media (max-width: 1024px) {
  :root {
    font-size: 12px;
  }
}