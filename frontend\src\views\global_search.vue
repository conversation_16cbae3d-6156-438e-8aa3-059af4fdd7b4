
<style scoped>
.search-page {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f6f7f9 0%, #eef1f5 100%);
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
  text-align: center;
}

.search-subtitle {
  font-size: 16px;
  color: #606266;
  margin: 0 0 32px;
  text-align: center;
}

.search-box {
  margin: 0 auto 32px;
  max-width: 800px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  overflow: hidden;
}

.search-stats {
  text-align: center;
  color: #606266;
  margin-bottom: 24px;
  font-size: 14px;
}

.result-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.result-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.result-tag {
  position: absolute;
  top: 16px;
  right: 16px;
}

.result-header {
  margin-bottom: 16px;
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.result-header h3 a {
  color: #409eff;
  text-decoration: none;
}

.result-header h3 a:hover {
  color: #66b1ff;
}

.result-details {
  margin-bottom: 16px;
}

.result-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.loading-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.pagination {
  margin-top: 32px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-container {
    padding: 16px;
  }

  .result-container {
    grid-template-columns: 1fr;
  }

  .search-title {
    font-size: 24px;
  }

  .search-subtitle {
    font-size: 14px;
  }
}
</style>

<template>
  <div class="search-page">
    <div class="search-container">
      <!-- 搜索标题 -->
      <h1 class="search-title">全局资源搜索</h1>
      <p class="search-subtitle">快速查找设备、服务器、应用系统信息或者是变更和事件信息</p>

      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="search.query"
          placeholder="请输入IP地址或者是变更编号进行搜索..."
          @keyup.enter="handleSearch"
          size="large"
          clearable
          :prefix-icon="SearchIcon"
        >
          <template #append>
            <!-- 当有搜索结果时显示清除按钮 -->
            <el-button v-if="results.length > 0" type="danger" @click="clearSearch">
              清除
            </el-button>
            <!-- 当没有搜索结果时显示搜索按钮 -->
            <el-button v-else type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>搜索
            </el-button>
          </template>
        </el-input>
      </div>

      <!-- 搜索结果统计 -->
      <div v-if="results.length > 0" class="search-stats">
        找到 {{ search.total }} 条相关结果 ({{ searchTime }}秒)
      </div>

      <!-- 搜索结果展示 -->
      <div v-if="results.length > 0" class="result-container">
        <div v-for="(item, index) in results" :key="index" class="result-card">
          <!-- 结果类型标签 -->
          <el-tag
            :type="getTagType(item.menu_name)"
            class="result-tag"
            size="small"
          >
            {{ getSystemType(item.menu_name) }}
          </el-tag>

          <!-- IP地址和名称 -->
          <div class="result-header">
            <h3>
              <a href="javascript:void(0)" @click="navigateToDetail(item.goto_router, item)">
                {{ item.management_ip }}
              </a>
            </h3>
          </div>

          <!-- 详细信息 -->
          <div class="result-details">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="数据来源">
                {{ item.menu_name }}
              </el-descriptions-item>
              <el-descriptions-item label="关键字">
                {{ item.management_ip }}
              </el-descriptions-item>
              <el-descriptions-item label="详情概述">
                {{ item.function_purpose || '暂无详细信息' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 快捷操作 -->
          <div class="result-actions">
            <el-button
              type="success"
              link
              @click="copyIP(item.management_ip)"
            >
              复制关键字
            </el-button>
            <el-button
              type="primary"
              link
              @click="navigateToDetail(item.goto_router, item)"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 无结果提示 -->
      <el-empty
        v-if="!loading && results.length === 0 && search.query"
        description="未找到相关结果"
      >
        <template #description>
          <p>试试其他搜索关键词，或者检查IP地址是否正确</p>
        </template>
      </el-empty>

      <!-- 分页器 -->
      <el-pagination
        v-if="results.length > 0"
        class="pagination"
        background
        :current-page="search.currentPage"
        :page-size="search.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="search.total"
        :pager-count="7"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { markRaw } from 'vue'

export default {
  components: {
    Search,
  },

  data() {
    // 从localStorage中恢复搜索状态和结果
    const savedSearch = JSON.parse(localStorage.getItem("globalSearchState") || "null");

    return {
      // 使用 markRaw 避免组件被响应式包装
      SearchIcon: markRaw(Search),
      search: savedSearch?.search || {
        query: "",
        total: 0,
        pageSize: 10,
        currentPage: 1,
        loginUsername: localStorage.getItem("loginUsername"),
        sortProp: "management_ip",
        sortOrder: "desc",
      },
      results: savedSearch?.results || [],
      loading: false,
      searchTime: savedSearch?.searchTime || 0,
      searchStartTime: 0,
      // 标记是否已经从localStorage恢复了状态
      restoredFromStorage: !!savedSearch,
    }
  },

  mounted() {
    // 如果有保存的搜索状态，但没有结果，尝试重新加载
    if (this.restoredFromStorage && this.search.query && this.results.length === 0) {
      this.handleSearch();
    }
  },

  methods: {
    handlePageChange(newPage) {
      this.search.currentPage = newPage
      this.handleSearch()
    },

    handlePageSizeChange(newSize) {
      this.search.pageSize = newSize
      this.search.currentPage = 1
      this.handleSearch()
    },

    // 清除搜索结果
    clearSearch() {
      this.search.query = ""
      this.results = []
      this.search.total = 0
      this.searchTime = 0
      localStorage.removeItem("globalSearchState")
    },

    async handleSearch() {
      if (!this.search.query.trim()) {
        ElMessage.warning("请输入搜索内容")
        return
      }

      this.loading = true
      this.searchStartTime = performance.now()

      try {
        const response = await this.$axios.post("/api/get_global_search", this.search)
        this.results = response.data.msg || []
        this.search.total = response.data.total
        this.searchTime = ((performance.now() - this.searchStartTime) / 1000).toFixed(2)

        // 保存搜索状态和结果到localStorage
        this.saveSearchState()
      } catch (error) {
        console.error("搜索失败:", error)
        ElMessage.error("搜索失败，请稍后重试")
      } finally {
        this.loading = false
      }
    },

    // 保存搜索状态到localStorage
    saveSearchState() {
      const stateToSave = {
        search: this.search,
        results: this.results,
        searchTime: this.searchTime
      }
      localStorage.setItem("globalSearchState", JSON.stringify(stateToSave))
    },

    getTagType(menuName) {
      // 使用更直观的颜色命名，便于理解
      const types = {
        '应用系统信息登记表': 'primary',   // 蓝色，表示应用系统
        '网络设备登记': 'success',         // 绿色，表示网络设备
        '实体服务器设备登记': 'warning',   // 黄色，表示实体服务器
        '虚拟机登记表': 'danger',          // 红色，表示虚拟机
        '变更管理': 'info'                // 灰色，表示变更管理
      }
      return types[menuName] || 'info'    // 默认为灰色
    },

    getSystemType(menuName) {
      const types = {
        '应用系统信息登记表': '应用系统',
        '网络设备登记': '网络设备',
        '实体服务器设备登记': '实体服务器',
        '虚拟机登记表': '虚拟机',
        '变更管理': '变更管理'
      }
      return types[menuName] || '其他'
    },

    // 处理路由跳转
    navigateToDetail(routePath, item) {
      try {
        // 如果是外部链接，直接打开
        if (routePath.startsWith('http')) {
          window.open(routePath, '_blank');
          return;
        }

        // 如果是相对路径，使用Vue Router导航
        // 先检查路由是否有效
        if (!routePath || typeof routePath !== 'string') {
          console.error('无效的路由路径:', routePath);
          ElMessage.error('跳转失败，路由路径无效');
          return;
        }

        // 构建查询参数，将IP地址作为搜索条件
        const query = {
          search_ip: item.management_ip // 添加IP作为搜索参数
        };

        // 使用Vue Router进行跳转，并传递查询参数
        this.$router.push({
          path: routePath,
          query: query
        });
      } catch (err) {
        console.error('路由跳转错误:', err, routePath);
        ElMessage.error('跳转失败，请稍后重试');
      }
    },

    copyIP(ip) {
      // 创建一个临时的文本区域元素
      const textArea = document.createElement('textarea');
      textArea.value = ip;

      // 确保元素不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // 选择文本并复制
      textArea.focus();
      textArea.select();

      let succeeded = false;
      try {
        // 尝试使用现代API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(ip).then(() => {
            ElMessage.success('IP已复制到剪贴板');
          }).catch(() => {
            // 如果现代API失败，尝试传统方法
            succeeded = document.execCommand('copy');
            if (succeeded) {
              ElMessage.success('IP已复制到剪贴板');
            } else {
              ElMessage.error('复制失败，请手动复制');
            }
          });
        } else {
          // 浏览器不支持Clipboard API，使用传统方法
          succeeded = document.execCommand('copy');
          if (succeeded) {
            ElMessage.success('IP已复制到剪贴板');
          } else {
            ElMessage.error('复制失败，请手动复制');
          }
        }
      } catch (err) {
        console.error('复制过程中出错:', err);
        ElMessage.error('复制失败，请手动复制');
      } finally {
        // 清理临时元素
        document.body.removeChild(textArea);
      }
    }
  }
}
</script>
