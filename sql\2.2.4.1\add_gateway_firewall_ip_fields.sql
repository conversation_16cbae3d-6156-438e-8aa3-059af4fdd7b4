-- =====================================================
-- 互联网线路管理 - 添加网关IP和防火墙IP字段
-- 版本: *******
-- 日期: 2025-01-31
-- 描述: 为互联网线路表添加网关IP和防火墙IP字段，支持IPv4和IPv6
-- =====================================================

BEGIN;

-- 添加网关IP字段
ALTER TABLE public.cmdb_internet_lines 
ADD COLUMN IF NOT EXISTS gateway_ip character varying(50);

-- 添加防火墙IP字段
ALTER TABLE public.cmdb_internet_lines 
ADD COLUMN IF NOT EXISTS firewall_ip character varying(50);

-- 添加字段注释
COMMENT ON COLUMN public.cmdb_internet_lines.gateway_ip IS '网关IP地址，支持IPv4和IPv6格式';
COMMENT ON COLUMN public.cmdb_internet_lines.firewall_ip IS '防火墙IP地址，支持IPv4和IPv6格式';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_internet_lines_gateway_ip ON public.cmdb_internet_lines(gateway_ip);
CREATE INDEX IF NOT EXISTS idx_internet_lines_firewall_ip ON public.cmdb_internet_lines(firewall_ip);

-- 提交事务
COMMIT;

-- 输出完成信息
DO $$
BEGIN
    RAISE NOTICE '互联网线路管理 - 网关IP和防火墙IP字段添加完成！';
    RAISE NOTICE '版本: *******';
    RAISE NOTICE '已添加字段: gateway_ip, firewall_ip';
    RAISE NOTICE '已添加相关索引';
END $$;