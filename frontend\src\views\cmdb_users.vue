<style lang="scss" scoped>
.user-manage {
  .search-card {
    margin-bottom: 20px;
    height: 70px;
    padding: 5px 10px;
  }

  :deep(.el-table) {
    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap; /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all; /* 尽量保持单词完整，不强制断开 */
      max-width: 400px; /* 保持最大宽度不变 */
      width: 100%; /* 确保宽度一致 */
      word-wrap: break-word; /* 当单词超过容器宽度时允许换行 */
    }
    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }

  .pagination {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
  }
}

/* ==================== 搜索栏响应式样式 ==================== */
.form-control {
  width: 100%;
  min-width: 180px;
  max-width: 250px;
}

.search-buttons-col {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
  padding-right: 8px;
}

.button-container {
  display: flex;
  gap: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .search-buttons-col {
    justify-content: center;
  }

  .button-container {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>

<template>
  <div class="user-manage">
    <el-dialog
      v-model="dialogVisible.add"
      title="新增用户"
      width="400"
      align-center
    >
      <div class="dialogdiv">
        <!-- 用户名 -->
        <p>
          <span class="label">用户名:</span>
          <el-input
            v-model="formData.username"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 用户姓名 -->
        <p>
          <span class="label">用户姓名:</span>
          <el-input
            v-model="formData.real_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 联系电话 -->
        <p>
          <span class="label">联系电话:</span>
          <el-input
            v-model="formData.phone"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 电子邮箱 -->
        <p>
          <span class="label">电子邮箱:</span>
          <el-input
            v-model="formData.email"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 企业微信ID -->
        <p>
          <span class="label">企业微信ID:</span>
          <el-input
            v-model="formData.wechat_id"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 初始密码 -->
        <p>
          <span class="label">初始密码:</span>
          <el-input
            v-model="formData.password"
            style="width: 240px"
            clearable
            type="password"
          />
        </p>

        <p>
          <span class="label">权限代码:</span>
          <!-- 使用 el-select 替换 el-input，实现多选 -->
          <el-select
            v-model="selectedRoles"
            multiple
            style="width: 240px"
            placeholder="请选择角色"
            @change="updateRoleCode"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">返回</el-button>
          <el-button type="primary" @click="submitAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogVisible.edit"
      title="更新用户信息"
      width="400"
      align-center
    >
      <div class="dialogdiv">
        <!-- 用户名 -->
        <p>
          <span class="label">用户名:</span>
          <el-input
            v-model="formData.username"
            style="width: 240px"
            clearable
            disabled
          />
        </p>

        <!-- 用户姓名 -->
        <p>
          <span class="label">用户姓名:</span>
          <el-input
            v-model="formData.real_name"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 联系电话 -->
        <p>
          <span class="label">联系电话:</span>
          <el-input
            v-model="formData.phone"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 电子邮箱 -->
        <p>
          <span class="label">电子邮箱:</span>
          <el-input
            v-model="formData.email"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 企业微信ID -->
        <p>
          <span class="label">企业微信ID:</span>
          <el-input
            v-model="formData.wechat_id"
            style="width: 240px"
            clearable
          />
        </p>

        <!-- 交易日历编辑权限 - 只有admin用户可以配置 -->
        <p v-if="search.loginUsername === 'admin'">
          <span class="label">交易日历权限:</span>
          <el-select
            v-model="formData.calendar_duty_permission"
            style="width: 240px"
            placeholder="请选择权限"
            clearable
          >
            <el-option label="无权限" value="" />
            <el-option label="查看权限（只能查看，无法编辑）" value="view" />
            <el-option label="编辑权限（可查看，可编辑）" value="edit" />
          </el-select>
        </p>

        <!-- 密码 -->
        <p>
          <span class="label">密码:</span>
          <el-input
            v-model="formData.password"
            style="width: 240px"
            clearable
            placeholder="为空不会更新密码"
            type="password"
          />
        </p>
        <p>
          <span class="label">权限代码:</span>
          <!-- 使用 el-select 替换 el-input，实现多选 -->
          <el-select
            v-model="selectedRoles"
            multiple
            style="width: 240px"
            placeholder="请选择角色"
            :disabled="search.loginUsername !== 'admin'"
            @change="updateRoleCode"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogVisible.delete"
      title="删除用户"
      width="500"
      align-center
    >
      <el-alert
        type="warning"
        :title="`确定要删除 IP 为 ${formData.username} 的记录吗？`"
        :closable="false"
      />
      <template #footer>
        <div>
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <el-card class="search-card">
      <el-form :model="search" ref="searchFormRef" label-width="80px" label-position="right">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="用户名">
              <el-input
                v-model="search.username"
                placeholder="请输入用户名"
                clearable
                @keyup.enter="loadData"
                class="form-control"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="18" class="search-buttons-col">
            <el-form-item>
              <div class="button-container">
                <el-button type="primary" @click="loadData">
                  <el-icon><Search /></el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
                <el-button
                  type="success"
                  :disabled="search.loginUsername !== 'admin'"
                  @click="handleAdd"
                >
                  <el-icon><Plus /></el-icon>新增
                </el-button>
                <el-button type="info" @click="exportData">
                  <el-icon><Download /></el-icon>导出
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="userArr"
        ref="table"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange"
      >
        <!-- 主键 -->
        <el-table-column prop="id" label="序号" v-if="false"></el-table-column>

        <!-- 用户名 -->
        <el-table-column
          prop="username"
          label="用户名"
          sortable
        ></el-table-column>

        <!-- 用户姓名 -->
        <el-table-column
          prop="real_name"
          label="用户姓名"
          sortable
        ></el-table-column>

        <!-- 联系电话 -->
        <el-table-column
          prop="phone"
          label="联系电话"
          sortable
        ></el-table-column>

        <!-- 电子邮箱 -->
        <el-table-column
          prop="email"
          label="电子邮箱"
          sortable
        ></el-table-column>

        <!-- 企业微信ID -->
        <el-table-column
          prop="wechat_id"
          label="企业微信ID"
          sortable
        ></el-table-column>

        <!-- 角色名称 -->
        <el-table-column
          prop="role_code_name"
          label="权限"
          sortable
        ></el-table-column>
        <!-- 角色代码 -->
        <el-table-column
          v-if="false"
          prop="role_code"
          label="权限代码"
          sortable
        ></el-table-column>

        <!-- 创建时间 -->
        <el-table-column
          prop="created_at"
          label="创建时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="created_by"
          label="创建人"
          sortable
        ></el-table-column>
        <el-table-column
          prop="updated_at"
          label="更新时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="updated_by"
          label="更新人"
          sortable
        ></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap">
              <el-button
                size="small"
                type="warning"
                :disabled="search.loginUsername !== 'admin' && scope.row.username !== search.loginUsername"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                size="small"
                type="danger"
                :disabled="search.loginUsername !== 'admin'"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          background
          :current-page="search.currentPage"
          :page-size="search.pageSize"
          :total="search.total"
          :page-sizes="[10, 20, 50, 100, 1000, 10000]"
          :pager-count="5"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from "element-plus";

import { Plus, Search, Download } from "@element-plus/icons-vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export default {
  components: {
    Plus,
    Search,
    Download,
  },

  data() {
    return {
      userArr: [], // 监控IP列表
      loading: false, // 加载状态
      showRoleColumn: false, // 设置为 false 以隐藏角色列
      hasDeletePermission: localStorage.getItem("role_code")?.includes("D"), // 是否有删除权限
      hasUpdatePermission: localStorage.getItem("role_code")?.includes("U"), // 是否有删除权限
      hasInsertPermission: localStorage.getItem("role_code")?.includes("I"), // 是否有删除权限

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false,
        delete: false,
      },
      // 查询数据 - 强制初始化为空字符串
      search: {
        username: "", // 强制为空字符串
        total: 0, // 总记录数
        pageSize: 10, // 每页显示条目数
        currentPage: 1, // 当前页码
        loginUsername: "", // 登陆用户 - 初始为空，在mounted中设置
        sortProp: "updated_at", // 排序字段
        sortOrder: "desc", // 排序顺序
      },

      // 表单数据
      formData: {
        id: null,
        username: "",
        password: "",
        role_code: "",
        created_at: null,
        role_code_name: "",
        real_name: "",
        phone: "",
        email: "",
        wechat_id: "",
        calendar_duty_permission: "", // 交易日历权限
      },
      selectedRoles: [], // 默认选择 'S'
      // 角色选项列表
      roleOptions: [
        { value: "I", label: "I:增" },
        { value: "D", label: "D:删" },
        { value: "U", label: "U:改" },
        // { value: 'S', label: 'S:查' },
      ],
    };
  },
  mounted() {
    // 确保loginUsername是最新的
    this.search.loginUsername = localStorage.getItem("loginUsername") || "";
    // 加载所有用户数据
    this.loadAllData();
  },
  methods: {
    // 页码选择
    handlePageChange(newPage) {
      this.search.currentPage = newPage;
      this.loadData();
    },

    // 每页显示条目数变化
    handlePageSizeChange(newSize) {
      this.search.pageSize = parseInt(newSize);
      this.search.currentPage = 1; // 重置当前页码为第一页
      this.loadData();
    },

    // 更新 role_code 字符串
    updateRoleCode(selectedValues) {
      // 将选择的值拼接成字符串
      this.formData.role_code = selectedValues.join(",");
    },

    //增加排序
    handleSortChange({ column, prop, order }) {
      this.search.sortProp = prop;
      this.search.sortOrder = order === "ascending" ? "asc" : "desc";
      this.loadData();
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        
        const response = await this.$axios.post(
          `/api/get_cmdb_users`,
          this.search
        );
        
        this.userArr = response.data.msg;
        this.search.total = response.data.total;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading = false;
      }
    },

    // 重置搜索条件并加载所有数据
    async loadAllData() {
      this.search.username = ""; // 清空搜索条件
      this.search.currentPage = 1; // 重置页码
      await this.loadData();
    },

    // 重置搜索条件
    resetSearch() {
      // 手动重置所有搜索字段到默认值
      this.search.username = "";
      this.search.currentPage = 1;

      // 如果表单引用存在，也调用resetFields方法
      if (this.$refs.searchFormRef) {
        this.$refs.searchFormRef.resetFields();
      }

      // 重新加载数据
      this.loadData();

      // 提示用户
      this.$message.success('搜索条件已重置');
    },

    // 添加用户
    async submitAdd() {
      this.formData.username = this.formData.username.trim();

      if (!this.formData.username || !this.formData.password) {
        this.$message.error("请输入用户名和密码！");
        return;
      }

      // 创建提交数据对象，添加操作者信息
      const submitData = { ...this.formData };
      submitData.usernameby = this.search.loginUsername;

      try {
        await this.$axios.post(`/api/add_cmdb_users`, submitData);
        this.$message.success("添加成功");
        this.dialogVisible.add = false;
        this.loadData();
      } catch (error) {
        console.error("添加失败:", error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$message.error(`添加失败: ${error.response.data.msg}`);
        } else {
          this.$message.error("添加失败");
        }
      }
    },

    // 获取用户的交易日历权限
    async loadCalendarDutyPermission(username) {
      try {
        const response = await this.$axios.post('/api/check_ops_calendar_duty_permission', {
          username: username
        });

        if (response.data.code === 0) {
          const permissions = response.data.msg;
          console.log('获取到的权限信息:', permissions);

          if (permissions.hasEditPermission) {
            this.formData.calendar_duty_permission = 'edit';
          } else if (permissions.hasViewPermission) {
            this.formData.calendar_duty_permission = 'view';
          } else {
            this.formData.calendar_duty_permission = '';
          }

          console.log('设置的权限值:', this.formData.calendar_duty_permission);
        } else {
          this.formData.calendar_duty_permission = '';
        }
      } catch (error) {
        console.error('获取交易日历权限失败:', error);
        this.formData.calendar_duty_permission = '';
      }
    },

    // 保存用户的交易日历权限
    async saveCalendarDutyPermission(username, permission) {
      if (this.search.loginUsername !== 'admin') {
        return; // 只有admin用户可以设置权限
      }

      try {
        const response = await this.$axios.post('/api/update_ops_calendar_duty_permission', {
          username: username,
          permission_type: permission,
          updated_by: this.search.loginUsername
        });

        if (response.data.code !== 0) {
          console.error('保存交易日历权限失败:', response.data.msg);
        }
      } catch (error) {
        console.error('保存交易日历权限失败:', error);
      }
    },

    // 编辑用户
    async submitEdit() {
      // 创建一个新的表单数据对象，避免修改原始对象
      const formDataToSubmit = { ...this.formData };
      
      // 添加当前登录用户名作为操作者
      formDataToSubmit.usernameby = this.search.loginUsername;

      // 如果不是管理员，不发送角色权限字段
      if (this.search.loginUsername !== 'admin') {
        delete formDataToSubmit.role_code;
        delete formDataToSubmit.calendar_duty_permission;
      }

      try {
        const response = await this.$axios.post(`/api/update_cmdb_users`, formDataToSubmit);
        if (response.data.code === 0) {
          // 如果是admin用户，还需要保存交易日历权限
          if (this.search.loginUsername === 'admin') {
            await this.saveCalendarDutyPermission(
              this.formData.username,
              this.formData.calendar_duty_permission
            );
          }

          this.$message.success("更新成功");
          this.dialogVisible.edit = false;
          this.loadData();
        } else {
          this.$message.error(`更新失败: ${response.data.msg}`);
        }
      } catch (error) {
        console.error("更新失败:", error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$message.error(`更新失败: ${error.response.data.msg}`);
        } else {
          this.$message.error("更新失败");
        }
      }
    },

    // 删除用户
    async submitDelete(row) {
      // 创建删除请求数据
      const deleteData = {
        id: this.formData.id,
        usernameby: this.search.loginUsername
      };

      try {
        const response = await this.$axios.post(
          `/api/del_cmdb_users`,
          deleteData
        );
        if (response.data.code === 0) {
          ElMessage.success("删除成功");
        } else {
          ElMessage.error(`删除失败: ${response.data.msg}`);
        }
        this.loadData();
        this.dialogVisible.delete = false;
      } catch (error) {
        console.error("删除失败:", error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$message.error(`删除失败: ${error.response.data.msg}`);
        } else {
          this.$message.error("删除失败");
        }
      }
    },

    // 新增效果实现
    handleAdd(index, row) {
      // 非admin用户不能添加新用户
      if (this.search.loginUsername !== 'admin') {
        ElMessage.error('只有管理员可以添加用户');
        return;
      }

      this.dialogVisible.add = !this.dialogVisible.add;
      this.formData = {
        username: "",
        password: "",
        real_name: "",
        phone: "",
        email: "",
        wechat_id: "",
        role_code: ""
      };
      this.selectedRoles = [];
    },

    // 编辑按钮实现
    async handleEdit(index, row) {
      // 非admin用户只能编辑自己的信息
      if (this.search.loginUsername !== 'admin' && row.username !== this.search.loginUsername) {
        ElMessage.error('您只能编辑自己的信息');
        return;
      }

      this.dialogVisible.edit = true;
      this.formData.id = row.id;
      this.formData.username = row.username;
      this.formData.real_name = row.real_name || "";
      this.formData.phone = row.phone || "";
      this.formData.email = row.email || "";
      this.formData.wechat_id = row.wechat_id || "";
      this.formData.password = "";
      this.formData.role_code = row.role_code;
      this.selectedRoles = row.role_code ? row.role_code.replace(/^,|,$/g, "").split(",") : [];

      // 获取交易日历权限（只有admin用户可以看到）
      if (this.search.loginUsername === 'admin') {
        await this.loadCalendarDutyPermission(row.username);
      }
    },

    // 删除效果实现
    handleDelete(index, row) {
      // 非admin用户不能删除任何用户
      if (this.search.loginUsername !== 'admin') {
        ElMessage.error('只有管理员可以删除用户');
        return;
      }

      this.dialogVisible.delete = !this.dialogVisible.delete;
      this.formData.id = row.id;
      this.formData.username = row.username;
    },
    //导出数据
    exportData() {
      const table = this.$refs.table; // 获取 el-table 实例
      const columns = table.columns; // 获取表头
      const headers = columns.map((col) => col.label); // 获取表头
      const data = this.userArr.map((row) =>
        columns.map((col) => row[col.property])
      ); // 获取表格数据

      const wsData = [headers, ...data]; // 将表头和数据合并
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const blob = new Blob([wbout], { type: "application/octet-stream" });
      saveAs(blob, "用户管理.xlsx");
    },
  },
};
</script>