/**
 * VMware虚拟化信息管理API接口
 */
import axios from '@/utils/request'

export const vmwareApi = {
  // 获取VMware主机列表
  getVmwareHosts(params) {
    return axios.post('/api/get_vmware_hosts', params)
  },

  // 获取VMware主机详情
  getVmwareHostDetail(params) {
    return axios.post('/api/get_vmware_host_detail', params)
  },

  // 删除VMware主机记录
  deleteVmwareHost(params) {
    return axios.post('/api/delete_vmware_host', params)
  },

  // 批量删除VMware主机记录
  batchDeleteVmwareHosts(params) {
    return axios.post('/api/batch_delete_vmware_hosts', params)
  },

  // 导出VMware主机数据
  exportVmwareHosts(params) {
    return axios.post('/api/export_vmware_hosts', params)
  },

  // 手动触发同步
  triggerVmwareSync() {
    return axios.post('/api/trigger_vmware_sync')
  },

  // 获取同步状态
  getVmwareSyncStatus() {
    return axios.post('/api/get_vmware_sync_status')
  },

  // 获取同步历史记录
  getVmwareSyncHistory(params) {
    return axios.post('/api/get_vmware_sync_history', params)
  },

  // 获取统计信息
  getVmwareStatistics() {
    return axios.post('/api/get_vmware_statistics')
  }
}