/**
 * 变更管理控制器
 */
const { connPG } = require('../../db/pg');
const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { upload, getFileUrl, deleteFile } = require('../../utils/file-upload');
const XLSX = require('xlsx'); // 用于检测Excel sheet数量

// 初始化数据库表和序列
const initDatabase = async () => {
  try {
    // 检查表是否存在
    const tableCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ops_change_management'
      );
    `);

    const tableExists = tableCheckResult.rows[0].exists;

    if (!tableExists) {
      console.log('创建变更管理表...');

      // 创建表
      await connPG.query(`
        CREATE TABLE "public"."ops_change_management" (
          "id" SERIAL PRIMARY KEY,
          "change_id" varchar(20) NOT NULL,
          "title" varchar(255) NOT NULL,
          "system" varchar(100) NOT NULL,
          "change_level" varchar(10) NOT NULL,
          "planned_change_time" date NOT NULL,
          "requester" varchar(50) NOT NULL,
          "implementers" text NOT NULL,
          "description" text,
          "oa_process" boolean DEFAULT false,
          "oa_process_file" varchar(255),
          "signed_archive" boolean DEFAULT false,
          "signed_archive_file" varchar(255),
          "operation_sheet" varchar(255),
          "supplementary_material" varchar(255),
          "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
          "created_by" varchar(50),
          "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
          "updated_by" varchar(50),
          "del_flag" char(1) DEFAULT '0'
        );
      `);

      // 添加注释
      await connPG.query(`
        COMMENT ON COLUMN "public"."ops_change_management"."change_id" IS '变更编号，格式：BG-YYYYMMDD-序号';
        COMMENT ON COLUMN "public"."ops_change_management"."title" IS '变更名称';
        COMMENT ON COLUMN "public"."ops_change_management"."system" IS '所属系统，来自系统管理员责任表（公司）的业务系统简称';
        COMMENT ON COLUMN "public"."ops_change_management"."change_level" IS '变更级别，使用数据字典P';
        COMMENT ON COLUMN "public"."ops_change_management"."planned_change_time" IS '计划变更时间';
        COMMENT ON COLUMN "public"."ops_change_management"."requester" IS '变更负责人，存储username';
        COMMENT ON COLUMN "public"."ops_change_management"."implementers" IS '变更实施人，存储多个username，以逗号分隔';
        COMMENT ON COLUMN "public"."ops_change_management"."description" IS '变更描述';
        COMMENT ON COLUMN "public"."ops_change_management"."oa_process" IS 'OA流程是否上传';
        COMMENT ON COLUMN "public"."ops_change_management"."oa_process_file" IS 'OA流程文件路径';
        COMMENT ON COLUMN "public"."ops_change_management"."signed_archive" IS '签字存档是否上传';
        COMMENT ON COLUMN "public"."ops_change_management"."signed_archive_file" IS '签字存档文件路径';
        COMMENT ON COLUMN "public"."ops_change_management"."operation_sheet" IS '变更操作表文件路径';
      `);

      // 创建索引
      await connPG.query(`
        CREATE INDEX "idx_ops_change_management_change_id" ON "public"."ops_change_management" ("change_id");
        CREATE INDEX "idx_ops_change_management_system" ON "public"."ops_change_management" ("system");
        CREATE INDEX "idx_ops_change_management_change_level" ON "public"."ops_change_management" ("change_level");
        CREATE INDEX "idx_ops_change_management_requester" ON "public"."ops_change_management" ("requester");
        CREATE INDEX "idx_ops_change_management_implementers" ON "public"."ops_change_management" USING gin(to_tsvector('simple', "implementers"));
        CREATE INDEX "idx_ops_change_management_del_flag" ON "public"."ops_change_management" ("del_flag");
      `);
    }

    // 检查序列是否存在
    const seqCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.sequences
        WHERE sequence_schema = 'public'
        AND sequence_name = 'ops_change_id_seq'
      );
    `);

    const seqExists = seqCheckResult.rows[0].exists;

    if (!seqExists) {
      console.log('创建变更编号序列...');

      // 创建序列
      await connPG.query(`
        CREATE SEQUENCE "public"."ops_change_id_seq"
          INCREMENT 1
          MINVALUE 1
          MAXVALUE 9999
          START 1
          CACHE 1
          CYCLE;
      `);
    }

    // 检查视图是否存在
    const viewCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.views
        WHERE table_schema = 'public'
        AND table_name = 'v_ops_change_management'
      );
    `);

    const viewExists = viewCheckResult.rows[0].exists;

    if (!viewExists) {
      console.log('创建变更管理视图...');

      // 创建视图
      await connPG.query(`
        CREATE VIEW "public"."v_ops_change_management" AS
        SELECT
          t.id,
          t.change_id,
          t.title,
          t.system,
          t.change_level,
          COALESCE(t2.dict_name, t.change_level) AS change_level_name_display,
          t.planned_change_time,
          to_char(t.planned_change_time, 'YYYY-MM-DD') || ' ' ||
          CASE EXTRACT(DOW FROM t.planned_change_time)
            WHEN 0 THEN '星期日'
            WHEN 1 THEN '星期一'
            WHEN 2 THEN '星期二'
            WHEN 3 THEN '星期三'
            WHEN 4 THEN '星期四'
            WHEN 5 THEN '星期五'
            WHEN 6 THEN '星期六'
          END AS formatted_change_time,
          t.requester,
          u1.real_name AS requester_name,
          t.implementers,
          (
            SELECT string_agg(u.real_name, ', ')
            FROM (
              SELECT unnest(string_to_array(t.implementers, ',')) AS username
            ) AS usernames
            LEFT JOIN cmdb_users u ON u.username = usernames.username AND u.del_flag = '0'
          ) AS implementers_name,
          t.description,
          t.oa_process,
          t.oa_process_file,
          t.signed_archive,
          t.signed_archive_file,
          t.operation_sheet,
          t.supplementary_material,
          t.created_at,
          t.created_by,
          t.updated_at,
          t.updated_by
        FROM
          ops_change_management t
        LEFT JOIN
          cmdb_data_dictionary t2 ON t2.dict_type = 'P' AND t2.dict_code = t.change_level AND t2.del_flag = '0'
        LEFT JOIN
          cmdb_users u1 ON u1.username = t.requester AND u1.del_flag = '0'
        WHERE
          t.del_flag = '0';
      `);
    }

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('初始化数据库失败:', error);
  }
};

// 执行初始化
initDatabase();

// 检测Excel文件的sheet数量
const getExcelSheetCount = (filePath) => {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheetNames = workbook.SheetNames;
    return {
      count: sheetNames.length,
      sheets: sheetNames
    };
  } catch (error) {
    console.error('读取Excel文件失败:', error);
    return {
      count: 0,
      sheets: []
    };
  }
};

// 处理上传的文件信息
const processUploadedFile = async (file, formData) => {
  try {
    console.log('处理上传的文件信息:', file);
    console.log('表单数据:', formData);

    if (!file) {
      throw new Error('未找到上传的文件');
    }

    // 处理文件名乱码问题
    let originalname = file.originalname;
    try {
      // 尝试解码文件名
      if (Buffer.from(originalname).toString('utf8') !== originalname) {
        originalname = Buffer.from(originalname, 'latin1').toString('utf8');
      }
    } catch (error) {
      console.error('文件名解码失败:', error);
    }

    console.log('原始文件名:', file.originalname);
    console.log('处理后文件名:', originalname);

    // 确保上传目录存在
    const { fileType } = formData;
    let uploadPath;

    // 创建按年月组织的子目录
    const yearMonth = format(new Date(), 'yyyy-MM');

    switch (fileType) {
      case 'oa_process':
        uploadPath = path.join(process.env.FILE_UPLOAD_OA_PATH, yearMonth);
        break;
      case 'signed_archive':
        uploadPath = path.join(process.env.FILE_UPLOAD_SIGNED_PATH, yearMonth);
        break;
      case 'operation_sheet':
        uploadPath = path.join(process.env.FILE_UPLOAD_OPERATION_PATH, yearMonth);
        break;
      case 'supplementary_material':
        uploadPath = path.join(process.env.FILE_UPLOAD_SUPPLEMENTARY_PATH, yearMonth);
        break;
      default:
        uploadPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, yearMonth);
    }

    // 确保目录存在
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    // 获取文件相对路径（相对于上传根目录）
    const relativePath = path.relative(
      path.resolve(process.env.FILE_UPLOAD_BASE_PATH),
      path.resolve(file.path)
    ).replace(/\\/g, '/');

    // 构建文件URL
    const fileUrl = getFileUrl(relativePath);

    return {
      originalname: originalname, // 使用处理后的文件名
      filename: file.filename,
      path: relativePath,
      url: fileUrl
    };
  } catch (error) {
    console.error('处理上传的文件信息失败:', error);
    throw error;
  }
};

// 生成变更编号
const generateChangeId = async () => {
  try {
    // 获取当前日期
    const today = new Date();
    const dateStr = format(today, 'yyyyMMdd');

    // 查询今日已存在的最大序号
    const seqResult = await connPG.query(
      `SELECT change_id FROM ops_change_management WHERE change_id LIKE $1 ORDER BY change_id DESC LIMIT 1`,
      [`BG-${dateStr}-%`]
    );
    let seqValue = 1;
    if (seqResult.rows.length > 0) {
      // 提取已有最大序号
      const lastId = seqResult.rows[0].change_id;
      const match = lastId.match(/BG-\d{8}-(\d{4})$/);
      if (match) {
        seqValue = parseInt(match[1], 10) + 1;
      }
    }
    // 格式化序列号为4位数，不足前面补0
    const seqStr = String(seqValue).padStart(4, '0');
    // 组合成变更编号: BG-YYYYMMDD-序号
    return `BG-${dateStr}-${seqStr}`;
  } catch (error) {
    console.error('生成变更编号失败:', error);
    throw error;
  }
};

// 获取变更管理列表
const getChangeManagementList = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('请求体:', req.body);

    const {
      id, // 添加ID参数，用于获取单个变更记录
      changeLevel,
      change_level, // 添加备用参数名
      system, // 添加变更系统参数
      requester, // 添加变更负责人参数
      implementer, // 添加变更实施人参数
      nonCompliant, // 添加未规范筛选参数
      oaProcess, // 添加OA流程参数
      signedArchive, // 添加签字存档参数
      isOverdue, // 添加过期状态筛选
      startDate, // 添加开始日期参数
      endDate, // 添加结束日期参数
      keyword, // 添加关键词参数
      currentPage = 1, // 添加当前页码参数
      pageSize = 10, // 添加每页条数参数
      sortProp = 'change_id', // 添加排序字段参数
      sortOrder = 'desc' // 添加排序顺序参数
    } = req.body;

    // 构建查询条件
    const conditions = [];
    const params = [];
    let paramIndex = 1;

    // 如果提供了ID参数，优先按ID查询单个记录
    if (id) {
      console.log('使用ID过滤:', id);
      conditions.push(`id = $${paramIndex++}`);
      params.push(id);
    }

    // 变更级别 - 支持两种参数名
    const effectiveChangeLevel = changeLevel || change_level;
    if (effectiveChangeLevel) {
      console.log('使用变更级别过滤:', effectiveChangeLevel);
      conditions.push(`change_level = $${paramIndex++}`);
      params.push(effectiveChangeLevel);
    }

    // 变更系统
    if (system) {
      console.log('使用变更系统过滤:', system);
      conditions.push(`system LIKE $${paramIndex++}`);
      params.push(`%${system}%`);
    }

    // 变更负责人
    if (requester) {
      conditions.push(`(requester = $${paramIndex++} OR requester_name LIKE $${paramIndex++})`);
      params.push(requester);
      params.push(`%${requester}%`);
    }

    // 变更实施人
    if (implementer) {
      conditions.push(`(implementers LIKE $${paramIndex++} OR implementers_name LIKE $${paramIndex++})`);
      params.push(`%${implementer}%`);
      params.push(`%${implementer}%`);
    }

    // OA流程
    if (oaProcess === '已上传' || oaProcess === '未上传') {
      conditions.push(`oa_process = $${paramIndex++}`);
      params.push(oaProcess === '已上传');
    }

    // 签字存档
    if (signedArchive === '已上传' || signedArchive === '未上传') {
      conditions.push(`signed_archive = $${paramIndex++}`);
      params.push(signedArchive === '已上传');
    }

    // 未规范筛选（未上传OA流程或未上传签字存档）
    if (nonCompliant === '是') {
      conditions.push(`((oa_process = false OR signed_archive = false) and planned_change_time >= '2025-01-01')`);
    } else if (nonCompliant === '否') {
      conditions.push(`(oa_process = true AND signed_archive = true )`);
    }

    // 是否过期筛选
    if (isOverdue === '已过期' || isOverdue === '未过期') {
      console.log('使用过期状态过滤:', isOverdue);
      if (isOverdue === '已过期') {
        conditions.push(`planned_change_time < CURRENT_DATE`);
      } else {
        conditions.push(`planned_change_time >= CURRENT_DATE`);
      }
    }

    // 日期范围
    if (startDate && endDate) {
      conditions.push(`planned_change_time BETWEEN $${paramIndex++} AND $${paramIndex++}`);
      params.push(startDate);
      params.push(endDate);
    } else if (startDate) {
      conditions.push(`planned_change_time >= $${paramIndex++}`);
      params.push(startDate);
    } else if (endDate) {
      conditions.push(`planned_change_time <= $${paramIndex++}`);
      params.push(endDate);
    }

    // 关键词搜索（变更编号或变更名称）
    if (keyword) {
      conditions.push(`(change_id ILIKE $${paramIndex++} OR title ILIKE $${paramIndex++})`);
      params.push(`%${keyword}%`);
      params.push(`%${keyword}%`);
    }

    // 构建WHERE子句
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // 构建排序子句
    const orderClause = `ORDER BY ${sortProp} ${sortOrder.toUpperCase()}`;

    // 查询总记录数 - 使用视图
    const countQuery = `
      SELECT COUNT(*) AS total
      FROM v_ops_change_management
      ${whereClause}
    `;

    const countResult = await connPG.query(countQuery, params);
    const total = parseInt(countResult.rows[0].total);

    // 计算分页参数
    const offset = (currentPage - 1) * pageSize;

    // 查询分页数据 - 使用视图
    const dataQuery = `
      SELECT
        id,
        change_id,
        title,
        system,
        change_level,
        change_level_name_display,
        planned_change_time,
        formatted_change_time,
        requester,
        requester_name,
        implementers,
        implementers_name,
        oa_process,
        oa_process_file,
        signed_archive,
        signed_archive_file,
        operation_sheet,
        supplementary_material,
        created_at,
        created_by,
        updated_at,
        updated_by
      FROM v_ops_change_management
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    const dataParams = [...params, pageSize, offset];
    const dataResult = await connPG.query(dataQuery, dataParams);

    // 返回结果
    res.json({
      code: 0,
      msg: dataResult.rows,
      total
    });
  } catch (error) {
    console.error('获取变更管理列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取系统列表（从系统管理员责任表公司中获取）
const getSystemList = async (_, res) => {
  try {
    const query = `
      SELECT system_abbreviation
      FROM cmdb_system_admin_responsibility_company
      WHERE del_flag = '0'
      ORDER BY system_abbreviation
    `;

    const result = await connPG.query(query);

    res.json({
      code: 0,
      msg: result.rows
    });
  } catch (error) {
    console.error('获取系统列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取用户列表
const getUserList = async (_, res) => {
  try {
    const query = `
      SELECT username, real_name
      FROM cmdb_users
      WHERE del_flag = '0'
      ORDER BY username
    `;

    const result = await connPG.query(query);

    res.json({
      code: 0,
      msg: result.rows
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 添加变更管理记录
const addChangeManagement = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('添加变更管理记录请求体:', req.body);

    const {
      title,
      system,
      changeLevel,
      change_level, // 支持前端使用下划线命名
      plannedChangeTime,
      planned_change_time, // 支持前端使用下划线命名
      requester,
      implementers,
      username,
      created_by,
      updated_by
    } = req.body;

    // 使用驼峰命名或下划线命名，以支持不同的前端命名风格
    const effectiveChangeLevel = changeLevel || change_level;
    const effectivePlannedChangeTime = plannedChangeTime || planned_change_time;

    // 生成变更编号
    const changeId = await generateChangeId();

    // 构建插入语句
    const query = `
      INSERT INTO ops_change_management (
        change_id,
        title,
        system,
        change_level,
        planned_change_time,
        requester,
        implementers,
        created_by,
        updated_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    // 确定创建者和更新者
    const effectiveCreatedBy = created_by || username || 'admin';
    const effectiveUpdatedBy = updated_by || username || 'admin';

    const params = [
      changeId,
      title,
      system,
      effectiveChangeLevel, // 使用有效的变更级别
      effectivePlannedChangeTime, // 使用有效的计划变更时间
      requester,
      implementers, // 使用多选的实施人，以逗号分隔的字符串
      effectiveCreatedBy,
      effectiveUpdatedBy
    ];

    const result = await connPG.query(query, params);

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '新增变更记录',
        change_id: changeId,
        title: title,
        system: system,
        change_level: effectiveChangeLevel
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/add_ops_change_management',
        JSON.stringify(logBody),
        effectiveCreatedBy,
        'add'
      ]);

      console.log(`已记录用户操作日志: ${effectiveCreatedBy} 新增了变更记录 ${changeId}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('添加变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 上传文件
const uploadFile = async (req, res) => {
  try {
    // 直接使用multer处理上传，而不是先检查req.body
    // multer会将文件信息存储在req.file中，将表单字段存储在req.body中
    const fileUploadMiddleware = upload.single('file');

    await new Promise((resolve, reject) => {
      fileUploadMiddleware(req, res, (err) => {
        if (err) {
          console.error('文件上传中间件错误:', err);
          return reject(err);
        }
        resolve();
      });
    });

    console.log('文件上传中间件处理完成');
    console.log('收到文件上传请求，请求体:', req.body);
    console.log('文件信息:', req.file);
    console.log('请求头:', req.headers);

    const { changeId, fileType } = req.body;

    console.log('变更ID:', changeId);
    console.log('文件类型:', fileType);

    if (!changeId) {
      console.log('错误: 缺少变更编号参数');
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      console.log('错误: 无效的文件类型:', fileType);
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    if (!req.file) {
      console.log('错误: 未找到上传的文件');
      return res.status(400).json({ code: 1, msg: '未找到上传的文件' });
    }

    console.log('开始处理文件信息...');
    // 处理文件信息
    const fileInfo = await processUploadedFile(req.file, req.body);


    // 更新数据库记录
    let updateQuery;
    let updateParams;

    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = true, oa_process_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.username || 'admin', changeId];
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = true, signed_archive_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.username || 'admin', changeId];
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.username || 'admin', changeId];
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.username || 'admin', changeId];
    }

    console.log('执行数据库更新，SQL:', updateQuery);
    console.log('参数:', updateParams);

    const result = await connPG.query(updateQuery, updateParams);
    console.log('数据库更新结果:', result.rows);

    if (result.rows.length === 0) {
      console.log('错误: 未找到指定的变更记录');
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    console.log('文件上传成功，返回结果');
    res.json({
      code: 0,
      msg: {
        ...fileInfo,
        changeId,
        fileType
      }
    });
  } catch (error) {
    console.error('文件上传失败:', error);
    console.error('错误堆栈:', error.stack);

    // 返回更详细的错误信息
    res.status(500).json({
      code: 1,
      msg: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      details: '文件上传失败，请检查服务器日志'
    });
  }
};

// 简化版文件上传API
const uploadFileSimple = async (req, res) => {
  try {
    console.log('收到简化版文件上传请求');
    console.log('请求头:', req.headers);
    console.log('Content-Type:', req.headers['content-type']);

    // 使用multer处理文件上传
    const fileUploadMiddleware = upload.single('file');

    await new Promise((resolve, reject) => {
      fileUploadMiddleware(req, res, (err) => {
        if (err) {
          console.error('文件上传中间件错误:', err);
          console.error('错误类型:', err.constructor.name);
          console.error('错误消息:', err.message);
          console.error('错误堆栈:', err.stack);
          return reject(err);
        }
        console.log('multer中间件处理完成');
        resolve();
      });
    });

    console.log('文件上传中间件处理完成');
    console.log('请求查询参数:', req.query);
    console.log('请求体:', req.body);
    console.log('文件信息:', req.file);

    // 从URL查询参数获取变更ID和文件类型
    const changeId = req.query.changeId || req.body.changeId;
    const fileType = req.query.fileType || req.body.fileType;
    const username = req.query.username || req.body.username || 'admin';

    console.log('变更ID:', changeId);
    console.log('文件类型:', fileType);
    console.log('用户名:', username);

    if (!changeId) {
      console.log('错误: 缺少变更编号参数');
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      console.log('错误: 无效的文件类型:', fileType);
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    if (!req.file) {
      console.log('错误: 未找到上传的文件');
      return res.status(400).json({ code: 1, msg: '未找到上传的文件' });
    }

    // 添加文件信息日志
    console.log('文件详细信息:');
    console.log('- 原始文件名:', req.file.originalname);
    console.log('- MIME类型:', req.file.mimetype);
    console.log('- 文件大小:', req.file.size);
    console.log('- 存储路径:', req.file.path);
    console.log('- 字段名:', req.file.fieldname);

    // 处理文件信息
    const fileInfo = await processUploadedFile(req.file, { changeId, fileType, username });

    // 更新数据库记录
    let updateQuery;
    let updateParams;

    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = true, oa_process_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, username, changeId];
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = true, signed_archive_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, username, changeId];
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, username, changeId];
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, username, changeId];
    }

    const result = await connPG.query(updateQuery, updateParams);

    if (result.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    res.json({
      code: 0,
      msg: {
        ...fileInfo,
        changeId,
        fileType
      }
    });
  } catch (error) {
    console.error('简化版文件上传失败:', error);
    console.error('错误堆栈:', error.stack);

    // 返回更详细的错误信息
    res.status(500).json({
      code: 1,
      msg: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      details: '文件上传失败，请检查服务器日志'
    });
  }
};

// 下载文件
const downloadFile = async (req, res) => {
  try {
    const { changeId, fileType, direct } = req.query;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 查询文件路径和变更信息
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path, change_id, title FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path, change_id, title FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path, change_id, title FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path, change_id, title FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;
    const changeIdFromDB = result.rows[0].change_id;
    const changeTitle = result.rows[0].title;
    const fullPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, filePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ code: 1, msg: '文件不存在' });
    }

    // 检查是否是直接下载请求
    if (direct === 'true') {
      // 直接提供文件下载，而不是返回URL
      console.log('直接提供文件下载:', fullPath);

      // 从文件路径中提取原始文件名和扩展名
      const originalFileName = path.basename(filePath);
      const fileExtension = path.extname(originalFileName);
      
      // 构建新的文件名：变更编号_变更名称.扩展名
      // 清理文件名中的特殊字符，避免下载问题
      const cleanTitle = changeTitle.replace(/[<>:"/\\|?*]/g, '_');
      const newFileName = `${changeIdFromDB}_${cleanTitle}${fileExtension}`;

      // 设置响应头，强制下载
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(newFileName)}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      // 创建文件读取流并通过管道传输到响应
      const fileStream = fs.createReadStream(fullPath);
      fileStream.pipe(res);

      // 处理错误
      fileStream.on('error', (err) => {
        console.error('文件流错误:', err);
        if (!res.headersSent) {
          res.status(500).json({ code: 1, msg: '文件读取失败' });
        }
      });

      return; // 直接返回，不执行后面的代码
    }

    // 获取文件URL
    const fileUrl = getFileUrl(filePath);

    // 返回文件URL和直接下载URL
    res.json({
      code: 0,
      msg: {
        url: fileUrl,
        path: filePath,
        directDownloadUrl: `/api/download_ops_change_file?changeId=${changeId}&fileType=${fileType}&direct=true`
      }
    });
  } catch (error) {
    console.error('获取文件下载链接失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 预览文件
const previewFile = async (req, res) => {
  try {
    const { changeId, fileType } = req.query;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 查询文件路径
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;
    const fullPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, filePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ code: 1, msg: '文件不存在' });
    }

    // 获取文件扩展名以确定文件类型
    const fileExtension = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);

    // 根据文件类型设置Content-Type
    let contentType = 'application/octet-stream';
    let isInlinePreview = false;

    switch (fileExtension) {
      case '.pdf':
        contentType = 'application/pdf';
        isInlinePreview = true;
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        isInlinePreview = true;
        break;
      case '.png':
        contentType = 'image/png';
        isInlinePreview = true;
        break;
      case '.gif':
        contentType = 'image/gif';
        isInlinePreview = true;
        break;
      case '.doc':
      case '.docx':
      case '.xls':
      case '.xlsx':
        // Office文档支持在线预览，提供多种预览方案
        // 使用配置工具动态构建BASE_URL，避免重复配置
        const { getBaseUrl } = require('../../utils/config');
        const baseUrl = getBaseUrl();
        const fileDownloadUrl = `${baseUrl}/api/download_ops_change_file?changeId=${changeId}&fileType=${fileType}&direct=true`;
        
        // 检测Excel文件的sheet数量
        let excelInfo = null;
        let isMultiSheetExcel = false;
        if (fileExtension === '.xls' || fileExtension === '.xlsx') {
          excelInfo = getExcelSheetCount(fullPath);
          isMultiSheetExcel = excelInfo.count > 1;
          console.log(`Excel文件 ${fileName} 包含 ${excelInfo.count} 个sheet页:`, excelInfo.sheets);
        }
        
        // 生成文件ID用于WOPI协议
        const fileId = `${changeId}_${fileType}`;
        
        // 临时解决方案：使用IP地址而不是域名，可能绕过某些主机检查
        const wopiBaseUrl = `http://************:3000/api/wopi/files/file_${fileId}`;
        
        // 支持多种预览方案
        const previewOptions = {
          // 方案1：LibreOffice Online（本地部署开源方案，默认推荐）
          libreoffice_online: process.env.LIBREOFFICE_SERVER ? 
            `${process.env.LIBREOFFICE_SERVER.replace(/\/$/, '')}/browser/e724e42045/cool.html?WOPISrc=${encodeURIComponent(wopiBaseUrl)}&title=${encodeURIComponent(fileName)}` : null,
          
          // 方案2：ONLYOFFICE Document Server（本地部署商业方案）
          onlyoffice: process.env.ONLYOFFICE_SERVER ? 
            `${process.env.ONLYOFFICE_SERVER.replace(/\/$/, '')}/example/?url=${encodeURIComponent(fileDownloadUrl)}&title=${encodeURIComponent(fileName)}` : null,
          
          // 方案3：纯前端预览（无需第三方服务）
          client_side: true, // 标记为可用，前端会处理具体实现
          
          // 方案4：后备方案 - 显示友好提示
          fallback: null
        };
        
        // 根据环境变量选择预览方案，优先使用纯前端预览
        const previewMethod = process.env.OFFICE_PREVIEW_METHOD || 'auto';
        let selectedPreviewUrl = null;
        let selectedMethod = null;
        
        // 添加调试信息
        console.log('预览方案选择调试信息:');
        console.log('文件类型:', fileType);
        console.log('文件扩展名:', fileExtension);
        console.log('预览方法:', previewMethod);
        console.log('client_side可用:', previewOptions.client_side);
        console.log('是否Excel文件:', ['.xlsx', '.xls'].includes(fileExtension));

        // 自动选择可用的预览方案 - 优先使用纯前端预览
        if (previewMethod === 'auto') {
          // 优先使用纯前端预览（无需第三方服务，兼容性好）
          if (previewOptions.client_side && ['.xlsx', '.xls'].includes(fileExtension)) {
            selectedPreviewUrl = null; // 纯前端预览不需要URL
            selectedMethod = 'client_side';
            console.log('选择纯前端预览方案');
          } else if (previewOptions.libreoffice_online) {
            selectedPreviewUrl = previewOptions.libreoffice_online;
            selectedMethod = 'libreoffice_online';
            console.log('选择LibreOffice Online方案');
          } else if (previewOptions.onlyoffice) {
            selectedPreviewUrl = previewOptions.onlyoffice;
            selectedMethod = 'onlyoffice';
            console.log('选择ONLYOFFICE方案');
          } else {
            // 所有预览服务不可用，使用下载方案
            selectedPreviewUrl = null;
            selectedMethod = 'fallback';
            console.log('选择下载方案（fallback）');
          }
        } else {
          // 手动指定预览方案
          if (previewMethod === 'client_side') {
            selectedPreviewUrl = null;
            selectedMethod = 'client_side';
          } else {
            selectedPreviewUrl = previewOptions[previewMethod];
            selectedMethod = previewMethod;
          }
        }
        
        console.log('最终选择的方案:', selectedMethod);
        
        // 生成友好的服务名称
        const serviceNames = {
          libreoffice_online: 'LibreOffice Online',
          onlyoffice: 'ONLYOFFICE Document Server',
          client_side: '纯前端预览'
        };
        
        // 生成更详细的消息
        let message = '';
        if (selectedMethod === 'client_side') {
          message = `正在使用${serviceNames[selectedMethod]}，无需第三方服务支持`;
          if (isMultiSheetExcel) {
            message += `\n该Excel文件包含${excelInfo.count}个工作表：${excelInfo.sheets.join(', ')}，支持工作表切换`;
          }
        } else if (selectedPreviewUrl) {
          message = `正在使用${serviceNames[selectedMethod]}预览Office文档...`;
          if (isMultiSheetExcel) {
            message += `\n该Excel文件包含${excelInfo.count}个工作表：${excelInfo.sheets.join(', ')}`;
          }
        } else {
          if (isMultiSheetExcel) {
            message = `该Excel文件包含${excelInfo.count}个工作表（${excelInfo.sheets.join(', ')}），建议使用纯前端预览或下载后查看`;
          } else {
            message = 'Office文档预览服务不可用，建议使用纯前端预览或下载后查看';
          }
        }
        
        return res.json({
          code: 0,
          msg: {
            fileName: fileName,
            fileType: fileExtension,
            filePath: filePath,
            previewSupported: selectedPreviewUrl !== null,
            previewUrl: selectedPreviewUrl,
            previewType: selectedMethod,
            previewOptions: previewOptions, // 提供所有预览选项供前端选择
            downloadUrl: fileDownloadUrl,
            message: message,
            // 添加Excel特有信息
            excelInfo: excelInfo ? {
              sheetCount: excelInfo.count,
              sheetNames: excelInfo.sheets,
              isMultiSheet: isMultiSheetExcel
            } : null
          }
        });
      default:
        // 其他文件类型不支持预览
        return res.json({
          code: 0,
          msg: {
            fileName: fileName,
            fileType: fileExtension,
            filePath: filePath,
            previewSupported: false,
            previewUrl: null,
            downloadUrl: `/api/download_ops_change_file?changeId=${changeId}&fileType=${fileType}&direct=true`,
            message: '该文件类型不支持在线预览'
          }
        });
    }

    if (isInlinePreview) {
      // 对于支持在线预览的文件类型，直接返回文件流
      console.log('提供文件预览:', fullPath);

      // 设置响应头，内联显示
      res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(fileName)}"`);
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'no-cache');

      // 创建文件读取流并通过管道传输到响应
      const fileStream = fs.createReadStream(fullPath);
      fileStream.pipe(res);

      // 处理错误
      fileStream.on('error', (err) => {
        console.error('文件流错误:', err);
        if (!res.headersSent) {
          res.status(500).json({ code: 1, msg: '文件读取失败' });
        }
      });
    }
  } catch (error) {
    console.error('文件预览失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 删除文件
const removeFile = async (req, res) => {
  try {
    const { changeId, fileType } = req.body;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 查询文件路径
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;

    // 删除文件
    const deleted = await deleteFile(filePath);

    if (!deleted) {
      return res.status(500).json({ code: 1, msg: '文件删除失败' });
    }

    // 更新数据库记录
    let updateQuery;
    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = false, oa_process_file = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = false, signed_archive_file = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    }

    const updateResult = await connPG.query(updateQuery, [req.body.username || 'admin', changeId]);

    if (updateResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    res.json({
      code: 0,
      msg: '文件删除成功'
    });
  } catch (error) {
    console.error('文件删除失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 更新变更管理记录
const updateChangeManagement = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('更新变更管理记录请求体:', req.body);

    const {
      id,
      title,
      system,
      changeLevel,
      change_level, // 支持前端使用下划线命名
      plannedChangeTime,
      planned_change_time, // 支持前端使用下划线命名
      requester,
      implementers,
      username,
      updated_by
    } = req.body;

    // 使用驼峰命名或下划线命名，以支持不同的前端命名风格
    const effectiveChangeLevel = changeLevel || change_level;
    const effectivePlannedChangeTime = plannedChangeTime || planned_change_time;

    // 构建更新语句
    const query = `
      UPDATE ops_change_management
      SET
        title = $1,
        system = $2,
        change_level = $3,
        planned_change_time = $4,
        requester = $5,
        implementers = $6,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = $7
      WHERE id = $8
      RETURNING *
    `;

    // 确定更新者
    const effectiveUpdatedBy = updated_by || username || 'admin';

    const params = [
      title,
      system,
      effectiveChangeLevel, // 使用有效的变更级别
      effectivePlannedChangeTime, // 使用有效的计划变更时间
      requester,
      implementers, // 使用多选的实施人，以逗号分隔的字符串
      effectiveUpdatedBy,
      id
    ];

    const result = await connPG.query(query, params);

    if (result.rowCount === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定ID的变更记录' });
    }

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '更新变更记录',
        change_id: result.rows[0].change_id,
        title: title,
        system: system,
        change_level: effectiveChangeLevel
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/update_ops_change_management',
        JSON.stringify(logBody),
        effectiveUpdatedBy,
        'update'
      ]);

      console.log(`已记录用户操作日志: ${effectiveUpdatedBy} 更新了变更记录 ${result.rows[0].change_id}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('更新变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 删除变更管理记录
const deleteChangeManagement = async (req, res) => {
  try {
    const { id, username } = req.body;

    if (!id) {
      return res.status(400).json({ code: 1, msg: '缺少变更记录ID参数' });
    }

    // 获取当前登录用户的username
    const currentUsername = username || req.headers['x-username'] || req.query.username || 'admin';

    // 先查询要删除的记录信息，用于日志记录
    const selectQuery = `
      SELECT change_id, title, system, change_level
      FROM ops_change_management
      WHERE id = $1 AND del_flag = '0'
    `;
    const selectResult = await connPG.query(selectQuery, [id]);

    if (selectResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录或记录已删除' });
    }

    const changeRecord = selectResult.rows[0];

    // 逻辑删除记录
    const deleteQuery = `
      UPDATE ops_change_management
      SET del_flag = '1', updated_at = CURRENT_TIMESTAMP, updated_by = $1
      WHERE id = $2
      RETURNING *
    `;
    const deleteResult = await connPG.query(deleteQuery, [currentUsername, id]);

    if (deleteResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '删除失败，未找到指定的变更记录' });
    }

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '删除变更记录',
        change_id: changeRecord.change_id,
        title: changeRecord.title,
        system: changeRecord.system,
        change_level: changeRecord.change_level
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/del_ops_change_management',
        JSON.stringify(logBody),
        currentUsername,
        'delete'
      ]);

      console.log(`已记录用户操作日志: ${currentUsername} 删除了变更记录 ${changeRecord.change_id}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: deleteResult.rows[0]
    });
  } catch (error) {
    console.error('删除变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// Office文档转PDF预览
const convertOfficeToPdf = async (req, res) => {
  try {
    const { changeId, fileType } = req.query;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 检查PDF转换功能是否启用
    if (process.env.ENABLE_PDF_CONVERT !== 'true') {
      return res.status(503).json({ 
        code: 1, 
        msg: 'PDF转换功能未启用，请下载文件后查看' 
      });
    }

    // 查询文件路径
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;
    const fullPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, filePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ code: 1, msg: '文件不存在' });
    }

    // 检查文件格式是否支持转换
    const fileExtension = path.extname(filePath).toLowerCase();
    const supportedFormats = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
    if (!supportedFormats.includes(fileExtension)) {
      return res.status(400).json({ 
        code: 1, 
        msg: `不支持转换的文件格式: ${fileExtension}。支持的格式: ${supportedFormats.join(', ')}` 
      });
    }

    // 检查是否已有PDF缓存
    // 获取文件修改时间，确保文件更新后缓存失效
    const fileStats = fs.statSync(fullPath);
    const fileModifiedTime = fileStats.mtime.getTime();
    
    const fileName = path.basename(filePath, path.extname(filePath));
    const pdfCacheDir = path.join(process.env.FILE_UPLOAD_BASE_PATH, 'pdf_cache');
    const pdfPath = path.join(pdfCacheDir, `${fileName}_${changeId}_${fileType}_${fileModifiedTime}.pdf`);

    // 确保缓存目录存在
    if (!fs.existsSync(pdfCacheDir)) {
      fs.mkdirSync(pdfCacheDir, { recursive: true });
    }

    // 清理同一文件的旧缓存（文件名相同但修改时间不同的缓存）
    try {
      const cacheFiles = fs.readdirSync(pdfCacheDir);
      const oldCachePattern = new RegExp(`^${fileName}_${changeId}_${fileType}_\\d+\\.pdf$`);
      
      cacheFiles.forEach(cacheFile => {
        if (oldCachePattern.test(cacheFile) && cacheFile !== path.basename(pdfPath)) {
          const oldCachePath = path.join(pdfCacheDir, cacheFile);
          try {
            fs.unlinkSync(oldCachePath);
            console.log('已清理旧缓存文件:', oldCachePath);
          } catch (unlinkError) {
            console.warn('清理旧缓存文件失败:', unlinkError.message);
          }
        }
      });
    } catch (cleanupError) {
      console.warn('清理缓存目录时出错:', cleanupError.message);
    }

    // 如果PDF缓存不存在，进行转换
    if (!fs.existsSync(pdfPath)) {
      try {
        console.log(`开始转换文档为PDF: ${fullPath} -> ${pdfPath}`);
        await convertToPdf(fullPath, pdfPath);
        console.log(`文档转换成功: ${pdfPath}`);
      } catch (error) {
        console.error('PDF转换失败:', error);
        
        // 根据错误类型返回不同的错误信息
        let errorMessage = '文档转换失败';
        if (error.message.includes('超时')) {
          errorMessage = '文档转换超时，文件可能过大或格式复杂，请下载后查看';
        } else if (error.message.includes('启动LibreOffice失败')) {
          errorMessage = 'PDF转换服务不可用，请确保LibreOffice已正确安装';
        } else if (error.message.includes('PDF文件生成失败')) {
          errorMessage = '文档格式不支持在线预览，请下载后查看';
        } else {
          errorMessage = `文档转换失败: ${error.message}`;
        }
        
        return res.status(500).json({ 
          code: 1, 
          msg: errorMessage
        });
      }
    } else {
      console.log(`使用缓存的PDF文件: ${pdfPath}`);
    }

    // 最终检查PDF文件是否存在
    if (!fs.existsSync(pdfPath)) {
      return res.status(500).json({ 
        code: 1, 
        msg: 'PDF文件生成失败，请下载原文件查看' 
      });
    }

    // 返回PDF文件
    const originalFileName = path.basename(filePath);
    const pdfFileName = originalFileName.replace(/\.(doc|docx|xls|xlsx|ppt|pptx)$/i, '.pdf');

    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(pdfFileName)}"`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // 1小时缓存

    const fileStream = fs.createReadStream(pdfPath);
    fileStream.pipe(res);

    fileStream.on('error', (err) => {
      console.error('PDF文件流错误:', err);
      if (!res.headersSent) {
        res.status(500).json({ code: 1, msg: 'PDF文件读取失败' });
      }
    });

  } catch (error) {
    console.error('Office转PDF失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 使用LibreOffice转换Office文档为PDF
const convertToPdf = async (inputPath, outputPath) => {
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    // 使用LibreOffice命令行工具进行转换
    const outputDir = path.dirname(outputPath);
    const libreOfficeCmd = process.env.LIBREOFFICE_PATH || 'libreoffice';
    
    // Windows环境下的特殊处理
    const isWindows = process.platform === 'win32';
    
    // 检查文件类型，为Excel文件添加特殊的转换参数
    const fileExtension = path.extname(inputPath).toLowerCase();
    const isExcelFile = ['.xls', '.xlsx'].includes(fileExtension);
    
    const args = [
      '--headless',
      '--invisible',
      '--nodefault',
      '--nolockcheck',
      '--nologo',
      '--norestore',
      '--convert-to', 'pdf'
    ];
    
    // 为Excel文件添加特殊的页面布局参数
    if (isExcelFile) {
      // 添加Excel专用的PDF转换参数
      args.push(
        '--infilter=calc_pdf_Export',
        // 设置页面方向为横向（适合更多列）
        '--outfilter=calc_pdf_Export:{"PageRange":{"type":"string","value":""},"Selection":{"type":"any"},"ExportNotes":{"type":"boolean","value":false},"ExportNotesPages":{"type":"boolean","value":false},"ExportOnlyNotesPages":{"type":"boolean","value":false},"ExportFormFields":{"type":"boolean","value":true},"FormsType":{"type":"long","value":0},"AllowDuplicateFieldNames":{"type":"boolean","value":false},"OpenBookmarkLevels":{"type":"long","value":-1},"ExportBookmarks":{"type":"boolean","value":true},"ExportHiddenSlides":{"type":"boolean","value":false},"SinglePageSheets":{"type":"boolean","value":false},"ExportPlaceholders":{"type":"boolean","value":false},"UseTaggedPDF":{"type":"boolean","value":false},"SelectPdfVersion":{"type":"long","value":0},"PDFUACompliance":{"type":"boolean","value":false},"ExportLinksRelativeToFileSystem":{"type":"boolean","value":false},"ConvertOOoTargetToPDFTarget":{"type":"boolean","value":false},"ExportBookmarksToPDFDestination":{"type":"boolean","value":false},"SignPDF":{"type":"boolean","value":false},"RestrictPermissions":{"type":"boolean","value":false},"Printing":{"type":"long","value":2},"Changes":{"type":"long","value":4},"EnableCopyingOfContent":{"type":"boolean","value":true},"EnableTextAccessForAccessibilityTools":{"type":"boolean","value":true}}'
      );
    }
    
    args.push('--outdir', outputDir, inputPath);

    console.log('开始转换Office文档为PDF:', libreOfficeCmd, args.join(' '));
    console.log('输入文件:', inputPath);
    console.log('输出目录:', outputDir);
    console.log('目标文件:', outputPath);

    // Windows环境下使用不同的spawn选项
    const spawnOptions = isWindows ? {
      windowsHide: true,
      stdio: ['pipe', 'pipe', 'pipe']
    } : {};

    const childProcess = spawn(libreOfficeCmd, args, spawnOptions);
    
    let stdout = '';
    let stderr = '';

    childProcess.stdout.on('data', (data) => {
      stdout += data.toString();
      console.log('LibreOffice stdout:', data.toString());
    });

    childProcess.stderr.on('data', (data) => {
      stderr += data.toString();
      console.log('LibreOffice stderr:', data.toString());
    });

    childProcess.on('close', (code) => {
      console.log('LibreOffice进程结束，退出代码:', code);
      console.log('标准输出:', stdout);
      console.log('错误输出:', stderr);
      
      if (code === 0 || code === null) {
        // LibreOffice会生成与输入文件同名但扩展名为.pdf的文件
        const originalName = path.basename(inputPath, path.extname(inputPath));
        const generatedPdfPath = path.join(outputDir, `${originalName}.pdf`);
        
        console.log('查找生成的PDF文件:', generatedPdfPath);
        
        // 检查生成的PDF文件是否存在
        if (fs.existsSync(generatedPdfPath)) {
          // 如果生成的PDF文件名与目标文件名不同，重命名
          if (generatedPdfPath !== outputPath) {
            try {
              fs.renameSync(generatedPdfPath, outputPath);
              console.log('PDF文件重命名成功:', outputPath);
            } catch (error) {
              console.error('重命名PDF文件失败:', error);
              return reject(new Error('PDF文件重命名失败'));
            }
          }
          
          console.log('PDF转换成功:', outputPath);
          resolve(outputPath);
        } else {
          console.error('PDF文件未生成:', generatedPdfPath);
          reject(new Error('PDF文件生成失败，可能是文档格式不支持或LibreOffice配置问题'));
        }
      } else {
        console.error('LibreOffice转换失败，退出代码:', code);
        reject(new Error(`LibreOffice转换失败 (退出代码: ${code}): ${stderr || '未知错误'}`));
      }
    });

    childProcess.on('error', (error) => {
      console.error('启动LibreOffice进程失败:', error);
      reject(new Error(`启动LibreOffice失败: ${error.message}. 请确保LibreOffice已正确安装并配置路径。`));
    });

    // 增加超时时间到60秒，复杂文档可能需要更长时间
    setTimeout(() => {
      console.log('LibreOffice转换超时，终止进程');
      childProcess.kill('SIGTERM');
      reject(new Error('文档转换超时，可能文档过大或格式复杂'));
    }, 60000); // 60秒超时
  });
};

// 清理PDF缓存
const clearPdfCache = async (req, res) => {
  try {
    const { changeId, fileType, clearAll } = req.body;
    
    const pdfCacheDir = path.join(process.env.FILE_UPLOAD_BASE_PATH, 'pdf_cache');
    
    if (!fs.existsSync(pdfCacheDir)) {
      return res.json({ code: 0, msg: 'PDF缓存目录不存在，无需清理' });
    }

    let deletedCount = 0;
    
    if (clearAll) {
      // 清理整个PDF缓存目录
      const cacheFiles = fs.readdirSync(pdfCacheDir);
      
      cacheFiles.forEach(cacheFile => {
        if (cacheFile.endsWith('.pdf')) {
          const cachePath = path.join(pdfCacheDir, cacheFile);
          try {
            fs.unlinkSync(cachePath);
            deletedCount++;
            console.log('已清理PDF缓存文件:', cachePath);
          } catch (error) {
            console.warn('清理PDF缓存文件失败:', error.message);
          }
        }
      });
      
      res.json({ 
        code: 0, 
        msg: `已清理所有PDF缓存，共 ${deletedCount} 个文件` 
      });
    } else if (changeId && fileType) {
      // 清理特定文件的PDF缓存
      const cacheFiles = fs.readdirSync(pdfCacheDir);
      const cachePattern = new RegExp(`.*_${changeId}_${fileType}_\\d+\\.pdf$`);
      
      cacheFiles.forEach(cacheFile => {
        if (cachePattern.test(cacheFile)) {
          const cachePath = path.join(pdfCacheDir, cacheFile);
          try {
            fs.unlinkSync(cachePath);
            deletedCount++;
            console.log('已清理PDF缓存文件:', cachePath);
          } catch (error) {
            console.warn('清理PDF缓存文件失败:', error.message);
          }
        }
      });
      
      res.json({ 
        code: 0, 
        msg: `已清理 ${deletedCount} 个PDF缓存文件` 
      });
    } else {
      // 清理所有PDF缓存（兼容旧版本调用）
      const cacheFiles = fs.readdirSync(pdfCacheDir);
      
      cacheFiles.forEach(cacheFile => {
        if (cacheFile.endsWith('.pdf')) {
          const cachePath = path.join(pdfCacheDir, cacheFile);
          try {
            fs.unlinkSync(cachePath);
            deletedCount++;
          } catch (error) {
            console.warn('清理PDF缓存文件失败:', error.message);
          }
        }
      });
      
      res.json({ 
        code: 0, 
        msg: `已清理所有PDF缓存，共 ${deletedCount} 个文件` 
      });
    }
  } catch (error) {
    console.error('清理PDF缓存失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

module.exports = {
  getChangeManagementList,
  getSystemList,
  getUserList,
  addChangeManagement,
  updateChangeManagement,
  deleteChangeManagement,
  uploadFile,
  uploadFileSimple,
  downloadFile,
  previewFile,
  removeFile,
  generateChangeId,
  convertOfficeToPdf,
  clearPdfCache
};
