#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const srcDir = path.join(__dirname, '..', 'src');
const originalApp = path.join(srcDir, 'App.tsx');
const backupApp = path.join(srcDir, 'App-Original.tsx');

console.log('🔄 恢复原始 CMDB Dashboard UI...\n');

try {
  // 检查备份文件是否存在
  if (!fs.existsSync(backupApp)) {
    console.error('❌ 错误: App-Original.tsx 备份文件不存在');
    console.log('ℹ️  可能原始文件从未被替换过');
    process.exit(1);
  }

  // 恢复原始文件
  fs.copyFileSync(backupApp, originalApp);
  console.log('✅ 已恢复原始界面');

  console.log('\n📋 原始界面特性：');
  console.log('   • 经典卡片布局');
  console.log('   • 基础动画效果');
  console.log('   • 轮播组件');
  console.log('   • 告警统计');

  console.log('\n🎨 如需重新启用增强版界面，请运行: npm run enable-enhanced-ui');

} catch (error) {
  console.error('❌ 恢复失败:', error.message);
  process.exit(1);
}