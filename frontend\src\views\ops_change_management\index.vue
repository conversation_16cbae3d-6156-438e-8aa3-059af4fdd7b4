<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" ref="searchFormRef" label-width="100px" label-position="right">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="关键字">
              <el-input v-model="searchForm.keyword" placeholder="变更编号/变更名称" clearable @keyup.enter="handleSearch"
                class="form-control" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="变更级别">
              <el-select v-model="searchForm.changeLevel" placeholder="请选择" clearable filterable class="form-control">
                <el-option v-for="item in changeLevelOptions" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_code" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="变更系统">
              <el-select v-model="searchForm.system" placeholder="请选择" clearable filterable class="form-control">
                <el-option v-for="item in systemOptions" :key="item.system_abbreviation"
                  :label="item.system_abbreviation" :value="item.system_abbreviation" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="变更负责人">
              <el-select v-model="searchForm.requester" placeholder="请选择" clearable filterable class="form-control">
                <el-option v-for="item in userOptions" :key="item.username" :label="item.real_name"
                  :value="item.username" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="变更实施人">
              <el-select v-model="searchForm.implementer" placeholder="请选择" clearable filterable class="form-control">
                <el-option v-for="item in userOptions" :key="item.username" :label="item.real_name"
                  :value="item.username" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="未规范">
              <el-select v-model="searchForm.nonCompliant" placeholder="请选择" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="OA流程">
              <el-select v-model="searchForm.oaProcess" placeholder="请选择" clearable filterable class="form-control">
                <el-option label="已上传" value="已上传" />
                <el-option label="未上传" value="未上传" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="签字存档">
              <el-select v-model="searchForm.signedArchive" placeholder="请选择" clearable filterable class="form-control">
                <el-option label="已上传" value="已上传" />
                <el-option label="未上传" value="未上传" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="是否过期">
              <el-select v-model="searchForm.isOverdue" placeholder="请选择" clearable filterable class="form-control">
                <el-option label="已过期" value="已过期" />
                <el-option label="未过期" value="未过期" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="变更时间">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="handleDateRangeChange" class="form-control" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="24" class="search-buttons-col">
            <el-form-item>
              <div class="button-container">
                <el-button type="primary" @click="handleSearch">
                  <el-icon>
                    <Search />
                  </el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>新增变更
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData">
          <el-icon>
            <Download />
          </el-icon> 导出数据
        </el-button>
        <!-- 状态图例 -->
        <div class="status-legend">
          <span class="legend-title">状态图例：</span>
          <el-tag type="danger" size="small">已过期</el-tag>
          <el-tag type="warning" size="small">今天/明天</el-tag>
          <el-tag type="primary" size="small">本周</el-tag>
          <el-tag type="success" size="small">未来</el-tag>
          <!-- <el-tag type="info" size="small">未设定</el-tag> -->
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%" table-layout="fixed"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }" @sort-change="handleSortChange">
        <el-table-column prop="change_id" label="变更编号" min-width="150" sortable />
        <el-table-column prop="title" label="变更名称" min-width="250" show-overflow-tooltip sortable />
        <el-table-column prop="system" label="变更系统" min-width="150" max-width="200" show-overflow-tooltip sortable />
        <el-table-column prop="change_level_name_display" label="变更级别" min-width="100" sortable />
        <el-table-column label="计划变更时间" min-width="150" sortable="custom" prop="planned_change_time">  
          <template #default="scope">
            <div class="change-time-cell">
              <div class="time-text">{{ scope.row.formatted_change_time }}</div>
              <el-tag :type="getChangeTimeStatus(scope.row.planned_change_time).type" size="small"
                class="time-status-tag">
                {{ getChangeTimeStatus(scope.row.planned_change_time).text }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="requester_name" label="变更负责人" min-width="120" sortable />
        <el-table-column prop="implementers_name" label="变更实施人" min-width="120" sortable show-overflow-tooltip />

        <el-table-column label="OA流程" min-width="100" align="center" sortable>
          <template #default="scope">
            <el-tag v-if="scope.row.oa_process" type="success">已上传</el-tag>
            <el-tag v-else type="info">未上传</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="签字存档" min-width="100" align="center" sortable>
          <template #default="scope">
            <el-tag v-if="scope.row.signed_archive" type="success">已上传</el-tag>
            <el-tag v-else type="info">未上传</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="变更操作表" min-width="100" align="center" sortable>
          <template #default="scope">
            <el-tag v-if="scope.row.operation_sheet" type="success">已上传</el-tag>
            <el-tag v-else type="info">未上传</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" min-width="150" sortable="created_at">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="创建人" min-width="100" sortable="created_by">
          <template #default="scope">
            {{ scope.row.created_by }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" min-width="150" sortable="updated_at">
          <template #default="scope">
            {{ formatDateTime(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="更新人" min-width="100" sortable="updated_by">
          <template #default="scope">
            {{ scope.row.updated_by }}
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="190">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap; gap: 8px;">
              <el-button type="primary" size="small" @click="handleView(scope.row)">
                详情
              </el-button>

              <el-button type="success" size="small" @click="handleDownloadOperationSheet(scope.row)"
                :loading="downloadLoading[scope.row.id]" :disabled="!scope.row.operation_sheet">
                <el-icon>
                  <Download />
                </el-icon>
                下载操作表
              </el-button>

              <!-- <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button> -->
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination background :current-page="pagination.currentPage" :page-size="pagination.pageSize"
          :total="pagination.total" :page-sizes="[10, 20, 50, 100, 1000]" :pager-count="5"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Download } from '@element-plus/icons-vue'
import request from '@/utils/request'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

export default {
  name: 'OpsChangeManagement',
  components: {
    Plus,
    Search,
    Download
  },
  setup() {
    const router = useRouter()
    const searchFormRef = ref(null)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      changeLevel: '',
      system: '',
      requester: '',
      implementer: '',
      startDate: '',
      endDate: '',
      nonCompliant: '',           // 未规范筛选条件
      oaProcess: '',              // 取消OA流程的默认筛选条件
      signedArchive: '',          // 取消签字存档的默认筛选条件
      isOverdue: '',              // 是否过期筛选
      sortProp: 'planned_change_time',  // 默认按计划变更时间排序
      sortOrder: 'desc'           // 从大到小排序（降序）
    })

    // 日期范围
    const dateRange = ref([])

    // 处理日期范围变化
    const handleDateRangeChange = (val) => {
      if (val) {
        searchForm.startDate = val[0]
        searchForm.endDate = val[1]
      } else {
        searchForm.startDate = ''
        searchForm.endDate = ''
      }
    }

    // 表格数据
    const tableData = ref([])
    const tableLoading = ref(false)

    // 下载加载状态
    const downloadLoading = ref({})

    // 分页
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 选项数据
    const changeLevelOptions = ref([])
    const systemOptions = ref([])
    const userOptions = ref([])

    // 获取变更级别列表
    const getChangeLevelList = async () => {
      try {
        const response = await request({
          url: '/api/get_cmdb_data_dictionary',
          method: 'post',
          data: {
            dict_type: 'P'  // 使用P类型数据字典作为变更级别
          }
        })

        if (response.code === 0) {
          // 过滤掉包含null值的选项
          changeLevelOptions.value = response.msg.filter(item =>
            item && item.dict_code !== null && item.dict_name !== null
          )
        }
      } catch (error) {
        console.error('获取变更级别列表失败:', error)
        ElMessage.error('获取变更级别列表失败')
      }
    }

    // 获取用户列表
    const getUserList = async () => {
      try {
        const response = await request({
          url: '/api/get_user_list',
          method: 'post'
        })

        if (response.code === 0) {
          // 过滤掉包含null值的选项
          userOptions.value = response.msg.filter(item =>
            item && item.username !== null && item.username !== undefined &&
            item.real_name !== null && item.real_name !== undefined
          )
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
      }
    }

    // 获取系统列表
    const getSystemList = async () => {
      try {
        const response = await request({
          url: '/api/get_system_list',
          method: 'post'
        })

        if (response.code === 0) {
          // 过滤掉包含null值的选项
          systemOptions.value = response.msg.filter(item =>
            item && item.system_abbreviation !== null && item.system_abbreviation !== undefined
          )
        }
      } catch (error) {
        console.error('获取系统列表失败:', error)
        ElMessage.error('获取系统列表失败')
      }
    }

    // 获取变更管理列表
    const getChangeList = async () => {
      tableLoading.value = true

      // 打印搜索表单数据，用于调试
      console.log('搜索表单数据:', searchForm)

      try {
        // 构建请求数据
        const requestData = {
          ...searchForm,
          currentPage: pagination.currentPage,
          pageSize: pagination.pageSize,
          // 确保变更级别参数名称正确
          change_level: searchForm.changeLevel
        }

        console.log('发送请求数据:', requestData)

        const response = await request({
          url: '/api/get_ops_change_management',
          method: 'post',
          data: requestData
        })

        if (response.code === 0) {
          tableData.value = response.msg
          pagination.total = response.total
        } else {
          ElMessage.error(`获取列表失败: ${response.msg}`)
        }
      } catch (error) {
        console.error('获取变更管理列表失败:', error)
        ElMessage.error('获取变更管理列表失败')
      } finally {
        tableLoading.value = false
      }
    }

    // 处理表格排序
    const handleSortChange = ({ prop, order }) => {
      searchForm.sortProp = prop
      searchForm.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      getChangeList()
    }

    // 搜索
    const handleSearch = () => {
      pagination.currentPage = 1
      getChangeList()
    }

    // 重置搜索
    const resetSearch = () => {
      // 手动重置所有字段到默认值
      searchForm.keyword = ''
      searchForm.changeLevel = ''
      searchForm.system = ''
      searchForm.requester = ''
      searchForm.implementer = ''
      searchForm.nonCompliant = ''           // 重置未规范筛选条件
      searchForm.oaProcess = ''              // 重置为空值，取消默认筛选
      searchForm.signedArchive = ''          // 重置为空值，取消默认筛选
      searchForm.isOverdue = ''              // 重置是否过期筛选
      searchForm.startDate = ''
      searchForm.endDate = ''
      searchForm.sortProp = 'planned_change_time'  // 重置为默认排序（按计划变更时间）
      searchForm.sortOrder = 'desc'          // 重置为默认排序（降序）

      // 重置日期范围
      dateRange.value = []

      // 重置分页
      pagination.currentPage = 1

      // 如果表单引用存在，也调用resetFields方法
      if (searchFormRef.value) {
        searchFormRef.value.resetFields()
      }

      // 重新加载数据
      getChangeList()

      // 提示用户
      ElMessage.success('搜索条件已重置')
    }

    // 处理分页大小变化
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      getChangeList()
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      getChangeList()
    }

    // 新增变更
    const handleAdd = () => {
      router.push('/ops_change_management/detail/new')
    }

    // 查看变更
    const handleView = (row) => {
      router.push(`/ops_change_management/detail/${row.id}`)
    }

    // 上传附件
    const handleUpload = (row) => {
      router.push(`/ops_change_management/detail/${row.id}?tab=attachments`)
    }

    // 删除变更
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除变更 "${row.change_id}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await request({
            url: '/api/del_ops_change_management',
            method: 'post',
            data: {
              id: row.id,
              username: localStorage.getItem('loginUsername') || 'admin'
            }
          })

          if (response.code === 0) {
            ElMessage.success('删除成功')
            getChangeList()
          } else {
            ElMessage.error(`删除失败: ${response.msg}`)
          }
        } catch (error) {
          console.error('删除变更失败:', error)
          ElMessage.error('删除变更失败')
        }
      }).catch(() => {
        // 用户取消删除操作
      })
    }

    // 下载变更操作表
    const handleDownloadOperationSheet = async (row) => {
      if (!row.operation_sheet) {
        ElMessage.warning('该变更没有上传变更操作表')
        return
      }

      try {
        // 设置下载加载状态
        downloadLoading.value[row.id] = true

        // 使用直接下载方式，与变更详情页面保持一致
        const downloadUrl = `/api/download_ops_change_file?changeId=${row.change_id}&fileType=operation_sheet&direct=true`

        // 创建一个隐藏的a标签并模拟点击下载
        const link = document.createElement('a')
        link.href = downloadUrl
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('变更操作表下载成功')
      } catch (error) {
        console.error('下载变更操作表失败:', error)
        ElMessage.error('下载变更操作表失败')
      } finally {
        // 清除下载加载状态
        downloadLoading.value[row.id] = false
      }
    }



    // 获取变更时间状态
    const getChangeTimeStatus = (plannedTime) => {
      if (!plannedTime) {
        return { type: 'info', text: '未设定', class: 'unset' }
      }

      const now = new Date()
      const changeDate = new Date(plannedTime)
      const diffDays = Math.ceil((changeDate - now) / (1000 * 60 * 60 * 24))

      if (diffDays < 0) {
        // 已过期
        return { type: 'danger', text: '已过期', class: 'overdue' }
      } else if (diffDays === 0) {
        // 今天
        return { type: 'warning', text: '今天', class: 'today' }
      } else if (diffDays === 1) {
        // 明天
        return { type: 'warning', text: '明天', class: 'tomorrow' }
      } else if (diffDays <= 7) {
        // 本周内
        return { type: 'primary', text: '本周', class: 'this-week' }
      } else {
        // 未来
        return { type: 'success', text: '未来', class: 'future' }
      }
    }

    // 格式化日期时间
    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return '';

      try {
        const date = new Date(dateTimeStr);

        // 检查是否为有效日期
        if (isNaN(date.getTime())) {
          return dateTimeStr;
        }

        // 格式化函数
        const padZero = (num) => String(num).padStart(2, '0');

        const year = date.getFullYear();
        const month = padZero(date.getMonth() + 1);
        const day = padZero(date.getDate());
        const hours = padZero(date.getHours());
        const minutes = padZero(date.getMinutes());
        const seconds = padZero(date.getSeconds());

        // 如果时间部分都是0，则只显示日期部分
        if (date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0) {
          return `${year}-${month}-${day}`;
        } else {
          // 否则显示完整的日期时间
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateTimeStr;
      }
    };

    // 导出数据
    const exportData = () => {
      if (!tableData.value || tableData.value.length === 0) {
        ElMessage.warning('没有数据可以导出')
        return
      }

      try {
        // 定义表头映射
        const headers = [
          '变更编号',
          '变更名称',
          '变更系统',
          '变更级别',
          '计划变更时间',
          'OA流程',
          '签字存档',
          '变更操作表',
          '变更负责人',
          '变更实施人',
          '创建时间',
          '创建人',
          '更新时间',
          '更新人'
        ]

        // 处理数据，将复杂字段转换为简单文本
        const data = tableData.value.map(row => [
          row.change_id || '',
          row.title || '',
          row.system || '',
          row.change_level_name_display || '',
          row.formatted_change_time || '',
          row.oa_process ? '已上传' : '未上传',
          row.signed_archive ? '已上传' : '未上传',
          row.operation_sheet ? '已上传' : '未上传',
          row.requester_name || '',
          row.implementers_name || '',
          formatDateTime(row.created_at),
          row.created_by || '',
          formatDateTime(row.updated_at),
          row.updated_by || ''
        ])

        // 合并表头和数据
        const wsData = [headers, ...data]
        const ws = XLSX.utils.aoa_to_sheet(wsData)

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
        const blob = new Blob([wbout], { type: 'application/octet-stream' })
        saveAs(blob, '变更管理数据.xlsx')

        ElMessage.success('数据导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        ElMessage.error('导出数据失败')
      }
    }

    // 页面加载时执行
    onMounted(async () => {
      // 检查URL参数中是否有search_ip
      if (router.currentRoute.value.query.search_ip) {
        // 将search_ip参数值设置为关键词搜索条件
        searchForm.keyword = router.currentRoute.value.query.search_ip
        console.log('从全局搜索跳转，设置关键词:', searchForm.keyword)
      }

      // 获取选项数据
      await Promise.all([
        getChangeLevelList(),
        getUserList(),
        getSystemList()
      ])

      // 获取列表数据
      getChangeList()
    })

    return {
      searchFormRef,
      searchForm,
      dateRange,
      tableData,
      tableLoading,
      downloadLoading,
      pagination,
      changeLevelOptions,
      systemOptions,
      userOptions,
      formatDateTime,
      getChangeTimeStatus,
      handleDateRangeChange,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleView,
      handleUpload,
      handleDelete,
      handleDownloadOperationSheet,
      exportData
    }
  }
}
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 10px; //  为搜索卡片添加底部间距
  height: auto; //  自适应高度
}

.table-card {
  margin-bottom: 20px; // 为表格添加底部间距
}

.form-control {
  width: 100%;
  min-width: 190px;
}

.search-buttons-col {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
}

.button-container {
  display: flex;
  gap: 10px;
}

.unified-action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .action-bar-left {
    display: flex;
    gap: 10px;
  }

  .action-bar-right {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

// 状态图例样式
.status-legend {
  display: flex;
  align-items: center;
  gap: 8px;

  .legend-title {
    font-size: 14px;
    color: #606266;
    margin-right: 4px;
  }
}

:deep(.el-table) {

  // 滚动条加粗
  .el-scrollbar__bar.is-horizontal {
    height: 10px;
    left: 2px;
  }

  .el-scrollbar__bar.is-vertical {
    top: 2px;
    width: 10px;
  }

  .cell {
    display: inline-block; // 确保 max-width 生效
    white-space: nowrap;
    max-width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cell:hover {
    white-space: pre-wrap;
    /* 使用pre-wrap来允许换行但保留空白字符 */
    overflow: visible;
    text-overflow: clip;
    word-break: keep-all;
    /* 尽量保持单词完整，不强制断开 */
    max-width: 400px;
    /* 保持最大宽度不变 */
    width: 100%;
    /* 确保宽度一致 */
    word-wrap: break-word;
    /* 当单词超过容器宽度时允许换行 */
    display: inline-block;
    /* 确保元素可以正确处理宽度 */
  }

  // 表头样式
  th .cell {
    white-space: nowrap !important; // 强制表头内容不换行
    display: flex;
    align-items: center; // 垂直居中对齐
  }
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 变更时间状态样式
.change-time-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .time-text {
    font-weight: 500;
  }

  .time-status-tag {
    align-self: flex-start;
  }
}



@media (max-width: 768px) {
  .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .pagination {
    justify-content: center;
  }

  .unified-action-bar {
    flex-direction: column;
    gap: 10px;

    .action-bar-left,
    .action-bar-right {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
