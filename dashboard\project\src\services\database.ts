// 数据库连接和查询服务
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

interface QueryResult<T = any> {
  success: boolean;
  data: T[];
  message?: string;
  total?: number;
}

interface DeviceLocationData {
  location: string;
  device_type: string;
  count: number;
  lifecycle_status: string;
}

interface LocationSummary {
  location: string;
  network_devices: number;
  physical_servers: number;
  virtual_servers: number;
  total_devices: number;
  coordinates?: {
    x: number;
    y: number;
  };
}

class DatabaseService {
  private baseUrl: string;

  constructor() {

    // API基础地址
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  }

  /**
   * 获取网络设备机房分布数据
   * 查询cmdb_networks表中生命周期正常的设备
   */
  async getNetworkDeviceDistribution(): Promise<QueryResult<DeviceLocationData>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/dashboard/network-devices-distribution`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            SELECT 
              COALESCE(location, '未知机房') as location,
              'network' as device_type,
              COUNT(*) as count,
              lifecycle_status
            FROM cmdb_networks 
            WHERE lifecycle_status = 'normal' 
              AND is_deleted = false
            GROUP BY location, lifecycle_status
            ORDER BY count DESC
          `
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('获取网络设备分布失败:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : '查询失败'
      };
    }
  }

  /**
   * 获取实体服务器机房分布数据
   * 查询cmdb_servers表中生命周期正常的实体服务器
   */
  async getPhysicalServerDistribution(): Promise<QueryResult<DeviceLocationData>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/dashboard/physical-servers-distribution`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            SELECT 
              COALESCE(location, '未知机房') as location,
              'physical' as device_type,
              COUNT(*) as count,
              lifecycle_status
            FROM cmdb_servers 
            WHERE lifecycle_status = 'normal' 
              AND server_type = 'physical'
              AND is_deleted = false
            GROUP BY location, lifecycle_status
            ORDER BY count DESC
          `
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('获取实体服务器分布失败:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : '查询失败'
      };
    }
  }

  /**
   * 获取虚拟化设备机房分布数据
   * 查询cmdb_virtual_machines表中生命周期正常的虚拟机
   */
  async getVirtualServerDistribution(): Promise<QueryResult<DeviceLocationData>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/dashboard/virtual-servers-distribution`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            SELECT 
              COALESCE(location, '未知机房') as location,
              'virtual' as device_type,
              COUNT(*) as count,
              lifecycle_status
            FROM cmdb_virtual_machines 
            WHERE lifecycle_status = 'normal' 
              AND is_deleted = false
            GROUP BY location, lifecycle_status
            ORDER BY count DESC
          `
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('获取虚拟化设备分布失败:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : '查询失败'
      };
    }
  }

  /**
   * 获取所有设备的机房分布汇总数据
   */
  async getAllDeviceDistribution(): Promise<QueryResult<LocationSummary>> {
    try {
      // 并行查询三种设备类型
      const [networkResult, physicalResult, virtualResult] = await Promise.all([
        this.getNetworkDeviceDistribution(),
        this.getPhysicalServerDistribution(),
        this.getVirtualServerDistribution()
      ]);

      // 合并数据
      const locationMap = new Map<string, LocationSummary>();

      // 处理网络设备数据
      if (networkResult.success) {
        networkResult.data.forEach(item => {
          const location = item.location;
          if (!locationMap.has(location)) {
            locationMap.set(location, {
              location,
              network_devices: 0,
              physical_servers: 0,
              virtual_servers: 0,
              total_devices: 0,
              coordinates: this.getLocationCoordinates(location)
            });
          }
          const summary = locationMap.get(location)!;
          summary.network_devices += item.count;
          summary.total_devices += item.count;
        });
      }

      // 处理实体服务器数据
      if (physicalResult.success) {
        physicalResult.data.forEach(item => {
          const location = item.location;
          if (!locationMap.has(location)) {
            locationMap.set(location, {
              location,
              network_devices: 0,
              physical_servers: 0,
              virtual_servers: 0,
              total_devices: 0,
              coordinates: this.getLocationCoordinates(location)
            });
          }
          const summary = locationMap.get(location)!;
          summary.physical_servers += item.count;
          summary.total_devices += item.count;
        });
      }

      // 处理虚拟化设备数据
      if (virtualResult.success) {
        virtualResult.data.forEach(item => {
          const location = item.location;
          if (!locationMap.has(location)) {
            locationMap.set(location, {
              location,
              network_devices: 0,
              physical_servers: 0,
              virtual_servers: 0,
              total_devices: 0,
              coordinates: this.getLocationCoordinates(location)
            });
          }
          const summary = locationMap.get(location)!;
          summary.virtual_servers += item.count;
          summary.total_devices += item.count;
        });
      }

      const data = Array.from(locationMap.values());

      return {
        success: true,
        data,
        total: data.length
      };

    } catch (error) {
      console.error('获取设备分布汇总失败:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : '查询失败'
      };
    }
  }

  /**
   * 获取机房坐标位置（用于地图显示）
   */
  private getLocationCoordinates(location: string): { x: number, y: number } {
    // 根据机房名称映射到地图坐标
    const locationCoords: Record<string, { x: number, y: number }> = {
      '上海机房': { x: 75, y: 35 },
      '上海数据中心': { x: 75, y: 35 },
      '上海IDC': { x: 75, y: 35 },
      '北京机房': { x: 65, y: 20 },
      '北京数据中心': { x: 65, y: 20 },
      '北京IDC': { x: 65, y: 20 },
      '深圳机房': { x: 70, y: 60 },
      '深圳数据中心': { x: 70, y: 60 },
      '深圳IDC': { x: 70, y: 60 },
      '广州机房': { x: 68, y: 65 },
      '广州数据中心': { x: 68, y: 65 },
      '广州IDC': { x: 68, y: 65 },
      '杭州机房': { x: 72, y: 40 },
      '杭州数据中心': { x: 72, y: 40 },
      '杭州IDC': { x: 72, y: 40 },
      '南京机房': { x: 73, y: 32 },
      '南京数据中心': { x: 73, y: 32 },
      '南京IDC': { x: 73, y: 32 },
      '成都机房': { x: 58, y: 45 },
      '成都数据中心': { x: 58, y: 45 },
      '成都IDC': { x: 58, y: 45 },
      '西安机房': { x: 60, y: 35 },
      '西安数据中心': { x: 60, y: 35 },
      '西安IDC': { x: 60, y: 35 },
      '武汉机房': { x: 67, y: 42 },
      '武汉数据中心': { x: 67, y: 42 },
      '武汉IDC': { x: 67, y: 42 },
      '重庆机房': { x: 56, y: 48 },
      '重庆数据中心': { x: 56, y: 48 },
      '重庆IDC': { x: 56, y: 48 },
      '天津机房': { x: 66, y: 22 },
      '天津数据中心': { x: 66, y: 22 },
      '天津IDC': { x: 66, y: 22 },
      '青岛机房': { x: 69, y: 28 },
      '青岛数据中心': { x: 69, y: 28 },
      '青岛IDC': { x: 69, y: 28 },
      '大连机房': { x: 71, y: 18 },
      '大连数据中心': { x: 71, y: 18 },
      '大连IDC': { x: 71, y: 18 },
      '厦门机房': { x: 74, y: 55 },
      '厦门数据中心': { x: 74, y: 55 },
      '厦门IDC': { x: 74, y: 55 },
      '长沙机房': { x: 65, y: 50 },
      '长沙数据中心': { x: 65, y: 50 },
      '长沙IDC': { x: 65, y: 50 },
      '郑州机房': { x: 64, y: 38 },
      '郑州数据中心': { x: 64, y: 38 },
      '郑州IDC': { x: 64, y: 38 },
      '济南机房': { x: 68, y: 30 },
      '济南数据中心': { x: 68, y: 30 },
      '济南IDC': { x: 68, y: 30 },
      '合肥机房': { x: 71, y: 38 },
      '合肥数据中心': { x: 71, y: 38 },
      '合肥IDC': { x: 71, y: 38 },
      '南昌机房': { x: 72, y: 48 },
      '南昌数据中心': { x: 72, y: 48 },
      '南昌IDC': { x: 72, y: 48 },
      '福州机房': { x: 75, y: 52 },
      '福州数据中心': { x: 75, y: 52 },
      '福州IDC': { x: 75, y: 52 },
      '未知机房': { x: 50, y: 50 },
      '': { x: 50, y: 50 }
    };

    return locationCoords[location] || {
      x: 50 + Math.random() * 40,
      y: 30 + Math.random() * 40
    };
  }

  /**
   * 测试数据库连接
   */
  async testConnection(): Promise<QueryResult<any>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/dashboard/test-connection`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('数据库连接测试失败:', error);
      return {
        success: false,
        data: [],
        message: error instanceof Error ? error.message : '连接失败'
      };
    }
  }
}

export const databaseService = new DatabaseService();
export type { DatabaseConfig, QueryResult, DeviceLocationData, LocationSummary };