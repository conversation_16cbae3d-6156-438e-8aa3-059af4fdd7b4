-- =====================================================
-- 版本: *******
-- 功能: 插入机房互联网线路管理相关数据字典项
-- 日期: 2025-01-28
-- 描述: 单独的数据字典插入脚本
-- =====================================================

-- 运营商字典
INSERT INTO public.cmdb_data_dictionary (dict_type, dict_type_name, dict_code, dict_name, created_by, updated_by) VALUES
('internet_provider', '互联网运营商', 'inet_telecom', '中国电信', 'admin', 'admin'),
('internet_provider', '互联网运营商', 'inet_unicom', '中国联通', 'admin', 'admin'),
('internet_provider', '互联网运营商', 'inet_mobile', '中国移动', 'admin', 'admin'),
('internet_provider', '互联网运营商', 'inet_other', '其他运营商', 'admin', 'admin')
ON CONFLICT (dict_code, del_flag) DO NOTHING;

-- 线路类型字典
INSERT INTO public.cmdb_data_dictionary (dict_type, dict_type_name, dict_code, dict_name, created_by, updated_by) VALUES
('internet_line_type', '互联网线路类型', 'line_dedicated', '专线', 'admin', 'admin'),
('internet_line_type', '互联网线路类型', 'line_broadband', '宽带', 'admin', 'admin'),
('internet_line_type', '互联网线路类型', 'line_fiber', '光纤', 'admin', 'admin'),
('internet_line_type', '互联网线路类型', 'line_vpn', 'VPN', 'admin', 'admin'),
('internet_line_type', '互联网线路类型', 'line_leased', '租用线路', 'admin', 'admin')
ON CONFLICT (dict_code, del_flag) DO NOTHING;

-- 协议类型字典
INSERT INTO public.cmdb_data_dictionary (dict_type, dict_type_name, dict_code, dict_name, created_by, updated_by) VALUES
('protocol_type', '网络协议类型', 'proto_TCP', 'TCP', 'admin', 'admin'),
('protocol_type', '网络协议类型', 'proto_UDP', 'UDP', 'admin', 'admin'),
('protocol_type', '网络协议类型', 'proto_ICMP', 'ICMP', 'admin', 'admin'),
('protocol_type', '网络协议类型', 'proto_HTTP', 'HTTP', 'admin', 'admin'),
('protocol_type', '网络协议类型', 'proto_HTTPS', 'HTTPS', 'admin', 'admin')
ON CONFLICT (dict_code, del_flag) DO NOTHING;

-- IP映射状态字典
INSERT INTO public.cmdb_data_dictionary (dict_type, dict_type_name, dict_code, dict_name, created_by, updated_by) VALUES
('ip_mapping_status', 'IP映射状态', 'mapping_active', '活跃', 'admin', 'admin'),
('ip_mapping_status', 'IP映射状态', 'mapping_inactive', '非活跃', 'admin', 'admin'),
('ip_mapping_status', 'IP映射状态', 'mapping_maintenance', '维护中', 'admin', 'admin'),
('ip_mapping_status', 'IP映射状态', 'mapping_reserved', '预留', 'admin', 'admin')
ON CONFLICT (dict_code, del_flag) DO NOTHING;

-- 输出成功信息
DO $$
BEGIN
    RAISE NOTICE '数据字典项插入完成！';
    RAISE NOTICE '版本: *******';
    RAISE NOTICE '添加数据字典项: 运营商、线路类型、协议类型、接入方式、IP映射状态';
END $$;