/**
 * 版本更新脚本
 * 用于同步更新所有相关文件中的版本号
 *
 * 使用方法：
 * node scripts/update-version.js <新版本号>
 * 例如：node scripts/update-version.js *******
 */

const fs = require('fs');
const path = require('path');

// 获取命令行参数中的新版本号
const newVersion = process.argv[2];

if (!newVersion) {
  process.stderr.write('错误: 请提供新的版本号\n');
  process.stderr.write('用法: node scripts/update-version.js <新版本号>\n');
  process.exit(1);
}

// 验证版本号格式
if (!/^\d+\.\d+\.\d+(\.\d+)?$/.test(newVersion)) {
  process.stderr.write('错误: 版本号格式不正确，应为 x.y.z 或 x.y.z.w\n');
  process.exit(1);
}

// 更新 package.json
const packageJsonPath = path.resolve(__dirname, '../package.json');
const packageJson = require(packageJsonPath);
const oldVersion = packageJson.version;
packageJson.version = newVersion;

fs.writeFileSync(
  packageJsonPath,
  JSON.stringify(packageJson, null, 2) + '\n',
  'utf8'
);


// 更新 version.js
const versionJsPath = path.resolve(__dirname, '../src/config/version.js');
if (fs.existsSync(versionJsPath)) {
  let versionJsContent = fs.readFileSync(versionJsPath, 'utf8');

  // 替换版本号
  const versionRegex = /(export const version = ')([^']+)(';)/;
  if (versionRegex.test(versionJsContent)) {
    versionJsContent = versionJsContent.replace(versionRegex, `$1${newVersion}$3`);
    fs.writeFileSync(versionJsPath, versionJsContent, 'utf8');
  } else {
    process.stderr.write('⚠️ 无法在 version.js 中找到版本号定义，请手动更新\n');
  }
} else {
  process.stderr.write('⚠️ version.js 文件不存在，请手动创建\n');
}

// 更新 CHANGELOG.md
// 注意：这只是一个简单的示例，实际上可能需要更复杂的逻辑
const changelogPath = path.resolve(__dirname, '../../CHANGELOG.md');
if (fs.existsSync(changelogPath)) {
  let changelog = fs.readFileSync(changelogPath, 'utf8');

  // 检查是否已经有这个版本的条目
  if (changelog.includes(`## [${newVersion}]`)) {

  } else {
    // 获取当前日期
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    const dateStr = `${year}年${month}月${day}日`;

    // 创建新的版本条目
    const newEntry = `## [${newVersion}] - ${dateStr}\n\n### 更新\n- \n\n### 优化\n- \n\n### 修复\n- \n\n`;

    // 在第一个版本条目之前插入新条目
    const firstVersionIndex = changelog.indexOf('## [');
    if (firstVersionIndex !== -1) {
      changelog =
        changelog.substring(0, firstVersionIndex) +
        newEntry +
        changelog.substring(firstVersionIndex);

      fs.writeFileSync(changelogPath, changelog, 'utf8');
    } else {
      process.stderr.write('⚠️ 无法在 CHANGELOG.md 中找到版本条目，跳过更新\n');
    }
  }
} else {
  process.stderr.write('⚠️ CHANGELOG.md 文件不存在，跳过更新\n');
}
